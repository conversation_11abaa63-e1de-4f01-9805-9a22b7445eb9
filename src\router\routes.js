/**
 * Default routes - static routes that don't require authentication
 * or are used before menu routes are loaded
 */
export default [
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/login/index.vue'),
    meta: {
      title: '登录',
      public: true
    }
  },
  {
    path: '/',
    name: 'Root',
    component: () => import('../layout/DefaultLayout.vue'),
    redirect: '/dashboard',
    children: [
      {
        path: '/dashboard',
        name: 'Dashboard',
        component: () => import('../views/home/<USER>'),
        meta: {
          title: '首页',
          icon: 'dashboard',
          keepAlive: true
        }
      },
      // 系统管理相关页面
      {
        path: '/system/user',
        name: 'User',
        component: () => import('../views/system/user/index.vue'),
        meta: {
          title: '用户管理',
          icon: 'user',
          keepAlive: true
        }
      },
      {
        path: '/system/role',
        name: 'Role',
        component: () => import('../views/system/role/index.vue'),
        meta: {
          title: '角色管理',
          icon: 'tree',
          keepAlive: true
        }
      },
      {
        path: '/system/route',
        name: 'Route',
        component: () => import('../views/system/route/index.vue'),
        meta: {
          title: '路由管理',
          icon: 'lock',
          keepAlive: true
        }
      },
      {
        path: '/system/dict',
        name: 'Dict',
        component: () => import('../views/system/dict/index.vue'),
        meta: {
          title: '字典管理',
          icon: 'lock',
          keepAlive: true
        }
      },
      // 示例菜单页面
      {
        path: '/example1/list',
        name: 'example1-list',
        component: () => import('../views/example/example1/index.vue'), // 暂时指向404页面，后续可替换为实际页面
        meta: {
          title: '列表1',
          keepAlive: true
        }
      },
      {
        path: '/example1/detail',
        name: 'example1-detail',
        component: () => import('../views/error/404.vue'), // 暂时指向404页面，后续可替换为实际页面
        meta: {
          title: '列表1',
          keepAlive: true
        }
      },
      {
        path: '/example2/list',
        name: 'example2-list',
        component: () => import('../views/error/404.vue'), // 暂时指向404页面，后续可替换为实际页面
        meta: {
          title: '列表2',
          keepAlive: true
        }
      },
      {
        path: '/example3/list',
        name: 'example3-list',
        component: () => import('../views/error/404.vue'), // 暂时指向404页面，后续可替换为实际页面
        meta: {
          title: '列表3',
          keepAlive: true
        }
      },
      {
        path: '/example4/list',
        name: 'example4-list',
        component: () => import('../views/error/404.vue'), // 暂时指向404页面，后续可替换为实际页面
        meta: {
          title: '列表4',
          keepAlive: true
        }
      },
      // 关于我们页面
      {
        path: '/about',
        name: 'About',
        component: () => import('../views/error/404.vue'), // 暂时指向404页面，后续可替换为实际页面
        meta: {
          title: '关于我们',
          keepAlive: true
        }
      }
    ]
  },
  {
    path: '/404',
    name: '404',
    component: () => import('../views/error/404.vue'),
    meta: {
      title: '404 Not Found',
      public: true
    }
  }
]