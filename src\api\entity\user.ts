import type { IPage } from './index'

// 用户实体接口
export interface IUser {
  /** 用户ID */
  userId: string
  /** 创建人ID */
  createUserid: string
  /** 创建时间 */
  createDate: string
  /** 更新人ID */
  updateUserid: string
  /** 更新时间 */
  updateDate: string
  /** 删除标记 0：删除；1：有效 */
  isFlag: boolean
  /** 姓名 */
  sname: string
  /** 用户名 */
  username: string
  /** 手机号 */
  phone: string
  /** 证件类型（码表） */
  cardType: string
  /** 证件号码/身份证号 */
  cardNo: string
  /** 员工编号 */
  workCode: string
  /** 密码 */
  password: string
  /** 直接上级 */
  managerId: string
  /** 所有上级拼接字符串 */
  managerIds: string
  /** 人员所属公司 */
  belongId: string
}

// 用户分页查询参数接口
export interface IUserQuery {
  pageNumber?: number
  pageSize?: number
  sname?: string
  username?: string
  phone?: string
  workCode?: string
  [key: string]: any
}

// 用户列表接口
export interface IUserList extends IPage<IUser> {
  records: IUser[]
}