import { defineStore } from 'pinia'
import { login, getUserInfo, logout } from '../api/user'
import { getToken, setToken, clearAuth, setUserInfo, getUserInfo as getStoredUserInfo } from '../utils/auth'
import router from '../router'

export const useUserStore = defineStore('user', {
  state: () => ({
    token: getToken() || '',
    userInfo: getStoredUserInfo() || null,
    roles: [],
    permissions: []
  }),
  
  getters: {
    isLoggedIn: (state) => !!state.token,
    isAdmin: (state) => state.roles.includes('admin')
  },
  
  actions: {
    // Login action
    async login(userInfo) {
      try {
        const { username, password } = userInfo
        const response = await login({ username, password })
        
        if (response.code === 200) {
          const { token, userInfo } = response.data
          
          // Save token and user info
          this.token = token
          this.userInfo = userInfo
          this.roles = userInfo.roles || []
          this.permissions = userInfo.permissions || []
          
          // Store in localStorage
          setToken(token)
          setUserInfo(userInfo)
          
          return Promise.resolve()
        } else {
          return Promise.reject(response.message || 'Login failed')
        }
      } catch (error) {
        return Promise.reject(error)
      }
    },
    
    // Get user info
    async getUserInfo() {
      try {
        // If we already have user info, return it
        if (this.userInfo) {
          return Promise.resolve(this.userInfo)
        }
        
        // Otherwise, fetch from API
        const response = await getUserInfo()
        
        if (response.code === 200) {
          const userInfo = response.data
          this.userInfo = userInfo
          this.roles = userInfo.roles || []
          this.permissions = userInfo.permissions || []
          
          // Store in localStorage
          setUserInfo(userInfo)
          
          return Promise.resolve(userInfo)
        } else {
          return Promise.reject(response.message || 'Get user info failed')
        }
      } catch (error) {
        return Promise.reject(error)
      }
    },
    
    // Logout action
    async logout() {
      try {
        await logout()
      } catch (error) {
        console.error('Logout error:', error)
      } finally {
        // Clear all auth data
        this.resetState()
        // Redirect to login page
        router.push('/login')
      }
    },
    
    // Reset state
    resetState() {
      this.token = ''
      this.userInfo = null
      this.roles = []
      this.permissions = []
      clearAuth()
    }
  }
}) 