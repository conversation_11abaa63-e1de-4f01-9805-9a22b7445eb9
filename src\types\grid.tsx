import { VxeColumnPropTypes, VxeGridProps, VxeGridPropTypes, VxeFormItemProps, VxeFormItemPropTypes } from 'vxe-table'
import { ExtendedFormProps } from './form'

/**
 * 扩展的表格属性配置接口
 */
export interface ExtendedGridProps<D = any> extends VxeGridProps<D> {
  /** 表格列配置 */
  columns?: ExtendedColumn<D>[]
  /** 工具栏配置 */
  toolbarConfig?: VxeGridPropTypes.ToolbarConfig
  /** 表单配置 */
  formConfig?: ExtendedFormConfig
  /** 自定义工具栏配置 */
  tools?: ExtendedFormProps
}

/**
 * 表单配置接口
 */
export interface ExtendedFormConfig extends VxeGridPropTypes.FormConfig {
  items?: ExtendedFormItemProps[]
  formData?: Record<string, any>
}

/**
 * 表单项配置接口
 */
export interface ExtendedFormItemProps<D = any> extends VxeFormItemProps {
  itemRender?: ExtendedFormItemRender<D>
}

/**
 * 表单项渲染配置接口
 */
export interface ExtendedFormItemRender<D = any> extends VxeFormItemPropTypes.ItemRender {
  options?: any[]
  dict?: string
  selectApi?: selectApiConfig
  component?: any
  /** 是否显示的函数类型事件,这个是表头的字段级设置 */
  visibleEvent?: (data: D) => Boolean
}

/**
 * 扩展的表格列配置接口
 */
export interface ExtendedColumn<D = any> extends VxeGridPropTypes.Column<D> {
  cellRender?: ExtendedCellRender<D>
  cellRenders?: ExtendedCellRender<D>[]
  dict?: string
  selectApi?: selectApiConfig
  options?: any[]
  component?: any
  /** 是否显示的函数类型事件,这个是表头的字段级设置 */
  visibleEvent?: (data: D) => Boolean
}

/**
 * 单元格渲染配置接口
 */
export interface ExtendedCellRender<D = any> extends VxeColumnPropTypes.CellRender<D> {
  dict?: string
  selectApi?: selectApiConfig
  options?: any[]
  /** 是否显示的函数类型事件,这个是表头的字段级设置 */
  visibleEvent?: (data: D) => Boolean
}

/**
 * selectApi 接口对象
 */
export interface selectApiConfig {
  url?: string // 工具栏位置
  body?: Record<string,any> // 接口得请求数组
  config?: KeyConfig // 字典的key和value字段名称
}

/**
* 字段名称配置
*/
export interface KeyConfig {
  keyFieldName: string // 字典的key字段名称
  valueFieldName: string // 字典的value字段名称
  childrenFieldName?: string // 字典的children字段名称
}