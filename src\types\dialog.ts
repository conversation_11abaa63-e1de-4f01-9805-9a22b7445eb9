import { ExtendedFormProps } from './form'
import { ExtendedGridProps } from './grid'

/**
 * 对话框内容项接口
 */
export interface DialogProps {
  /** 分组标题 */
  title: string
  /** 高度 屏幕高度百分比 */
  height?: string
  /** 宽度 屏幕宽度百分比 */
  width?: string
  /** 一个JSON数组 { x: 0, y: 0, w: 4, h: 1, i: '0' } */
  layout?: Layout[]
  /** 把指定的配置给到指定的布局 */
  dialogOptions?: Map<string, ExtendedGridProps> | Map<string, ExtendedFormProps>
  /** 工具栏配置 */
  tools?: ToolItem[]
}

/**
 * 布局项接口
 */
export interface Layout {
    x: number
    y: number
    w: number
    h: number
    i: string
}

/**
 * 工具项接口
 */
export interface ToolItem {
    /** 位置: 'left', 'right', 'center' */
    position: 'left' | 'right' | 'center'
    /** 边距 */
    margin?: {
      left?: string
      right?: string
      top?: string
      bottom?: string
    }
    /** 宽度 */
    width?: string
    /** 组件名称 */
    name: string
    /** 组件属性 */
    props?: Record<string, any>
    /** 组件事件 */
    events?: {
      /** 点击事件 */
      click?: (formData: any, formRef: any) => void
      /** 其他事件 */
      [key: string]: ((...args: any[]) => void) | undefined
    }
  }
  