import { createRouter, createWebHistory } from 'vue-router'
import { getToken } from '../utils/auth'
import defaultRoutes from './routes'
import { useUserStore } from '../stores/user'
import { useMenuStore } from '../stores/menu'

// Create router instance
const router = createRouter({
  history: createWebHistory(),
  routes: defaultRoutes,
  scrollBehavior() {
    return { top: 0 }
  }
})

// Router guard
router.beforeEach(async (to, from, next) => {
  // Set page title
  document.title = to.meta.title ? `${to.meta.title} - Admin System` : 'Admin System'
  
  // Check if user is logged in
  const token = getToken()
  const userStore = useUserStore()
  const menuStore = useMenuStore()
  
  // If has token
  if (token) {
    // If going to login page, redirect to dashboard
    if (to.path === '/login') {
      next({ path: '/dashboard' })
      return
    }
    
    // If user info not loaded, get user info
    if (!userStore.userInfo) {
      try {
        await userStore.getUserInfo()
      } catch (error) {
        // If error, clear token and go to login
        userStore.resetState()
        next(`/login?redirect=${to.path}`)
        return
      }
    }
    
    // If menu not loaded, get menu list
    if (!menuStore.menuLoaded) {
      try {
        await menuStore.getMenuList()
        
        // Generate dynamic routes from menu
        const dynamicRoutes = menuStore.routerMenus
        
        // Add dynamic routes
        dynamicRoutes.forEach(route => {
          if (!router.hasRoute(route.name)) {
            router.addRoute(route)
          }
        })
        
        // Add catch-all 404 route
        if (!router.hasRoute('NotFound')) {
          router.addRoute({
            path: '/:pathMatch(.*)*',
            name: 'NotFound',
            component: () => import('../views/error/404.vue'),
            meta: { title: '404 Not Found' }
          })
        }
        
        // Redirect to the original route
        next({ ...to, replace: true })
        return
        
      } catch (error) {
        console.error('Failed to load menu:', error)
        next()
        return
      }
    }
    
    // Set active menu
    menuStore.setActiveMenu(to.path)
    next()
  } else {
    // No token
    
    // Allow access to login and public routes
    if (to.meta.public) {
      next()
    } else {
      // Redirect to login
      next(`/login?redirect=${to.path}`)
    }
  }
})

export default router 