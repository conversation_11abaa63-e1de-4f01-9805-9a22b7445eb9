// Mock menu list
window.__MOCK_MENU_LIST__ = () => {
  return {
    code: 200,
    message: 'Success',
    data: [
      {
        id: 1,
        name: 'Dashboard',
        path: '/dashboard',
        component: 'views/home/<USER>',
        meta: {
          title: '首页',
          icon: 'dashboard',
          keepAlive: true
        }
      },
      {
        id: 2,
        name: 'System',
        path: '/system',
        component: 'layout/DefaultLayout',
        redirect: '/system/user',
        meta: {
          title: '系统管理',
          icon: 'setting'
        },
        children: [
          {
            id: 21,
            name: 'User',
            path: '/system/user',
            component: 'views/system/user/index',
            meta: {
              title: '用户管理',
              icon: 'user',
              keepAlive: true
            }
          },
          {
            id: 22,
            name: 'Role',
            path: '/system/role',
            component: 'views/system/role/index',
            meta: {
              title: '角色管理',
              icon: 'tree',
              keepAlive: true
            }
          },
          {
            id: 23,
            name: 'Route',
            path: '/system/route',
            component: 'views/system/route/index',
            meta: {
              title: '路由管理',
              icon: 'lock',
              keepAlive: true
            }
          },
          {
            id: 24,
            name: 'Dict',
            path: '/system/dict',
            component: 'views/system/dict/index',
            meta: {
              title: '字典管理',
              icon: 'lock',
              keepAlive: true
            }
          }
        ]
      }
    ]
  }
} 