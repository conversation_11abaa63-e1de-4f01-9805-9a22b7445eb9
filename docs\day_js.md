\# 安装
=====

 ![包图网] Day.js 可以运行在浏览器和 Node.js 中。

 本文档所有代码都可以在这两种环境中正常运行，所有单元测试也都在这两个环境下完成。

 CI 系统测试覆盖的浏览器有：Chrome on Windows XP, IE 8, 9, and 10 on Windows 7, IE 11 on Windows 10, latest Firefox on Linux, and latest Safari on OSX 10\.8 and 10\.11。

 打开您的浏览器控制台，即可输入测试示例代码。

 \# Node.js
----------

 要在您的 Node.js 项目中使用 Day.js，只需使用 npm  (opens new window) 安装

 
```
> npm install dayjs

```
或 cnpm  (opens new window) 安装

 
```
> cnpm install dayjs -S

```
或 yarn  (opens new window) 安装

 
```
> yarn add dayjs

```
或 pnpm  (opens new window) 安装

 
```
> pnpm add dayjs

```
然后在项目代码中引入即可：

 
```
var dayjs = require('dayjs')
// import dayjs from 'dayjs' // ES 2015
dayjs.format

```
查看这里了解更多关于加载 多语言 和 插件 的信息。

 \# 浏览器
------

 
```
<script src=""></script>
<script>
  dayjs.format
</script>

```
CDN引入（网络不稳定的时候，可能需要翻墙）

 注意

 Day.js可以通过CDN提供商，如：cdnjs.com  (opens new window), unpkg  (opens new window)，jsdelivr  (opens new window)和bootcdn.cn  (opens new window)等引入

 
```
<script src=""></script>
<script>dayjs.format</script>

```
友情提示

 F12 打开控制台，选择 网络(Network) \-\> js 也可以找到 Day.js 的CDN引入地址。

  \# 微信小程序
--------

 ### \# 方式1

 下载 `dayjs.min.js` 放到小程序 `lib` 目录下（没有新建或用其他目录）

 引入示例：

 
```
const dayjs = require('../../libs/dayjs.min.js');

```
### \# 方式2

 使用 `npm` 安装

 
```
> npm install dayjs --save

```
引入示例：

 
```
const dayjs = require("dayjs");

```
\# Element\-plus
----------------

 Element\-plus  (opens new window) 组件库默认支持 dayjs 进行日期时间处理，所以可以直接导入使用，相关 Date Picker  (opens new window) 组件介绍。

 
```
// element-plus 集成的 dayjs 默认也安装了 dayjs 插件，所以相关插件可以直接使用
import { dayjs } from 'element-plus'

// 扩展插件
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore'
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter'
dayjs.extend(isSameOrBefore)
dayjs.extend(isSameOrAfter)

dayjs.isSameOrBefore(dayjs('2011-01-01'))

```
\# Typescript
-------------

 在 NPM 包中已经包含 Day.js 的 TypeScript 类型定义文件。

 通过 NPM 安装

 
```
> npm install dayjs --save

```
在 TypeScript 项目中导入并使用

 
```
import * as dayjs from 'dayjs'
dayjs.format

```
如果您的 `tsconfig.json` 包含以下配置，您必须使用 `import dayjs from 'dayjs'` 的 default import 模式：

 
```
{ //tsconfig.json
  "compilerOptions": {
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
  }
}

```
如果您没有上述配置，default import 将无法正常工作。 您需要使用 `import * as dayjs from 'dayjs'`

 
```
import * as dayjs from 'dayjs'

```
### \# 导入本地化语言和插件

 在使用本地化语言和插件，您首先需要导入它们。

 
```
import * as dayjs from 'dayjs'
import * as isLeapYear from 'dayjs/plugin/isLeapYear' // 导入插件
import 'dayjs/locale/zh-cn' // 导入本地化语言

dayjs.extend(isLeapYear) // 使用插件
dayjs.locale('zh-cn') // 使用本地化语言

```
\# 下载
-----

 访问   (opens new window) 下载最新版本的 Day.js。

 访问   (opens new window) 查看 Day.js 的已发布版本和源代码



\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-

\# 解析
=====

 ![包图网] \# 实例
-----

 代替修改本地`Date.prototype`，Day.js对`Date`对象进行了封装，只需要调用`Dayjs`即可

 Day.js对象是不可变的，也就是说，以某种方式改变Day.js对象的所有API操作都将返回它的一个新实例。

 \# 当前时间
-------

 直接调用 `dayjs` 将返回一个包含当前日期和时间的 Day.js 对象。

 
```
var now = dayjs

```
等同于 `dayjs(new Date)` 的调用。

 当没有传入参数时，参数默认值是 undefined，所以调用 `dayjs(undefined)` 就相当于调用 `dayjs`。

 Day.js 将 `dayjs(null)` 视为无效的输入。

 \# 字符串
------

 解析传入的 ISO 8601  (opens new window) 格式的字符串并返回一个 Day.js 对象实例。

 
```
dayjs('2018-04-04T16:00:00.000Z')

```
注意

 为了保证结果一致，当解析除了 ISO 8601 格式以外的字符串时，您应该使用 String \+ Format。

 \# 字符串\+格式
----------

 如果知道输入字符串的格式，您可以用它来解析日期。

 注意

 此功能依赖 CustomParseFormat 插件

 
```
dayjs.extend(customParseFormat)
dayjs("12-25-1995", "MM-DD-YYYY")

```
如果想解析包含本地化语言的日期字符串，可以传入第三个参数。

 
```
require('dayjs/locale/zh-cn')
dayjs('2018 三月 15', 'YYYY MMMM DD', 'zh-cn')

```
最后一个参数可传入布尔值来启用严格解析模式。 严格解析要求格式和输入内容完全匹配，包括分隔符。

 
```
dayjs('1970-00-00', 'YYYY-MM-DD').isValid // true
dayjs('1970-00-00', 'YYYY-MM-DD', true).isValid // false
dayjs('1970-00-00', 'YYYY-MM-DD', 'es', true).isValid // false

```
如果您不知道输入字符串的确切格式，但知道它可能是几种中的一种，可以使用数组传入多个格式。

 
```
dayjs("12-25-2001", ["YYYY", "YYYY-MM-DD"], 'es', true);

```
支持的解析占位符列表：

 

| 输入 | 示例 | 描述 |
| --- | --- | --- |
| YY | 18 | 两位数的年份 |
| YYYY | 2018 | 四位数的年份 |
| M | 1\-12 | 月份，从 1 开始 |
| MM | 01\-12 | 月份，两位数 |
| MMM | Jan\-Dec | 缩写的月份名称 |
| MMMM | January\-December | 完整的月份名称 |
| D | 1\-31 | 月份里的一天 |
| DD | 01\-31 | 月份里的一天，两位数 |
| H | 0\-23 | 小时 |
| HH | 00\-23 | 小时，两位数 |
| h | 1\-12 | 小时, 12 小时制 |
| hh | 01\-12 | 小时, 12 小时制, 两位数 |
| m | 0\-59 | 分钟 |
| mm | 00\-59 | 分钟，两位数 |
| s | 0\-59 | 秒 |
| ss | 00\-59 | 秒，两位数 |
| S | 0\-9 | 毫秒，一位数 |
| SS | 00\-99 | 毫秒，两位数 |
| SSS | 000\-999 | 毫秒，三位数 |
| Z | \-05:00 | UTC 的偏移量 |
| ZZ | \-0500 | UTC 的偏移量，两位数 |
| A | AM / PM | 上午 下午 大写 |
| a | am / pm | 上午 下午 小写 |
| Do | 1st... 31st | 带序数词的月份里的一天 |
| X | 1410715640\.579 | Unix 时间戳 |
| x | 1410715640579 | Unix 时间戳 |

  \# Unix 时间戳 (毫秒)
----------------

 解析传入的一个 Unix 时间戳 (13 位数字，从1970年1月1日 UTC 午夜开始所经过的毫秒数) 创建一个 Day.js 对象。

 
```
dayjs(1318781876406)

```
注意

 传递的参数必须是一个数字

 \# Unix 时间戳 (秒)
---------------

 解析传入的一个 Unix 时间戳 (10 位数字，从1970年1月1日 Utc 午夜开始所经过的秒数) 创建一个 Day.js 对象。

 
```
dayjs.unix(1318781876)

```
这个方法是用 `dayjs( timestamp * 1000)` 实现的，所以传入时间戳里的小数点后面的秒也会被解析。

 
```
dayjs.unix(1318781876.721)

```
\# Date 对象
----------

 使用原生 Javascript `Date` 对象创建一个 Day.js 对象。

 
```
var d = new Date(2018, 8, 18)
var day = dayjs(d)

```
这将克隆 `Date` 对象。 对传入的 `Date` 对象做进一步更改不会影响 Day.js 对象，反之亦然。

 \# 对象
-----

 您可以传入包含单位和数值的一个对象来创建 Dayjs 对象。

 注意

 此功能依赖 ObjectSupport 插件，才能正常运行

 
```
dayjs.extend(objectSupport)
dayjs({ hour:15, minute:10 });
dayjs.utc({ y:2010, M:3, d:5, h:15, m:10, s:3, ms: 123});
dayjs({ year :2010, month :3, day :5, hour :15, minute :10, second :3, millisecond :123});
dayjs({ years:2010, months:3, date:5, hours:15, minutes:10, seconds:3, milliseconds:123});

```
`day` 和 `date` 都表示月份里的日期。

 `dayjs({})` 返回当前时间。

 注意

 类似 `new Date(year, month, date)`，月份从 0 开始计算。

 \# 数组
-----

 您可以传入一个数组来创建一个 Dayjs 对象，数组和结构和 `new Date` 十分类似。

 注意

 此功能依赖 ArraySupport 插件，才能正常运行

 
```
dayjs.extend(arraySupport)
dayjs([2010, 1, 14, 15, 25, 50, 125]); // February 14th, 3:25:50.125 PM
dayjs.utc([2010, 1, 14, 15, 25, 50, 125]);
dayjs([2010]);        // January 1st
dayjs([2010, 6]);     // July 1st
dayjs([2010, 6, 10]); // July 10th

```
`dayjs({})` 返回当前时间。

 注意

 类似 `new Date(year, month, date)`, 月份从 0 开始计算。

 \# UTC
------

 
> 世界调整时间（Universal Time Coordinated）

 默认情况下，Day.js 会把时间解析成本地时间。

 如果想使用 UTC 时间，您可以调用 `dayjs.utc` 而不是 `dayjs`。

 在 UTC 模式下，所有显示方法将会显示 UTC 时间而非本地时间。

 注意

 此功能依赖 UTC 插件，才能正常运行

 
```
dayjs.extend(utc)

// 默认是当地时间
dayjs.format //2019-03-06T08:00:00+08:00
// UTC 时间
dayjs.utc.format // 2019-03-06T00:00:00Z
此外，在 UTC 模式下， 所有 getters 和 setters 将使用 Date#getUTC* 和 Date#setUTC* 方法而不是 Date#get* 和 Date#set* 方法。

dayjs.utc.seconds(30).valueOf// => new Date.setUTCSeconds(30)
dayjs.utc.seconds// => new Date.getUTCSeconds

```
此外，在 UTC 模式下， 所有 getters 和 setters 将使用 `Date#getUTC*` 和 `Date#setUTC*` 方法而不是 `Date#get*` 和 `Date#set*` 方法。

 
```
dayjs.utc.seconds(30).valueOf// => new Date.setUTCSeconds(30)
dayjs.utc.seconds// => new Date.getUTCSeconds

```
要在本地时间和 UTC 时间之间切换，您可以使用 `dayjs#utc` 或 `dayjs#local`。

 \# Dayjs 复制
-----------

 所有的 Day.js 对象都是不可变的。 但如果有必要，使用 `dayjs#clone` 可以复制出一个当前对象。

 
```
var a = dayjs
var b = a.clone
// a 和 b 是两个独立的 Day.js 对象

```
在 `dayjs` 里传入一个 Day.js 对象也会返回一个复制的对象。

 
```
var a = dayjs
var b = dayjs(a)

```
\# 验证
-----

 返回 布尔值 表示 `Dayjs` 的日期是否通过校验。

 
```
dayjs.isValid

```
### \# 不严格的校验

 只检查传入的值能否被解析成一个时间日期

 
```
dayjs('2022-01-33').isValid;
// true, parsed to 2022-02-02
dayjs('some invalid string').isValid;
// false

```
### \# 严格校验

 检查传入的值能否被解析，且是否是一个有意义的日期。 最后两个参数 `format` 和 `strict` 必须提供。

 注意

 此功能依赖 CustomParseFormat 插件，才能正常运行

 
```
dayjs('2022-02-31', 'YYYY-MM-DD', true).isValid;
// false

```


\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-

\# 取值/赋值
========

 ![包图网] 在设计上 Day.js 的 `getter` 和 `setter` 使用了相同的 API，也就是说，不传参数调用方法即为 `getter`，调用并传入参数为 `setter`。

 这些 API 调用了对应原生 `Date` 对象的方法。

 
```
dayjs.second(30).valueOf // => new Date.setSeconds(30)
dayjs.second // => new Date.getSeconds

```
如果您处于 UTC 模式，将会调用对应的 UTC 方法。

 
```
dayjs.utc.seconds(30).valueOf// => new Date.setUTCSeconds(30)
dayjs.utc.seconds// => new Date.getUTCSeconds

```
\# 毫秒
-----

 获取或设置毫秒。

 传入0到999的数字。 如果超出这个范围，它会进位到秒。

 
```
dayjs.millisecond
dayjs.millisecond(1)

```
\# 秒
----

 获取或设置秒。

 传入0到59的数字。 如果超出这个范围，它会进位到分钟。

 
```
dayjs.second
dayjs.second(1)

```
\# 分钟
-----

 获取或设置分钟。

 传入0到59的数字。 如果超出这个范围，它会进位到小时。

 
```
dayjs.minute
dayjs.minute(59)

```
\# 小时
-----

 获取或设置小时。

 传入0到23的数字。 如果超出这个范围，它会进位到天数。

 
```
dayjs.hour
dayjs.hour(12)

```
 \# 日期
-----

 获取或设置月份里的日期。

 接受1到31的数字。 如果超出这个范围，它会进位到月份。

 
```
dayjs.date
dayjs.date(1)

```
注意

 `dayjs#date` 是该月的日期。 `dayjs#day` 是星期几。

 \# 星期
-----

 获取或设置星期几。

 传入 number 从0(星期天)到6(星期六)。 如果超出这个范围，它会进位到其他周。

 
```
dayjs.day
dayjs.day(0)

```
注意

 `dayjs#date` 是该月的日期。 `dayjs#day` 是星期几。

 \# 本地化星期
--------

 根据本地化配置获取或设置星期几。

 注意

 此功能依赖 Weekday 插件，才能正常运行

 如果本地化配置了星期天为一周的第一天， `dayjs.weekday(0)` 将返回星期天。 如果星期一是一周的第一天， `dayjs.weekday(0)` 将返回星期一。

 
```
dayjs.extend(weekday)

// 当星期天是一周的第一天
dayjs.weekday(-7); // last Sunday
dayjs.weekday(7); // next Sunday

// 当星期一是一周的第一天
dayjs.weekday(-7) // last Monday
dayjs.weekday(7) // next Monday


// 当星期天是一周的第一天
dayjs.weekday(-5) // last Tuesday (5th day before Sunday)
dayjs.weekday(5) // next Friday (5th day after Sunday)

```
\# ISO星期
--------

 获取或设置 ISO 星期几  (opens new window) ，其中 1 是星期一、7 是星期日。

 注意

 此功能依赖 IsoWeek 插件，才能正常运行

 
```
dayjs.extend(isoWeek)

dayjs.isoWeekday
dayjs.isoWeekday(1); // Monday

```
\# 年\-日期
--------

 获取或设置年份里第几天。

 传入1到366的数字。

 如果超出这个范围，它会进位到下一年。

 注意

 此功能依赖 DayOfYear 插件

 
```
dayjs.extend(dayOfYear)

dayjs('2010-01-01').dayOfYear // 1
dayjs('2010-01-01').dayOfYear(365) // 2010-12-31

```
\# 年\-周
-------

 获取或设置该年的第几周。

 注意

 此功能依赖 WeekOfYear 插件

 
```
dayjs.extend(weekOfYear)

dayjs('2018-06-27').week // 26
dayjs('2018-06-27').week(5) // 设置周

```
注意

 `week` 函数是特定于区域设置的，因此应该在之前导入区域设置。

 
```
import "dayjs/locale/zh-cn";
dayjs.locale("zh-cn");

dayjs("2022-8-8").week(1).format("YYYY-MM-DD"); // 2022-01-03
dayjs("2022-8-9").week(1).format("YYYY-MM-DD"); // 2022-01-04

```
\# 年\-周(ISO)
------------

 获取或设置年份的 ISO 星期  (opens new window)。

 注意

 此功能依赖 IsoWeek 插件

 
```
dayjs.extend(isoWeek)

dayjs.isoWeek
dayjs.isoWeek(2)

```
\# 月
----

 获取或设置月份。

 传入0到11的 number。 如果超出这个范围，它会进位到年份。

 
```
dayjs.month
dayjs.month(0)

```
注意

 月份是从 0 开始计算的，即 1 月是 0。

 \# 季度
-----

 获取或设置季度。

 注意

 此功能依赖 QuarterOfYear 插件

 
```
dayjs.extend(quarterOfYear)

dayjs('2010-04-01').quarter // 2
dayjs('2010-04-01').quarter(2)

```
\# 年
----

 获取或设置年份。

 
```
dayjs.year
dayjs.year(2000)

```
\# 周年
-----

 获取基于当前语言配置的按周计算的年份。

 注意

 此功能依赖 WeekYear 插件

 
```
dayjs.extend(weekYear)
dayjs.extend(weekOfYear)

dayjs.weekYear

```
\# 周年(ISO)
----------

 获取 ISO 周年  (opens new window)。

 注意

 此功能依赖 IsoWeek 插件

 
```
dayjs.extend(isoWeek)

dayjs.isoWeekYear

```
\# 年周数(ISO)
-----------

 获取当前年份的周数，根据 ISO weeks  (opens new window) 的定义。

 注意

 此功能依赖 IsoWeeksInYear 插件

 
```
dayjs.extend(isoWeeksInYear)
dayjs.extend(isLeapYear)

dayjs('2004-01-01').isoWeeksInYear // 53
dayjs('2005-01-01').isoWeeksInYear // 52

```
\# Get
------

 从 Day.js 对象中获取相应信息的 getter。

 可以理解为：

 
```
dayjs.get(unit) === dayjs[unit]

```
各个传入的单位对大小写不敏感，支持缩写和复数。 请注意，缩写是区分大小写的。

 
```
dayjs.get('year')
dayjs.get('month') // start 0
dayjs.get('date')
dayjs.get('hour')
dayjs.get('minute')
dayjs.get('second')
dayjs.get('millisecond')

```
支持的单位列表：

 

| 单位 | 缩写 | 描述 |
| --- | --- | --- |
| date | D | 日期 |
| day | d | 星期(星期日0，星期六6\) |
| month | M | 月份(0\-11\) |
| year | y | 年 |
| hour | h | 小时 |
| minute | m | 分钟 |
| second | s | 秒 |
| millisecond | ms | 毫秒 |

 \# Set
------

 通用的 setter，两个参数分别是要更新的单位和数值，调用后会返回一个修改后的新实例。

 可以理解为：

 
```
dayjs.set(unit, value) === dayjs[unit](value)

```

```
dayjs.set('date', 1)
dayjs.set('month', 3) // 四月
dayjs.set('second', 30)

```
也支持这样的链式调用：

 
```
dayjs.set('hour', 5).set('minute', 55).set('second', 15)

```
各个传入的单位对大小写不敏感，支持缩写和复数。

 支持的单位列表

 \# 最大值
------

 返回传入的 Day.js 实例中的最大的 (即最靠近未来的)。 它接受传入多个 Day.js实例或一个数组。

 注意

 此功能依赖 MinMax 插件

 
```
dayjs.extend(minMax)

dayjs.max(dayjs, dayjs('2018-01-01'), dayjs('2019-01-01'))
dayjs.max([dayjs, dayjs('2018-01-01'), dayjs('2019-01-01')])

```
\# 最小值
------

 返回传入的 Day.js 实例中的最小的 (即最靠近过去的)。 它接受传入多个 Day.js实例或一个数组。

 注意

 此功能依赖 MinMax 插件

 
```
dayjs.extend(minMax)

dayjs.min(dayjs, dayjs('2018-01-01'), dayjs('2019-01-01'))
dayjs.min([dayjs, dayjs('2018-01-01'), dayjs('2019-01-01')])

```


\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-

\# 操作
=====

 ![包图网] 您可能需要一些方法来操作 Day.js 对象。

 Day.js 支持像这样的链式调用：

 
```
dayjs('2019-01-25').add(1, 'day').subtract(1, 'year').year(2009).toString

```
 \# 增加
-----

 返回增加一定时间的复制的 Day.js 对象。

 
```
dayjs.add(7, 'day')

```
各个传入的单位对大小写不敏感，支持缩写和复数。 请注意，缩写是区分大小写的。

 支持的单位列表：

 

| 单位 | 缩写 | 描述 |
| --- | --- | --- |
| day | d | 日 |
| week | w | 周 |
| month | M | 月份(0\-11\) |
| quarter | Q | 季度，依赖 `QuarterOfYear` 插件 |
| year | y | 年 |
| hour | h | 小时 |
| minute | m | 分钟 |
| second | s | 秒 |
| millisecond | ms | 毫秒 |

 或者，也可以给 Day.js 对象增加一个 持续时间 。

 \# 减去
-----

 返回减去一定时间的复制的 Day.js 对象。

 
```
dayjs.subtract(7, 'year')

```
各个传入的单位对大小写不敏感，支持缩写和复数。

  \# 时间的开始
--------

 返回复制的 Day.js 对象，并设置到一个时间的开始。

 
```
dayjs.startOf('year')

```
各个传入的单位对大小写不敏感，支持缩写和复数。

 支持的单位列表：

 

| 单位 | 缩写 | 描述 |
| --- | --- | --- |
| date | D | 当天 00:00 |
| day | d | 当天 00:00 |
| month | M | 本月1日上午 00:00 |
| quarter | Q | 本季度第一个月1日上午 00:00，依赖 `QuarterOfYear` 插件 |
| year | y | 今年一月1日上午 00:00 |
| week | w | 本周的第一天上午 00:00 |
| isoWeek |  | 本周的第一天上午 00:00 (根据 ISO 8601\) ， ( 依赖 `IsoWeek` 插件 ) |
| hour | h | 当前时间，0 分、0 秒、0 毫秒 |
| minute | m | 当前时间，0 秒、0 毫秒 |
| second | s | 当前时间，0 毫秒 |

 \# 时间的结束
--------

 返回复制的 Day.js 对象，并设置到一个时间的末尾。

 
```
dayjs.endOf('month')

```
各个传入的单位对大小写不敏感，支持缩写和复数。

 \# 当前时区
-------

 返回一个在当前时区模式下的 Day.js 对象。

 注意

 此功能依赖 UTC 插件

 
```
dayjs.extend(utc)

var a = dayjs.utc
a.format // 2019-03-06T00:00:00Z
a.local.format //2019-03-06T08:00:00+08:00

```
了解更多关于 UTC 模式 的信息。

 \# UTC
------

 返回一个在 UTC 模式下的 Day.js 对象。

 注意

 此功能依赖 UTC 插件

 
```
dayjs.extend(utc)

var a = dayjs
a.format //2019-03-06T08:00:00+08:00
a.utc.format // 2019-03-06T00:00:00Z

```
传入 true 将只改变 UTC 模式而不改变本地时间。

 
```
dayjs('2016-05-03 22:15:01').utc(true).format 
// 2016-05-03T22:15:01Z

```
\# UTC偏移量
---------

 获取 UTC 偏移量 (分钟)。

 
```
dayjs.utcOffset

```
也可以传入分钟来得到一个更改 UTC 偏移量的新实例。 请注意，一旦您设置了 UTC 偏移量，它将保持固定，不会自动改变 (即没有DST夏令时变更)。

 注意

 此功能依赖 UTC 插件

 
```
dayjs.extend(utc)

dayjs.utcOffset(120)

```
如果输入在\-16到16之间，会将您的输入理解为小时数而非分钟。

 
```
// 以下两种写法是等效的
dayjs.utcOffset(8)  // 设置小时偏移量
dayjs.utcOffset(480)  // 设置分钟偏移量 (8 * 60)

```
第二个参数传入 true 可以只改变偏移量而保持本地时间不变。

 
```
dayjs.utc('2000-01-01T06:01:02Z').utcOffset(1, true).format 
// 2000-01-01T06:01:02+01:00

```


\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-

\# 显示
=====

 ![包图网] 当解析和操作完成后，您需要一些方式来展示 Day.js 对象。

 \# 格式化
------

 根据传入的占位符返回格式化后的日期。

 将字符放在方括号中，即可原样返回而不被格式化替换 (例如， \[`MM`])。

 
```
dayjs.format 
// 默认返回的是 ISO8601 格式字符串 '2020-04-02T08:02:17-05:00'

dayjs('2019-01-25').format('[YYYYescape] YYYY-MM-DDTHH:mm:ssZ[Z]') 
// 'YYYYescape 2019-01-25T00:00:00-02:00Z'

dayjs('2019-01-25').format('DD/MM/YYYY') // '25/01/2019'

```
 支持的格式化占位符列表：

 

| 标识 | 示例 | 描述 |
| --- | --- | --- |
| YY | 18 | 年，两位数 |
| YYYY | 2018 | 年，四位数 |
| M | 1\-12 | 月，从1开始 |
| MM | 01\-12 | 月，两位数 |
| MMM | Jan\-Dec | 月，英文缩写 |
| MMMM | January\-December | 月，英文全称 |
| D | 1\-31 | 日 |
| DD | 01\-31 | 日，两位数 |
| d | 0\-6 | 一周中的一天，星期天是 0 |
| dd | Su\-Sa | 最简写的星期几 |
| ddd | Sun\-Sat | 简写的星期几 |
| dddd | Sunday\-Saturday | 星期几，英文全称 |
| H | 0\-23 | 小时 |
| HH | 00\-23 | 小时，两位数 |
| h | 1\-12 | 小时, 12 小时制 |
| hh | 01\-12 | 小时, 12 小时制, 两位数 |
| m | 0\-59 | 分钟 |
| mm | 00\-59 | 分钟，两位数 |
| s | 0\-59 | 秒 |
| ss | 00\-59 | 秒，两位数 |
| S | 0\-9 | 毫秒（十），一位数 |
| SS | 00\-99 | 毫秒（百），两位数 |
| SSS | 000\-999 | 毫秒，三位数 |
| Z | \-05:00 | UTC 的偏移量，±HH:mm |
| ZZ | \-0500 | UTC 的偏移量，±HHmm |
| A | AM / PM | 上/下午，大写 |
| a | am / pm | 上/下午，小写 |
| Do | 1st... 31st | 月份的日期与序号 |
| ... | ... | 其他格式 ( 依赖 AdvancedFormat 插件 ) |

 **本地化格式**

 在不同的本地化配置下，有一些不同的本地化格式可以使用。

 注意

 此功能依赖 LocalizedFormat 插件

 
```
dayjs.extend(LocalizedFormat)
dayjs.format('L LT')

```
支持的本地化格式列表：

 

| 占位符 | 英语语言 | 输出结果 |
| --- | --- | --- |
| LT | h:mm A | 8:02 PM |
| LTS | h:mm:ss A | 8:02:18 PM |
| L | MM/DD/YYYY | 08/16/2018 |
| LL | MMMM D, YYYY | August 16, 2018 |
| LLL | MMMM D, YYYY h:mm A | August 16, 2018 8:02 PM |
| LLLL | dddd, MMMM D, YYYY h:mm A | Thursday, August 16, 2018 8:02 PM |
| l | M/D/YYYY | 8/16/2018 |
| ll | MMM D, YYYY | Aug 16, 2018 |
| lll | MMM D, YYYY h:mm A | Aug 16, 2018 8:02 PM |
| llll | ddd, MMM D, YYYY h:mm A | Thu, Aug 16, 2018 8:02 PM |

 \# 相对当前时间（前）
------------

 返回现在到当前实例的相对时间。

 注意

 此功能依赖 RelativeTime 插件

 
```
dayjs.extend(relativeTime)

dayjs('1999-01-01').fromNow // 22 年前

```
如果传入 true，则可以获得不带后缀的值。

 
```
dayjs.extend(relativeTime)

dayjs('1999-01-01').fromNow(true) // 22 年

```
时间范围划分标准：

 表格里的值是由语言配置决定的，并且 可以自定义输出内容。 时间会舍入到最接近的秒数。

 

| 范围 | 键值 | 输出结果 |
| --- | --- | --- |
| 0 to 44 seconds | s | 几秒前 |
| 45 to 89 seconds | m | 1 分钟前 |
| 90 seconds to 44 minutes | mm | 2 分钟前 ... 44 分钟前 |
| 45 to 89 minutes | h | 1 小时前 |
| 90 minutes to 21 hours | hh | 2 小时前 ... 21 小时前 |
| 22 to 35 hours | d | 1 天前 |
| 36 hours to 25 days | dd | 2 天前 ... 25 天前 |
| 26 to 45 days | M | 1 个月前 |
| 46 days to 10 months | MM | 2 个月前 ... 10 个月前 |
| 11 months to 17months | y | 1 年前 |
| 18 months\+ | yy | 2 年前 ... 20 年前 |

  \# 相对指定时间（前）
------------

 返回 X 到当前实例的相对时间。

 注意

 此功能依赖 RelativeTime 插件

 
```
dayjs.extend(relativeTime)

var a = dayjs('2000-01-01')

dayjs('1999-01-01').from(a) // 1 年前

```
如果传入 true，则可以获得不带后缀的值。

 
```
dayjs.extend(relativeTime)

var a = dayjs('2000-01-01')

dayjs('1999-01-01').from(a, true) // 1 年

```
\# 相对当前时间（后）
------------

 返回当前实例到现在的相对时间。

 注意

 此功能依赖 RelativeTime 插件

 
```
dayjs.extend(relativeTime)

dayjs('1999-01-01').toNow // 22 年后

```
如果传入 true，则可以获得不带后缀的值。

 
```
dayjs.extend(relativeTime)

dayjs('1999-01-01').toNow(true) // 22 年

```
\# 相对指定时间（后）
------------

 返回当前实例到 X 的相对时间。

 注意

 此功能依赖 RelativeTime 插件

 
```
dayjs.extend(relativeTime)

var a = dayjs('2000-01-01')

dayjs('1999-01-01').to(a) // 1 年后

```
如果传入 true，则可以获得不带后缀的值。

 
```
dayjs.extend(relativeTime)

var a = dayjs('2000-01-01')

dayjs('1999-01-01').to(a, true) // 1 年

```
\# 日历时间
-------

 日历时间显示了距离给定时间 (默认为现在) 的相对时间，但与 `dayjs#fromnow` 略有不同。

 注意

 此功能依赖 Calendar 插件

 
```
dayjs.extend(calendar)

dayjs.calendar
dayjs.calendar(dayjs('2008-01-01'))

```


| 键 | 值 |
| --- | --- |
| 上个星期 (lastWeek) | 上星期一 2:30 |
| 前一天 (lastDay) | 昨天 2:30 |
| 同一天 (sameDay) | 今天 2:30 |
| 下一天 (nextDay) | 明天 2:30 |
| 下个星期 (nextWeek) | 星期日 2:30 |
| 其他 (sameElse) | 7/10/2011 |

 表格里的值是由语言配置决定的，并且 可以自定义输出内容。

 您也可以通过第二个参数传入指定日历输出格式。

 将字符放在方括号中，即可原样返回而不被格式化替换 (例如， \[`Today`])。

 
```
dayjs.calendar(null, {
  sameDay: '[Today at] h:mm A', // The same day ( Today at 2:30 AM )
  nextDay: '[Tomorrow]', // The next day ( Tomorrow at 2:30 AM )
  nextWeek: 'dddd', // The next week ( Sunday at 2:30 AM )
  lastDay: '[Yesterday]', // The day before ( Yesterday at 2:30 AM )
  lastWeek: '[Last] dddd', // Last week ( Last Monday at 2:30 AM )
  sameElse: 'DD/MM/YYYY' // Everything else ( 7/10/2011 )
})

```
\# 差异（Diff）
-----------

 返回指定单位下两个日期时间之间的差异。

 要获得以毫秒为单位的差异，请使用 `dayjs#diff`。

 
```
const date1 = dayjs('2019-01-25')
const date2 = dayjs('2018-06-05')
date1.diff(date2) // 20214000000 默认单位是毫秒

```
要获取其他单位下的差异，则在第二个参数传入相应的单位。

 
```
const date1 = dayjs('2019-01-25')
date1.diff('2018-06-05', 'month') // 7

```
默认情况下 `dayjs#diff` 会将结果进位成整数。 如果要得到一个浮点数，将 `true` 作为第三个参数传入。

 
```
const date1 = dayjs('2019-01-25')
date1.diff('2018-06-05', 'month', true) // 7.645161290322581

```
时间戳（距今多少天）

 
```
dayjs.diff(dayjs(1718258944185), 'day')

```
支持的单位列表：

 各个传入的单位对大小写不敏感，支持缩写和复数。 请注意，缩写是区分大小写的。

 

| 单位 | 缩写 | 描述 |
| --- | --- | --- |
| week | w | 周（Week of Year） |
| day | d | 日 |
| month | M | 月份 (一月 0， 十二月 11\) |
| quarter | Q | 季度，依赖 `QuarterOfYear` 插件 |
| year | y | 年 |
| hour | h | 小时 |
| minute | m | 分钟 |
| second | s | 秒 |
| millisecond | ms | 毫秒 |

 \# Unix时间戳(毫秒)
--------------

 返回当前实例的 UNIX 时间戳，13位数字，毫秒

 
```
dayjs('2019-01-25').valueOf // 1548381600000
+dayjs(1548381600000) // 1548381600000

```
您应该使用 `Unix Timestamp` 来获取 UNIX 时间戳(10位 秒)

 \# Unix时间戳
----------

 返回当前实例的 UNIX 时间戳，10位数字，秒。

 
```
dayjs('2019-01-25').unix // 1548381600

```
此值不包含毫秒信息，会进位到秒。

 \# 获取月天数
--------

 获取当前月份包含的天数。

 
```
dayjs('2019-01-25').daysInMonth // 31

```
\# 转Date
--------

 调用 `dayjs#toDate` 从 Day.js 对象中获取原生的 Date 对象

 
```
dayjs('2019-01-25').toDate

```
\# 转数组
------

 返回一个包含各个时间信息的 Array 。

 注意

 此功能依赖 ToArray 插件

 
```
dayjs.extend(toArray)

dayjs('2019-01-25').toArray // [ 2019, 0, 25, 0, 0, 0, 0 ]

```
\# 转JSON
--------

 序列化为 ISO 8601 格式的字符串。

 
```
dayjs('2019-01-25').toJSON // '2019-01-25T02:00:00.000Z'

```
\# 转ISO 8601字符串
---------------

 返回一个 ISO 8601 格式的字符串。

 
```
dayjs('2019-01-25').toISOString // '2019-01-25T02:00:00.000Z'

```
\# 转对象
------

 返回包含时间信息的 Object。

 注意

 此功能依赖 ToObject 插件

 
```
dayjs.extend(toObject)

dayjs('2019-01-25').toObject
/* { years: 2019,
     months: 0,
     date: 25,
     hours: 0,
     minutes: 0,
     seconds: 0,
     milliseconds: 0 } */

```
\# 转字符串
-------

 返回包含时间信息的 string 。

 
```
dayjs('2019-01-25').toString // 'Fri, 25 Jan 2019 02:00:00 GMT'

```


\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-

\# 查询
=====

 ![包图网] Day.js 对象还有很多查询的方法。

 \# 是否之前
-------

 这表示 Day.js 对象是否在另一个提供的日期时间之前。

 
```
dayjs.isBefore(dayjs('2011-01-01')) // 默认毫秒

```
如果想使用除了毫秒以外的单位进行比较，则将单位作为第二个参数传入。

 
```
dayjs.isBefore('2011-01-01', 'year')

```
各个传入的单位对大小写不敏感，支持缩写和复数。

 \# 是否相同
-------

 这表示 Day.js 对象是否和另一个提供的日期时间相同。

 
```
dayjs.isSame(dayjs('2011-01-01')) // 默认毫秒

```
如果想使用除了毫秒以外的单位进行比较，则将单位作为第二个参数传入。

 当使用第二个参数时，将会连同去比较更大的单位。 如传入 `month` 将会比较 `month` 和 `year`。 传入 `day` 将会比较 `day`、 `month`和 `year`。

 
```
dayjs.isSame('2011-01-01', 'year')

```
各个传入的单位对大小写不敏感，支持缩写和复数。

 注意

 `isSame` 方法不传参数或参数为 `undefined` 都默认返回true

 \# 是否之后
-------

 这表示 Day.js 对象是否在另一个提供的日期时间之后。

 
```
dayjs.isAfter(dayjs('2011-01-01')) // 默认毫秒

// 其他写法
dayjs('2011-01-02').isAfter('2011-01-01') // true
dayjs('2011-01-02', 'YYYY-MM-DD').isAfter('2011-01-01') // true
dayjs(dayjs('2011-01-02')).isAfter(dayjs('2011-01-01')) // true
dayjs(dayjs('2011-01-02', 'YYYY-MM-DD')).isAfter(dayjs('2011-01-01', 'YYYY-MM-DD')) // true
dayjs(new Date('2011-01-02')).isAfter(new Date('2011-01-01')) // true

```
如果想使用除了毫秒以外的单位进行比较，则将单位作为第二个参数传入。

 
```
dayjs.isAfter('2011-01-01', 'year')

```
各个传入的单位对大小写不敏感，支持缩写和复数。

  \# 是否相同或之前
----------

 这表示 Day.js 对象是否和另一个提供的日期时间相同或在其之前。

 注意

 此功能依赖 IsSameOrBefore 插件

 
```
dayjs.extend(isSameOrBefore)
dayjs.isSameOrBefore(dayjs('2011-01-01')) // 默认毫秒

```
如果想使用除了毫秒以外的单位进行比较，则将单位作为第二个参数传入。

 
```
dayjs.isSameOrBefore('2011-01-01', 'year')

```
各个传入的单位对大小写不敏感，支持缩写和复数。

 \# 是否相同或之后
----------

 这表示 Day.js 对象是否和另一个提供的日期时间相同或在其之后。

 注意

 此功能依赖 IsSameOrAfter 插件

 
```
dayjs.extend(isSameOrAfter)
dayjs.isSameOrAfter(dayjs('2011-01-01')) // 默认毫秒

```
如果想使用除了毫秒以外的单位进行比较，则将单位作为第二个参数传入。

 
```
dayjs.isSameOrAfter('2011-01-01', 'year')

```
各个传入的单位对大小写不敏感，支持缩写和复数。

 \# 是否两者之间
---------

 这表示 Day.js 对象是否在其他两个的日期时间之间。

 注意

 此功能依赖 IsBetween 插件

 
```
dayjs.extend(isBetween)
dayjs('2010-10-20').isBetween('2010-10-19', dayjs('2010-10-25')) 
// 默认毫秒

```
如果想使用除了毫秒以外的单位进行比较，则将单位作为第三个参数传入。

 
```
dayjs.isBetween('2010-10-19', '2010-10-25', 'year')

```
各个传入的单位对大小写不敏感，支持缩写和复数。

 支持的单位列表

 第四个参数是设置包容性。 `[` 表示包含。 `(` 表示排除。

 要使用包容性参数，必须同时传入两个指示符。

 
```
dayjs('2016-10-30').isBetween('2016-01-01', '2016-10-30', null, '[)')

```
\# 是否是Day.js
------------

 这表示一个变量是否为 Day.js 对象。

 
```
dayjs.isDayjs(dayjs) // true
dayjs.isDayjs(new Date) // false

```
这和使用 instanceof 的结果是一样的：

 
```
dayjs instanceof dayjs // true

```
\# 是否闰年
-------

 查询 Day.js 对象的年份是否是闰年。

 注意

 此功能依赖 IsLeapYear 插件

 
```
dayjs.extend(isLeapYear)

dayjs('2000-01-01').isLeapYear // true

```


\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-

\# 国际化（i18n）
============

 ![包图网] Day.js 完美支持国际化。

 但除非手动加载，多国语言默认是不会被打包到工程里的。

 您可以加载多个其他语言并自由切换。

 支持的语言列表

 我们还根目录提供了 locale.json  (opens new window) 文件，包含所有支持的语言列表。

 语言包配置的具体细节以及如何更新或自定义语言都可以查看 自定义 中的内容。

 欢迎给我们提交 Pull Request 来增加新的语言。

 \# 在NodeJS中加载语言配置
-----------------

 按需加载语言文件。

 
```
require('dayjs/locale/de')
// import 'dayjs/locale/de' // ES 2015 

dayjs.locale('de') // use locale globally
dayjs.locale('de').format // use locale in a specific instance

```
您还可以加载并获取语言配置对象方便后面使用。

 
```
var locale_de = require('dayjs/locale/de')
// import locale_de from 'dayjs/locale/de'  // ES 2015 

```
\# 在浏览器中加载语言配置
--------------

 按需加载语言文件。

 
```
<script src="path/to/dayjs/locale/de"></script>
<script>
  dayjs.locale('de') // use locale globally
  dayjs.locale('de').format // use locale in a specific instance
</script>

```
获取语言对象方便后面使用。

 
```
<script src="path/to/dayjs/locale/de"></script>
<!-- Load locale as window.dayjs_locale_NAME -->
<script>
var customLocale = window.dayjs_locale_zh_cn // zh-cn -> zh_cn
</script>

```
可以通过 CDN 加载 Day.js.

 
```
<!-- CDN example (unpkg) -->
<script src=""></script>
<script src=""></script>
<script>dayjs.locale('zh-cn')</script>

```
 \# 改变全局语言配置
-----------

 默认情况下，Day.js 只内置了 English 的语言配置。
您可以按需加载其他本地化语言配置。

 
```
require('dayjs/locale/zh-cn')

```
当加载了一个语言配置之后，它就是可用的状态了。 要改变全局语言配置，只需调用 `dayjs.locale` 并传入一个已经加载的语言配置的名称。

 更改全局的语言配置并不会影响之前存在的实例。

 
```
dayjs.locale('zh-cn') // use loaded locale globally
dayjs.locale('en') // switch back to default English locale globally

```
\# 改变当前语言配置
-----------

 当操作多个 Day.js 实例并想格式化显示为不同语言的文字时，全局的语言配置可能会出现问题。

 用法与 `dayjs#locale` 一致，但只会修改当前实例的语言配置。

 
```
require('dayjs/locale/de')
dayjs.locale('de').format // 局部修改语言配置

```
\# 检查当前语言配置
-----------

 返回当前 Day.js 实例的语言配置。

 
```
dayjs.locale // 'en'

```
\# 列出当前语言的月份和周
--------------

 获取当前语言配置的全部月份和星期列表。

 注意

 此功能依赖 LocaleData 插件

 
```
dayjs.extend(localeData)

dayjs.weekdays
dayjs.weekdaysShort
dayjs.weekdaysMin
dayjs.monthsShort
dayjs.months // e.g. return [ 'January','February','March','April','May',
// 'June','July','August','September','October','November','December' ]

```
\# 获取语言配置的属性
------------

 你可以通过调用 `dayjs.localeData` 来获得当前全局语言配置的属性，或 `dayjs.localeData` 获取当前 Day.js 对象的。

 注意

 此功能依赖 LocaleData 插件

 
```
dayjs.extend(localeData)

globalLocaleData = dayjs.localeData
globalLocaleData.firstDayOfWeek
globalLocaleData.months
globalLocaleData.monthsShort
globalLocaleData.weekdays
globalLocaleData.weekdaysShort
globalLocaleData.weekdaysMin

globalLocaleData.months(dayjs)
globalLocaleData.monthsShort(dayjs)
globalLocaleData.weekdays(dayjs)
globalLocaleData.weekdaysShort(dayjs)
globalLocaleData.weekdaysMin(dayjs)

instanceLocaleData = dayjs.localeData
instanceLocaleData.firstDayOfWeek
instanceLocaleData.months
instanceLocaleData.monthsShort
instanceLocaleData.weekdays
instanceLocaleData.weekdaysShort
instanceLocaleData.weekdaysMin

```


\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-

\# 插件
=====

 ![包图网] 插件是一个独立的模块，可以添加到`Day.js`来扩展功能或添加新特性。

 默认情况下，`Day.js`只提供核心代码，没有安装插件。

 您可以根据需要加载多个插件。

 **自定义**

 您可以构建自己的`Day.js`插件来满足不同的需求。

 请随意打开一个`pull request`来共享你的插件。

 `Day.js`插件的模板。

 
```
export default (option, dayjsClass, dayjsFactory) => {
  // extend dayjs
  // e.g. add dayjs.isSameOrBefore
  dayjsClass.prototype.isSameOrBefore = function(arguments) {}

  // extend dayjs
  // e.g. add dayjs.utc
  dayjsFactory.utc = arguments => {}

  // overriding existing API
  // e.g. extend dayjs.format
  const oldFormat = dayjsClass.prototype.format
  dayjsClass.prototype.format = function(arguments) {
    // original format result
    const result = oldFormat.bind(this)(arguments)
    // return modified result
  }
}

```
\# 在NodeJS中加载插件
---------------

 按需加载插件。

 
```
var AdvancedFormat = require('dayjs/plugin/advancedFormat')
// import AdvancedFormat from 'dayjs/plugin/advancedFormat' // ES 2015

dayjs.extend(AdvancedFormat) // 使用插件

```
注意

 文档中的插件数量可能与实际下载在 `node_modules/dayjs/plugin/` 中的插件数量不一至，使用的时候注意对照一下。

 \# 在浏览器中加载插件
------------

 按需加载插件。

 
```
<script src="path/to/dayjs/plugin/advancedFormat"></script>
<!-- Load plugin as window.dayjs_plugin_NAME -->
<script>
  dayjs.extend(window.dayjs_plugin_advancedFormat)
</script>

```
Day.js可在CDN上使用。

 
```
<!-- CDN example (unpkg) -->
<script src=""></script>
<script src=""></script>
<script>dayjs.extend(window.dayjs_plugin_utc)</script>

```
TIP

 其他插件：依照官方插件名称使用 **`{插件版本(可选)}/plugin/{插件名称}.js`** 方式找，如：**``**。
  
其他类库：**`{类库名称}`**，如：**``**

  \# 插件列表
-------

 官方插件目录：  (opens new window)

 \# AdvancedFormat
-----------------

 AdvancedFormat延伸`dayjs`。格式化API以提供更多的格式选项。

 
```
var advancedFormat = require('dayjs/plugin/advancedFormat'); // nodejs
// import advancedFormat from 'dayjs/plugin/advancedFormat'; // ES 2015
dayjs.extend(advancedFormat)

dayjs.format('Q Do k kk X x')

```
已有的格式列表：

 

| 格式 | 输出 | 描述 |
| --- | --- | --- |
| Q | 1\-4 | 季度 |
| Do | 1st 2nd ... 31st | 月份的日期与序号 |
| k | 1\-24 | 整点，1点开始 |
| kk | 01\-24 | 小时，2位数，从1开始 |
| X | 1360013296 | Unix时间戳为秒 |
| x | 1360013296123 | Unix时间戳(毫秒) |
| w | 1 2 ... 52 53 | 年周(依赖于 WeekOfYear 插件) |
| ww | 01 02 ... 52 53 | 年份的周数，2位数(依赖于 WeekOfYear 插件) |
| wo | 1st 2nd ... 52nd 53rd | 带序号的年份周(依赖于 WeekOfYear 插件) |
| gggg | 2017 | 周年(依赖于 WeekYear 插件) |

 \# ArraySupport
---------------

 ArraySupport 扩展了 `dayjs`, `dayjs.utc` API 以支持数组参数。

 
```
var arraySupport = require("dayjs/plugin/arraySupport"); // nodejs
// import arraySupport from 'dayjs/plugin/arraySupport'; // ES 2015

dayjs.extend(arraySupport);

dayjs([2010, 1, 14, 15, 25, 50, 125]);
dayjs.utc([2010, 1, 14, 15, 25, 50, 125]);

```
\# BadMutable
-------------

 Day.js 被设计成不可变的对象，但是为了方便一些老项目实现对 moment.js 的替换，可以使用🚨 BadMutable 🚨插件让 Day.js 转变成可变的对象，

 注意

 这并不好，也不推荐在大多数项目中使用。

 当使用这个插件后，所有的 setter 都会更新当前实例。

 
```
var badMutable = require('dayjs/plugin/badMutable') // nodejs
// import badMutable from 'dayjs/plugin/badMutable' // ES 2015

dayjs.extend(badMutable)
// with 🚨 BadMutable 🚨 plugin
const today = dayjs
today.add(1, 'day')
console.log(today) // update itself, value will be tomorrow

```
\# BuddhistEra
--------------

 BuddhistEra 扩展了 `dayjs.format` API 以支持佛历格式化。

 佛历是一个年份编号系统，主要用于柬埔寨、老挝、缅甸和泰国等东南亚国家以及斯里兰卡、马来西亚和新加坡的中国人，用于宗教或官方场合 (Wikipedia)。

 要计算 BE 年，只需在年份中添加 543。 例如，1977 年 5 月 26 日 AD / CE 应显示为 2520 年 5 月 26 日 BE（1977 \+ 543）。

 
```
var buddhistEra = require('dayjs/plugin/buddhistEra') // nodejs
// import buddhistEra from 'dayjs/plugin/buddhistEra' // ES 2015

dayjs.extend(buddhistEra)

dayjs.format('BBBB BB')

```
扩展的模版列表：

 

| 格式 | 输出 | 描述 |
| --- | --- | --- |
| BBBB | 2561 | 完整佛历年 (年份 \+ 543\) |
| BB | 61 | 佛历年 (年份 \+ 543\) 两位数 |

 \# Calendar
-----------

 Calendar 增加了 `.calendar` API 返回一个 string 来显示日历时间。

 
```
var calendar = require('dayjs/plugin/calendar') // nodejs
// import calendar from 'dayjs/plugin/calendar' // ES 2015

dayjs.extend(calendar)

dayjs.calendar(dayjs('2008-01-01'))
dayjs.calendar(null, {
  sameDay: '[Today at] h:mm A', // The same day ( Today at 2:30 AM )
  nextDay: '[Tomorrow]', // The next day ( Tomorrow at 2:30 AM )
  nextWeek: 'dddd', // The next week ( Sunday at 2:30 AM )
  lastDay: '[Yesterday]', // The day before ( Yesterday at 2:30 AM )
  lastWeek: '[Last] dddd', // Last week ( Last Monday at 2:30 AM )
  sameElse: 'DD/MM/YYYY' // Everything else ( 7/10/2011 )
})

```
\# CustomParseFormat
--------------------

 CustomParseFormat 拓展了 `dayjs` 支持自定义时间格式。

 
```
var customParseFormat = require('dayjs/plugin/customParseFormat') // nodejs
// import customParseFormat from 'dayjs/plugin/customParseFormat' // ES 2015

dayjs.extend(customParseFormat)

dayjs('05/02/69 1:02:03 PM -05:00', 'MM/DD/YY H:mm:ss A Z')
// Returns an instance containing '1969-05-02T18:02:03.000Z'

dayjs('2018 Enero 15', 'YYYY MMMM DD', 'es')
// Returns an instance containing '2018-01-15T00:00:00.000Z'

dayjs('1970-00-00', 'YYYY-MM-DD', true) // strict parsing

```
支持的解析占位符列表：

 

| 输入 | 示例 | 描述 |
| --- | --- | --- |
| YY | 18 | 两位数的年份 |
| YYYY | 2018 | 四位数的年份 |
| M | 1\-12 | 月份，从 1 开始 |
| MM | 01\-12 | 月份，两位数 |
| MMM | Jan\-Dec | 缩写的月份名称 |
| MMMM | January\-December | 完整的月份名称 |
| D | 1\-31 | 月份里的一天 |
| DD | 01\-31 | 月份里的一天，两位数 |
| H | 0\-23 | 小时 |
| HH | 00\-23 | 小时，两位数 |
| h | 1\-12 | 小时, 12 小时制 |
| hh | 01\-12 | 小时, 12 小时制, 两位数 |
| m | 0\-59 | 分钟 |
| mm | 00\-59 | 分钟，两位数 |
| s | 0\-59 | 秒 |
| ss | 00\-59 | 秒，两位数 |
| S | 0\-9 | 毫秒，一位数 |
| SS | 00\-99 | 毫秒，两位数 |
| SSS | 000\-999 | 毫秒，三位数 |
| Z | \-05:00 | UTC 的偏移量 |
| ZZ | \-0500 | UTC 的偏移量，两位数 |
| A | AM / PM | 上午 下午 大写 |
| a | am / pm | 上午 下午 小写 |
| Do | 1st... 31st | 带序数词的月份里的一天 |
| X | 1410715640\.579 | Unix 时间戳 |
| x | 1410715640579 | Unix 时间戳 |

 \# DayOfYear
------------

 DayOfYear 增加了 `.dayOfYear` API 返回一个 `number` 来表示 Dayjs 的日期是年中第几天，或设置成是年中第几天。

 
```
var dayOfYear = require('dayjs/plugin/dayOfYear') // nodejs
// import dayOfYear from 'dayjs/plugin/dayOfYear' // ES 2015

dayjs.extend(dayOfYear)

dayjs('2010-01-01').dayOfYear // 1
dayjs('2010-01-01').dayOfYear(365) // 2010-12-31

```
\# DevHelper
------------

 DevHelper 可以在您使用 Day.js 时显示一些提示和警告方便开发。

 注意，您可以将 `process.env.NODE_ENV` 设置为 `production` 以禁用您的生产环境中的DevHelper。 如果您启用了像UglifyJS这样的 JavaScript 优化工具，它可以自动从生产包中移除此插件来减小打包体积。

 
```
var devHelper = require('dayjs/plugin/devHelper') // nodejs
// import devHelper from 'dayjs/plugin/devHelper' // ES 2015

dayjs.extend(devHelper)

```
您也可自行实现按需加载此插件。

 
```
if (isInDevelopment) {
  // load DevHelper plugin like above
}

```
\# Duration
-----------

 Duration 增加了 `.duration` `.isDuring` API 来支持时间长度。

 
```
var duration = require('dayjs/plugin/duration') // nodejs
// import duration from 'dayjs/plugin/duration' // ES 2015

dayjs.extend(duration)

dayjs.duration(100)

```
\# IsBetween
------------

 IsBetween 增加了 `.isBetween` API 返回一个 `boolean` 来展示一个时间是否介于两个时间之间。

 
```
var isBetween = require('dayjs/plugin/isBetween') // nodejs
// import isBetween from 'dayjs/plugin/isBetween' // ES 2015

dayjs.extend(isBetween)

// 如果使用年份对比 `year` 则传入第三个参数
dayjs('2010-10-20').isBetween('2010-10-19', dayjs('2010-10-25'), 'year')

// 第四个参数是两个字符 '[' 表示包含, '(' 表示不包含
// '' 不包含开始和结束的日期 (默认)
// '[]' 包含开始和结束的日期
// '[)' 包含开始日期但不包含结束日期
// 例如，当想包含开始的日期作为比较依据，你应该使用“day”作为第三个参数。
dayjs('2016-10-30').isBetween('2016-01-01', '2016-10-30', 'day', '[)')

```
\# IsLeapYear
-------------

 IsLeapYear 增加了 `.isLeapYear` API 返回一个 `boolean` 来展示一个 Day.js 对象的年份是不是闰年。

 
```
var isLeapYear = require('dayjs/plugin/isLeapYear') // nodejs
// import isLeapYear from 'dayjs/plugin/isLeapYear' // ES 2015

dayjs.extend(isLeapYear)

dayjs('2000-01-01').isLeapYear // true

```
\# IsSameOrAfter
----------------

 IsSameOrAfter 增加了 `.isSameOrAfter` API 返回一个 `boolean` 来展示一个时间是否和一个时间相同或在一个时间之后。

 
```
var isSameOrAfter = require('dayjs/plugin/isSameOrAfter') // nodejs
// import isSameOrAfter from 'dayjs/plugin/isSameOrAfter' // ES 2015

dayjs.extend(isSameOrAfter)

dayjs('2010-10-20').isSameOrAfter('2010-10-19', 'year')

```
\# IsSameOrBefore
-----------------

 IsSameOrBefore 增加了 `.isSameOrBefore` API 返回一个 `boolean` 来展示一个时间是否和一个时间相同或在一个时间之前。

 
```
var isSameOrBefore = require('dayjs/plugin/isSameOrBefore') // nodejs
// import isSameOrBefore from 'dayjs/plugin/isSameOrBefore' // ES 2015

dayjs.extend(isSameOrBefore)

dayjs('2010-10-20').isSameOrBefore('2010-10-19', 'year')

```
\# IsToday
----------

 IsToday 增加了 `.isToday` API 来判断当前 Day.js 对象是否是今天。

 
```
var isToday = require('dayjs/plugin/isToday') // nodejs
// import isToday from 'dayjs/plugin/isToday' // ES 2015

dayjs.extend(isToday)

dayjs.isToday // true

```
\# IsTomorrow
-------------

 IsTomorrow 增加了 `.isTomorrow` API 来判断当前 Day.js 对象是否是明天。

 
```
var isTomorrow = require('dayjs/plugin/isTomorrow') // nodejs
// import isTomorrow from 'dayjs/plugin/isTomorrow' // ES 2015

dayjs.extend(isTomorrow)

dayjs.add(1, 'day').isTomorrow // true

```
\# IsYesterday
--------------

 IsYesterday 增加了 `.isYesterday` API 来判断当前 Day.js 对象是否是昨天。

 
```
var isYesterday = require('dayjs/plugin/isYesterday') // nodejs
// import isYesterday from 'dayjs/plugin/isYesterday' // ES 2015

dayjs.extend(isYesterday)

dayjs.add(-1, 'day').isYesterday // true

```
\# IsoWeek
----------

 IsoWeek 添加 `.isoWeek` API 以获取或设置年度的 ISO 周数。 并添加 `.isoWeekday` 获取或设置一周的 ISO 日和 `isoWeekYear` 获取ISO 周年，并扩展 `.startOf` `.endOf` APIs 支持单位 `isoWeek`

 
```
var isoWeek = require('dayjs/plugin/isoWeek') // nodejs
// import isoWeek from 'dayjs/plugin/isoWeek' // ES 2015

dayjs.extend(isoWeek)

dayjs.isoWeek
dayjs.isoWeekday
dayjs.isoWeekYear

```
\# IsoWeeksInYear
-----------------

 IsoWeeksInYear 增加了 `.isoWeeksInYear` API 返回一个 `number` 来得到依据 ISO week 标准一年中有几周

 注意

 此功能依赖 IsLeapYear 插件，才能正常运行

 
```
var isLeapYear = require('dayjs/plugin/isLeapYear')
var isoWeeksInYear = require('dayjs/plugin/isoWeeksInYear') // 依赖 isLeapYear 插件
// import isoWeeksInYear from 'dayjs/plugin/isoWeeksInYear' // ES 2015
// import isLeapYear from 'dayjs/plugin/isLeapYear' // ES 2015

dayjs.extend(isLeapYear)
dayjs.extend(isoWeeksInYear)

dayjs('2004-01-01').isoWeeksInYear // 53
dayjs('2005-01-01').isoWeeksInYear // 52

```
\# LocaleData
-------------

 LocaleData 增加了 `dayjs.localeData` API 来提供本地化数据。

 
```
var localeData = require('dayjs/plugin/localeData') // nodejs
// import localeData from 'dayjs/plugin/localeData' // ES 2015

dayjs.extend(localeData)

dayjs.localeData

```
支持的方法：

 
```
dayjs.months
dayjs.monthsShort
dayjs.weekdays
dayjs.weekdaysShort
dayjs.weekdaysMin
dayjs.longDateFormat('L')

globalLocaleData = dayjs.localeData
globalLocaleData.firstDayOfWeek
globalLocaleData.months
globalLocaleData.monthsShort
globalLocaleData.weekdays
globalLocaleData.weekdaysShort
globalLocaleData.weekdaysMin
globalLocaleData.longDateFormat('L')

globalLocaleData.months(dayjs)
globalLocaleData.monthsShort(dayjs)
globalLocaleData.weekdays(dayjs)
globalLocaleData.weekdaysShort(dayjs)
globalLocaleData.weekdaysMin(dayjs)
globalLocaleData.meridiem
globalLocaleData.ordinal

instanceLocaleData = dayjs.localeData
instanceLocaleData.firstDayOfWeek
instanceLocaleData.months
instanceLocaleData.monthsShort
instanceLocaleData.weekdays
instanceLocaleData.weekdaysShort
instanceLocaleData.weekdaysMin
instanceLocaleData.longDateFormat('L')
instanceLocaleData.meridiem
instanceLocaleData.ordinal

```
\# LocalizedFormat
------------------

 LocalizedFormat 扩展了 `dayjs.format` API 以支持更多本地化的长日期格式。

 
```
var localizedFormat = require('dayjs/plugin/localizedFormat') // nodejs
// import localizedFormat from 'dayjs/plugin/localizedFormat' // ES 2015

dayjs.extend(localizedFormat)

dayjs.format('L LT')

```
支持的本地化格式列表

 \# MinMax
---------

 MinMax 增加了 `.min` `.max`API 返回一个 `dayjs` 来比较传入的 Day.js 实例的大小。 它接受传入多个 Day.js实例或一个数组。

 
```
var minMax = require('dayjs/plugin/minMax') // nodejs
// import minMax from 'dayjs/plugin/minMax' // ES 2015

dayjs.extend(minMax)

dayjs.max(dayjs, dayjs('2018-01-01'), dayjs('2019-01-01'))
dayjs.min([dayjs, dayjs('2018-01-01'), dayjs('2019-01-01')])

```
\# ObjectSupport
----------------

 ObjectSupport 扩展了 `dayjs`, `dayjs.utc`, `dayjs.set`, `dayjs.add`, `dayjs.subtract` API 以支持传入对象参数。

 
```
var objectSupport = require("dayjs/plugin/objectSupport") // nodejs
// import objectSupport from 'dayjs/plugin/objectSupport' // ES 2015

dayjs.extend(objectSupport);

dayjs({
  year: 2010,
  month: 1,
  day: 12
});
dayjs.utc({
  year: 2010,
  month: 1,
  day: 12
});
dayjs.set({ year: 2010, month: 1, day: 12 })
dayjs.add({ M: 1 })
dayjs.subtract({ month: 1 })

```
\# PluralGetSet
---------------

 PluralGetSet 增加了复数形式的 API `.milliseconds`, `.seconds`, `.minutes`, `.hours`, `.days`, `.weeks`, `.isoWeeks`, `.months`, `.quarters`, `.years`, `.dates`。

 
```
var pluralGetSet = require('dayjs/plugin/pluralGetSet') // nodejs
// import pluralGetSet from 'dayjs/plugin/pluralGetSet' // ES 2015

dayjs.extend(pluralGetSet)

dayjs.millisecond
dayjs.milliseconds

```
\# PreParsePostFormat
---------------------

 预解析/后格式化让您在解析前处理输入，并在格式化要输出的字符串。 参考类似 moment.js  (opens new window) 国际化里的用法。

 注意

 此插件需要在 `localeData` 插件之前导入(因为有依赖关系)。

 注意

 此插件也会改变 `relativeTime` 插件的相关行为

 ### \# 基础用法

 例如： 在阿拉伯语言中，它被用于支持阿拉伯数字的特殊显示  (opens new window)。

 
```
// Arabic [ar]
import dayjs from 'dayjs'
import preParsePostFormat from 'dayjs/plugin/preParsePostFormat'
dayjs.extend(preParsePostFormat)

const months = 'يناير_فبراير_مارس_أبريل_مايو_يونيو_يوليو_أغسطس_سبتمبر_أكتوبر_نوفمبر_ديسمبر'.split('_')
const symbolMap = {
  1: '١',
  2: '٢',
  3: '٣',
  4: '٤',
  5: '٥',
  6: '٦',
  7: '٧',
  8: '٨',
  9: '٩',
  0: '٠'
}

const numberMap = {
  '١': '1',
  '٢': '2',
  '٣': '3',
  '٤': '4',
  '٥': '5',
  '٦': '6',
  '٧': '7',
  '٨': '8',
  '٩': '9',
  '٠': '0'
}

const locale = {
  name: 'ar',
  // ...
  preparse(string) {
    return string
      .replace(
        /[١٢٣٤٥٦٧٨٩٠]/g,
        match => numberMap[match]
      )
      .replace(/،/g, ',')
  },
  postformat(string) {
    return string
      .replace(/\d/g, match => symbolMap[match])
      .replace(/,/g, '،')
  },
  // ...
}
// ...

```
单元测试  (opens new window) 也应该让您很好地了解如何使用插件。

 \# QuarterOfYear
----------------

 QuarterOfYear 增加了 `.quarter` API 返回当前实例是哪个季度，并扩展了 `.add` `.subtract` `.startOf` `.endOf` API 来支持 `quarter` 季度单位。

 
```
var quarterOfYear = require('dayjs/plugin/quarterOfYear') // nodejs
// import quarterOfYear from 'dayjs/plugin/quarterOfYear' // ES 2015

dayjs.extend(quarterOfYear)

dayjs('2010-04-01').quarter // 2
dayjs('2010-04-01').quarter(2)

```
\# RelativeTime
---------------

 RelativeTime 增加了 `.from` `.to` `.fromNow` `.toNow` 4 个 API 来展示相对的时间 (例如：3 小时以前)。

 
```
var relativeTime = require('dayjs/plugin/relativeTime') // nodejs
// import relativeTime from 'dayjs/plugin/relativeTime' // ES 2015

dayjs.extend(relativeTime)

dayjs.from(dayjs('1990-01-01')) // 31 年后
dayjs.from(dayjs('1990-01-01'), true) // 31 年
dayjs.fromNow

dayjs.to(dayjs('1990-01-01')) // 31 年前
dayjs.toNow

```
### \# 距离现在的相对时间 `.fromNow(withoutSuffix?: boolean)`

 返回 `string` 距离现在的相对时间

 ### \# 距离 X 的相对时间 `.from(compared: Dayjs, withoutSuffix?: boolean)`

 返回 `string` 距离 X 的相对时间

 ### \# 到现在的相对时间 `.toNow(withoutSuffix?: boolean)`

 返回 `string` 到现在的相对时间

 ### \# 到 X 的相对时间 `.to(compared: Dayjs, withoutSuffix?: boolean)`

 返回 `string` 到 X 的相对时间

 时间范围划分标准：

 

| 范围 | 键值 | 示例输出 |
| --- | --- | --- |
| 0 到 44 秒 | s | 几秒前 |
| 45 到 89 秒 | m | 1 分钟前 |
| 90 秒 到 44 分 | mm | 2 分钟前 ... 44 分钟前 |
| 45 到 89 分 | h | 1 小时前 |
| 90 分 到 21 小时 | hh | 2 小时前 ... 21 小时前 |
| 22 到 35 小时 | d | 1 天前 |
| 36 小时 到 25 天 | dd | 2 天前 ... 25 天前 |
| 26 到 45 天 | M | 1 个月前 |
| 46 天 到 10 月 | MM | 2 个月前 ... 10 个月前 |
| 11 月 到 17 月 | y | 1 年前 |
| 18 月以上 | yy | 2 年前 ... 20 年前 |

 \# Timezone
-----------

 Timezone 插件添加了 `dayjs.tz` `.tz` `.tz.guess` `.tz.setDefault` API，在时区之间解析或显示。

 
```
var utc = require('dayjs/plugin/utc')
var timezone = require('dayjs/plugin/timezone') // 依赖 utc 插件

dayjs.extend(utc)
dayjs.extend(timezone)

dayjs.tz("2014-06-01 12:00", "America/New_York")

dayjs("2014-06-01 12:00").tz("America/New_York")

dayjs.tz.guess

dayjs.tz.setDefault("America/New_York")

```
\# ToArray
----------

 ToArray 增加了 `.toArray` API 来返回包含时间数值的 array。

 
```
var toArray = require('dayjs/plugin/toArray') // nodejs
// import toArray from 'dayjs/plugin/toArray' // ES 2015

dayjs.extend(toArray)

dayjs('2019-01-25').toArray // [ 2019, 0, 25, 0, 0, 0, 0 ]

```
\# ToObject
-----------

 ToObject 增加了 `.toObject` API 来返回包含时间数值的 `object`。

 
```
var toObject = require('dayjs/plugin/toObject') // nodejs
// import toObject from 'dayjs/plugin/toObject' // ES 2015

dayjs.extend(toObject)

dayjs('2019-01-25').toObject
/* { years: 2019,
     months: 0,
     date: 25,
     hours: 0,
     minutes: 0,
     seconds: 0,
     milliseconds: 0 } */

```
\# UpdateLocale
---------------

 UpdateLocale 增加了 `.updateLocale` API 来更新语言配置的属性。

 
```
var updateLocale = require('dayjs/plugin/updateLocale') // nodejs
// import updateLocale from 'dayjs/plugin/updateLocale' // ES 2015

dayjs.extend(updateLocale)

dayjs.updateLocale('en', {
  months : String[]
})

```
\# UTC
------

 UTC 增加了 `.utc` `.local` `.isUTC` APIs 使用 UTC 模式来解析和展示时间。

 
```
var utc = require('dayjs/plugin/utc') // nodejs
// import utc from 'dayjs/plugin/utc' // ES 2015

dayjs.extend(utc)

// 默认当地时间
dayjs.format //2019-03-06T17:11:55+08:00

// UTC 模式
dayjs.utc.format // 2019-03-06T09:11:55Z

// 将本地时间转换成 UTC 时间
dayjs.utc.format // 2019-03-06T09:11:55Z 

// 在 UTC 模式下，所有的展示方法都将使用 UTC 而不是本地时区
// 所有的 get 和 set 方法也都会使用 Date#getUTC* 和 Date#setUTC* 而不是 Date#get* and Date#set*
dayjs.utc.isUTC // true
dayjs.utc.local.format //2019-03-06T17:11:55+08:00
dayjs.utc('2018-01-01', 'YYYY-MM-DD') // with CustomParseFormat plugin

```
默认情况下，Day.js 会把时间解析成本地时间。

 Day.js 默认使用用户本地时区来解析和展示时间。 如果想要使用 UTC 模式来解析和展示时间，可以使用 `dayjs.utc` 而不是 `dayjs`

 ### \# dayjs.utc `dayjs.utc(dateType?: string | number | Date | Dayjs, format? string)`

 返回一个使用 UTC 模式的 `Dayjs` 对象。

 ### \# Use UTC time `.utc`

 返回一个复制的包含使用 UTC 模式标记的 `Dayjs` 对象。

 ### \# Use local time `.local`

 返回一个复制的包含使用本地时区标记的 `Dayjs` 对象。

 ### \# Set UTC offset `.utcOffset`

 返回一个复制的使用 UTC 模式的 `Day.js` 对象。

 ### \# isUTC mode `.isUTC`

 返回一个 `boolean` 来展示当前 Day.js 对象是不是在 UTC 模式下。

 \# Weekday
----------

 WeekDay 增加了 `.weekday` API 来获取或设置当前语言的星期。

 
```
var weekday = require('dayjs/plugin/weekday') // nodejs
// import weekday from 'dayjs/plugin/weekday' // ES 2015

dayjs.extend(weekday)

// 当星期天是一周的第一天
dayjs.weekday(-7); // 上个星期天
dayjs.weekday(7); // 下个星期天

// 当星期一是一周的第一天
dayjs.weekday(-7) // 上个星期一
dayjs.weekday(7) // 下个星期一

```
\# weekOfYear
-------------

 WeekOfYear 增加了 `.week` API 返回一个 `number` 来表示 Day.js 的日期是年中第几周。

 
```
var weekOfYear = require('dayjs/plugin/weekOfYear') // nodejs
// import weekOfYear from 'dayjs/plugin/weekOfYear' // ES 2015

dayjs.extend(weekOfYear)

dayjs('2018-06-27').week // 26
dayjs('2018-06-27').week(5) // 设置周

```
\# WeekYear
-----------

 WeekYear 增加了 `.weekYear` API 来获取基于当前语言的按周计算的年份。

 
```
var weekOfYear = require('dayjs/plugin/weekOfYear')
var weekYear = require('dayjs/plugin/weekYear') // 依赖 weekOfYear 插件

dayjs.extend(weekOfYear)
dayjs.extend(weekYear)

dayjs.weekYear

```


\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-

\# 自定义
======

 ![包图网] 想自定义 Day.js 也很容易。

 您可以创建一个新的语言配置。

 
```
var localeObject = {...} // Day.js 语言对象，下面有详述
dayjs.locale('en-my-settings', localeObject);

```
更新一个已有的语言配置。

 注意

 此功能依赖 UpdateLocale 插件

 
```
dayjs.extend(updateLocale)

dayjs.updateLocale('en', {
  // ...
})

```
 Day.js 的语言对象模版。

 
```
const localeObject = {
  name: 'es', // name String
  weekdays: 'Domingo_Lunes ...'.split('_'), // weekdays Array
  weekdaysShort: 'Sun_M'.split('_'), // OPTIONAL, short weekdays Array, use first three letters if not provided
  weekdaysMin: 'Su_Mo'.split('_'), // OPTIONAL, min weekdays Array, use first two letters if not provided
  weekStart: 1, // OPTIONAL, set the start of a week. If the value is 1, Monday will be the start of week instead of Sunday。
  yearStart: 4, // OPTIONAL, the week that contains Jan 4th is the first week of the year.
  months: 'Enero_Febrero ... '.split('_'), // months Array
  monthsShort: 'Jan_F'.split('_'), // OPTIONAL, short months Array, use first three letters if not provided
  ordinal: n => `${n}º`, // ordinal Function (number) => return number + output
  formats: {
    // abbreviated format options allowing localization
    LTS: 'h:mm:ss A',
    LT: 'h:mm A',
    L: 'MM/DD/YYYY',
    LL: 'MMMM D, YYYY',
    LLL: 'MMMM D, YYYY h:mm A',
    LLLL: 'dddd, MMMM D, YYYY h:mm A',
    // lowercase/short, optional formats for localization
    l: 'D/M/YYYY',
    ll: 'D MMM, YYYY',
    lll: 'D MMM, YYYY h:mm A',
    llll: 'ddd, MMM D, YYYY h:mm A'
  },
  relativeTime: {
    // relative time format strings, keep %s %d as the same
    future: 'in %s', // e.g. in 2 hours, %s been replaced with 2hours
    past: '%s ago',
    s: 'a few seconds',
    m: 'a minute',
    mm: '%d minutes',
    h: 'an hour',
    hh: '%d hours', // e.g. 2 hours, %d been replaced with 2
    d: 'a day',
    dd: '%d days',
    M: 'a month',
    MM: '%d months',
    y: 'a year',
    yy: '%d years'
  },
  meridiem: (hour, minute, isLowercase) => {
    // OPTIONAL, AM/PM
    return hour > 12 ? 'PM' : 'AM'
  }
}

```
Day.js 的语言文件模板。 (例 dayjs/locale/es.js)

 
```
import dayjs from 'dayjs'

const locale = { ... } // Day.js 的语言对象.

dayjs.locale(locale, null, true) // load locale for later use

export default locale

```
\# 月份名称
-------

 `Locale#months` 应该是一个包含月份的数组。

 注意

 此功能依赖 UpdateLocale 插件

 
```
dayjs.extend(updateLocale)

dayjs.updateLocale('en', {
  months: [
    "January", "February", "March", "April", "May", "June", "July",
    "August", "September", "October", "November", "December"
  ]
})

```
如果需要更多处理来计算月份的名称（例如，如果不同格式的语法不同），则 `Locale#months` 可以是具有以下签名的函数。 它应始终返回月份的名称。

 
```
dayjs.updateLocale("en", {
  months: function (dayjsInstance, format) {
    // dayjsInstance is the Day.js object currently being formatted
    // format is the formatting string
    if (/^MMMM/.test(format)) {
      // if the format starts with 'MMMM'
      return monthShortFormat[dayjsInstance.month];
    } else {
      return monthShortStandalone[dayjsInstance.month];
    }
  },
});

```
\# 月份缩写
-------

 `Locale#monthsShort` 应该是一个包含月份缩写数组。

 注意

 此功能依赖 UpdateLocale 插件

 
```
dayjs.extend(updateLocale)

dayjs.updateLocale('en', {
  monthsShort: [
    "Jan", "Feb", "Mar", "Apr", "May", "Jun",
    "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"
  ]
})

```
`Locale#monthsShort` 可以是一个回调函数，可参考 `Locale#months`

 \# 星期名称
-------

 `Locale#weekdayjs` 应该是一个包含星期名称的数组。

 注意

 此功能依赖 UpdateLocale 插件

 
```
dayjs.extend(updateLocale)

dayjs.updateLocale('en', {
  weekdays: [
    "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"
  ]
})

```
\# 星期缩写
-------

 `Locale#weekdaysShort` 应该是一个缩写的周的数组。

 注意

 此功能依赖 UpdateLocale 插件

 
```
dayjs.extend(updateLocale)

dayjs.updateLocale('en', {
  weekdaysShort: ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"]
})

```
\# 星期最短缩写
---------

 `Locale#weekdayMin` 是一个包含星期缩写(两个字母) 的数组。

 注意

 此功能依赖 UpdateLocale 插件

 
```
dayjs.extend(updateLocale)

dayjs.updateLocale('en', {
  weekdaysMin: ["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"]
})

```
\# 相对时间
-------

 `Locale#relativeTime` 应该是 `dayjs#from` 里用来替换字符串的对象。

 注意

 此功能依赖 UpdateLocale 插件

 
```
dayjs.extend(updateLocale)

dayjs.updateLocale('en', {
  relativeTime: {
    future: "in %s",
    past: "%s ago",
    s: 'a few seconds',
    m: "a minute",
    mm: "%d minutes",
    h: "an hour",
    hh: "%d hours",
    d: "a day",
    dd: "%d days",
    M: "a month",
    MM: "%d months",
    y: "a year",
    yy: "%d years"
  }
})

```
`Locale#relativeTime.future` 代表未来日期的前缀/后缀。 `Locale#relativeTime.past` 代表过去日期的前缀/后缀。

 其余的属性，单字符代表单数，双字符代表复数。

 **更高级的占位符处理**

 如果语言配置需要更高级的占位符处理，则可以传入一个函数而不是字符串。 函数应该返回一个字符串。

 
```
relativeTime: {
  ...,
  yy: function (number, withoutSuffix, key, isFuture) {
    return string;
  }
}

```
`number` 参数代表该键的单位数。 对于 `m`，该数字是分钟数，以此类推。

 如果要不带后缀显示，则传入 `without Suffix` 参数为 true，如果要带后缀显示，则为 false。 (之所以使用逻辑倒置，是因为默认的行为是显示后缀。)

 `key` 参数代表 `Locale#relativeTime` 对象中的替换键。 (例如 `s m mm h` 等）

 如果要使用未来的后缀/前缀，则 `isFuture` 参数将会为 true，如果要使用过去的前缀/后缀，则为 false。

 **Relative Time 的阈值和舍入函数配置**

 您可以在使用此插件时，通过传入一个配置对象来更新其阈值和舍入函数的配置。

 
```
var config = {
  thresholds: [{}],
  rounding: function
}
dayjs.extend(relativeTime, config)

```
`thresholds` 是一个 `Array` 的 `Object` 定义了每个一分钟、一小时等等的单位。 例如，默认情况下，超过45秒会被视为一分钟，超过22小时会被视为一天，依此类推。 要改变这个，可以传入一个新的 `thresholds`。

 
```
// strict thresholds
var thresholds = [
  { l: 's', r: 1 },
  { l: 'm', r: 1 },
  { l: 'mm', r: 59, d: 'minute' },
  { l: 'h', r: 1 },
  { l: 'hh', r: 23, d: 'hour' },
  { l: 'd', r: 1 },
  { l: 'dd', r: 29, d: 'day' },
  { l: 'M', r: 1 },
  { l: 'MM', r: 11, d: 'month' },
  { l: 'y' },
  { l: 'yy', d: 'year' }
]

```
也可以添加自定义键值并更新相应的语言设置。

 
```
var thresholds = [
  ...,
  { l: 'ss', r: 59, d: 'second' }
]
dayjs.updateLocale('en', {
  relativeTime: {
    ...,
    ss: "%d seconds"
  }
})

```
`rounding` 是一个根据语言配置在相对时间字符串展示之前处理数字的 `Function`。 要改变这个，可以传入一个新的 `rounding`。

 
```
// Math.round by default
var rounding = Math.floor

```
\# 日历
-----

 `Locale#calendar` 应该包含以下内容。

 注意

 此功能依赖 UpdateLocale 插件

 
```
dayjs.extend(updateLocale)

dayjs.updateLocale('en', {
  calendar: {
    lastDay: '[Yesterday at] LT',
    sameDay: '[Today at] LT',
    nextDay: '[Tomorrow at] LT',
    lastWeek: '[last] dddd [at] LT',
    nextWeek: 'dddd [at] LT',
    sameElse: 'L'
  }
})

```
每个 `Locale#calendar` 的键值也可以是一个函数，这个函数的作用域是当前 Day.js 对象，且传入的第一个参数是代表当天的 Day.js 对象。 这个函数需要返回一个格式化后的字符串。

 
```
function callback (now) {
  return '[hoy a la' + ((this.hour !== 1) ? 's' : '') + ']' + now.format;
}

```


\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-

\# 时长
=====

 ![包图网] Day.js 有表示时间长度的对象。 将 Day.js 对象定义为单个时间点，将 duration 定义为时间的长度。

 时长没有定义的开始和结束日期。 它们是无上下文的。

 从概念上讲，时长比 '今天下午2点到4点之间' 更类似于 '2 小时'。 因此，在依赖上下文的时间单位之间的转换中，时长并不是一个合适的选择。

 例如，一年可以定义为366天、365天、365\.25天、12个月、52周。 没有上下文，试图将年转换为天是毫无意义的。 与使用 Durations 相比，使用 dayjs\#diff 计算两个时刻之间的天数或年数要好得多。

 注意

 此功能依赖 Duration 插件

 
```
dayjs.extend(duration)

dayjs.duration({ months: 12 })

```
\# 创建
-----

 要创建时长，则调用 `dayjs.duration`，并以毫秒为单位。

 注意

 此功能依赖 Duration 插件

 
```
dayjs.extend(duration)

dayjs.duration(100); // 100 milliseconds

```
如果要使用毫秒以外的其他度量单位来创建时长对象，则也可以传入度量单位。

 
```
dayjs.duration(2, 'days');

```
支持的单位列表：

 

| 单位 | 缩写 |
| --- | --- |
| days | d |
| weeks | w |
| months | M |
| years | y |
| hours | h |
| minutes | m |
| seconds | s |
| milliseconds | ms |

 如果需要同时传入多个不同的度量单位，则可以传入值的对象。

 
```
dayjs.duration({
  seconds: 2,
  minutes: 2,
  hours: 2,
  days: 2,
  weeks: 2,
  months: 2,
  years: 2
});

```
Day.js 也支持解析 ISO 8601 时长格式。

 
```
dayjs.duration('P1Y2M3DT4H5M6S');
dayjs.duration('P1M');

```
\# 克隆
-----

 
```
dayjs.duration.clone;

```
复制一个时长对象。 时长是不可变的，就像 Day.js 对象一样。 当然，这可以用来保存在某个时间点的快照。

  \# 人性化
------

 当仅仅是想显示一段时长，要得到类似 dayjs\#from 的结果，但又不想创建两个 Day.js 对象时。

 注意

 此功能依赖 Duration 插件，才能正常运行

 注意

 此功能依赖 RelativeTime 插件，才能正常运行

 
```
dayjs.extend(duration)
dayjs.extend(relativeTime)
dayjs.duration(1, "minutes").humanize; // a minute
dayjs.duration(2, "minutes").humanize; // 2 minutes
dayjs.duration(24, "hours").humanize;  // a day

```
默认情况下，返回的字符串是没有后缀。 如果需要后缀，则按如下所示传入 true。

 
```
dayjs.duration(1, "minutes").humanize(true); // in a minute

```
对于当前时间之前的后缀，则传入负数。

 
```
dayjs.duration(-1, "minutes").humanize(true); // a minute ago

```
\# 格式化
------

 根据传入的占位符返回格式化后的时长。

 将字符放在方括号中，即可原样返回而不被格式化替换 (例如， \[`MM`])。

 
```
dayjs.duration({
  seconds: 1,
  minutes: 2,
  hours: 3,
  days: 4,
  months: 6,
  years: 7
}).format('YYYY-MM-DDTHH:mm:ss') // 0007-06-04T03:02:01

```
支持的格式化占位符列表：

 

| Format | 输出 | 详情 |
| --- | --- | --- |
| Y | 18 | 年 |
| YY | 18 | 年，两位数 |
| YYYY | 2018 | 年，四位数 |
| M | 1\-12 | 月份，从 1 开始 |
| MM | 01\-12 | 月份，两位数 |
| D | 1\-31 | 日 |
| DD | 01\-31 | 日，两位数 |
| H | 0\-23 | 小时 |
| HH | 00\-23 | 小时，两位数 |
| m | 0\-59 | 分钟 |
| mm | 00\-59 | 分钟，两位数 |
| s | 0\-59 | 秒 |
| ss | 00\-59 | 秒，两位数 |
| SSS | 000\-999 | 毫秒，三位数 |

 \# 毫秒
-----

 要获取时长的毫秒数，请使用 `dayjs.duration.milliseconds`。

 它将返回0到999之间的数字。

 
```
dayjs.duration(500).milliseconds; // 500
dayjs.duration(1500).milliseconds; // 500
dayjs.duration(15000).milliseconds; // 0

```
如果想得到要以毫秒为单位时长长度，则改用 `dayjs.duration.asMilliseconds`。

 
```
dayjs.duration(500).asMilliseconds; // 500
dayjs.duration(1500).asMilliseconds; // 1500
dayjs.duration(15000).asMilliseconds; // 15000

```
\# 秒
----

 要获取时长的秒数，请使用 `dayjs.duration.seconds`。

 它将返回0到59之间的数字。

 
```
dayjs.duration(500).seconds; // 0
dayjs.duration(1500).seconds; // 1
dayjs.duration(15000).seconds; // 15

```
如果想得到要以秒为单位时长长度，则改用 `dayjs.duration.asSeconds`。

 
```
dayjs.duration(500).asSeconds; // 0.5
dayjs.duration(1500).asSeconds; // 1.5
dayjs.duration(15000).asSeconds; // 15

```
\# 分钟
-----

 
```
dayjs.duration.minutes;
dayjs.duration.asMinutes;

```
和其他 getters 类似， `dayjs.duration.minutes` 用来获取时长的分钟部分 (0\-59\)。

 `dayjs.duration.asMinutes` 获取以分钟为单位的时长。

 \# 小时
-----

 
```
dayjs.duration.hours;
dayjs.duration.asHours;

```
和其他 getters 类似， `dayjs.duration.hours` 用来获取时长的小时部分 (0\-23\)。

 `dayjs.duration.asHours` 获取以小时为单位的时长。

 \# 日
----

 
```
dayjs.duration.days;
dayjs.duration.asDays;

```
和其他 getters 类似， `dayjs.duration.days` 用来获取时长的天部分 (0\-30\)。

 `dayjs.duration.asDays` 获取以天为单位的时长。

 \# 周
----

 
```
dayjs.duration.weeks;
dayjs.duration.asWeeks;

```
和其他 getters 类似， `dayjs.duration.weeks` 用来获取时长的周部分 (0\-4\)。

 `dayjs.duration.asWeeks` 获取以周为单位的时长。

 与时长的其他 getter 不同，周数是作为天数的子集，且不会从天数中扣除。

 注意：以周为单位的时长的长度定义为 7 天。

 \# 月
----

 
```
dayjs.duration.months;
dayjs.duration.asMonths;

```
和其他 getters 类似， `dayjs.duration.months` 用来获取时长的月份部分 (0\-11\)。

 `dayjs.duration.asMonths` 获取以月份为单位的时长。

 \# 年
----

 
```
dayjs.duration.years;
dayjs.duration.asYears;

```
和其他 getters 类似， `dayjs.duration.years` 用来获取时长的年部分。

 `dayjs.duration.asYears` 获取以年为单位的时长。

 \# 增加时间
-------

 返回增加一定时间的复制的时长对象。

 
```
var a = dayjs.duration(1, 'd');
var b = dayjs.duration(2, 'd');

a.add(b).days; // 3
a.add({ days: 2 } ).days;
a.add(2, 'days');

```
支持的单位列表

 \# 减少时间
-------

 返回减去一定时间的复制的时长对象。

 
```
var a = dayjs.duration(3, 'd');
var b = dayjs.duration(2, 'd');

a.subtract(b).days; // 1
a.subtract({ days: 2 } ).days;
a.subtract(2, 'days');

```
支持的可用单位同上。

 \# Diff时长
---------

 可以将时长与 `dayjs#diff` 一起使用，以获取两个时刻之间的时长。 为此，只需将 `dayjs#diff` 方法传给 `dayjs#duration`，如下所示：

 
```
var x = dayjs
var y = dayjs

var duration = dayjs.duration(x.diff(y))
// 返回时长对象，其时长在 x 和 y 之间。

```
查看 这里 了解更多关于 `dayjs#diff` 的信息。

 \# 单位转换
-------

 作为 `Duration#asX` 的替代，可以使用 `Duration#as('x')`。

 
```
var duration = dayjs.duration

duration.as('hours');
duration.as('minutes');
duration.as('seconds');
duration.as('milliseconds');

```
支持的可用单位同上。

 \# 获取单位时间
---------

 作为 `Duration#x` 获取器的替代，可以使用 `Duration#get('x')`。

 
```
var duration = dayjs.duration

duration.get('hours');
duration.get('minutes');
duration.get('seconds');
duration.get('milliseconds');

```
支持的可用单位同上。

 \# 序列化为JSON
-----------

 当将时长对象序列化为 JSON 时，它将会表示为 ISO8601 字符串。

 
```
JSON.stringify({
    postDuration : dayjs.duration(5, 'm')
}); // '{"postDuration":"PT5M"}'

```
\# 是否duration
-------------

 要检查变量是否为 Day.js 时长对象，请使用 `dayjs.isDuration`。

 
```
dayjs.isDuration // false
dayjs.isDuration(new Date) // false
dayjs.isDuration(dayjs) // false
dayjs.isDuration(dayjs.duration) // true
dayjs.isDuration(dayjs.duration(2, 'minutes')) // true

```
\# ISO 8601标准
-------------

 返回 ISO 8601  (opens new window) 标准指定的字符串形式的时长。

 
```
dayjs.duration(1, 'd').toISOString // "P1D"

```
格式 `PnYnMnDTnHnMnS` 的说明：

 

| 单位 | 描述 |
| --- | --- |
| P | P 代表周期。 放置在时长表示的开始处。 |
| Y | 年 |
| M | 月 |
| D | 日 |
| T | 在时间分量之前的指示符。 |
| H | 时 |
| M | 分 |
| S | 秒 |

 \# 本地化
------

 您可以使用 locale 获取或设置时长的国际化。 本地化会影响一些方法输出的字符串内容，例如 humanize。 更多关于国际化的信息，请参阅 i18n 部分。

 注意

 此功能依赖 RelativeTime 插件

 
```
require('dayjs/locale/es')

dayjs.duration(1, "minutes").locale("en").humanize; // a minute
dayjs.duration(1, "minutes").locale("es").humanize; // un minuto

```


\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-

Day.js通过支持环境  (opens new window)中的国际化API  (opens new window)支持时区。通过使用内置API，`zero-byte`时区数据需要包含在代码包中。

 对于遗留或不支持的环境，请使用适当的polyfill  (opens new window)。

 Day.js使用了 Internationalization API  (opens new window) 来设置和使用时区。可以在以下 这些环境  (opens new window) 中直接使用。 通过使用内置的 API，无需额外引入时区数据。

 对于旧环境或不支持的环境，请选用合适的 polyfill  (opens new window) 。

 注意

 此功能依赖 Timezone 插件

 
```
dayjs.extend(utc)
dayjs.extend(timezone)

dayjs.tz("2014-06-01 12:00", "America/New_York")

dayjs("2014-06-01 12:00").tz("America/New_York")

```
\# 解析时区
-------

 使用给定时区解析日期时间字符串并返回 Day.js 对象实例。

 注意

 此功能依赖 Timezone 插件

 
```
dayjs.extend(utc)
dayjs.extend(timezone)
dayjs.tz("2013-11-18 11:55", "America/Toronto")

```
如果你知道输入字符串的格式，你可以用它来解析一个日期，参数与 字符串 \+ 格式 完全相同。

 
```
dayjs.extend(customParseFormat)
dayjs.tz("12-25-1995", "MM-DD-YYYY", "America/Toronto")

```
注意

 此功能依赖 CustomParseFormat 插件

 \# 转换到对应时区
----------

 转换到对应时区并更新 UTC 偏移量，返回 Day.js 对象实例。

 注意

 此功能依赖 Timezone 插件

 
```
dayjs.extend(utc)
dayjs.extend(timezone)
dayjs("2013-11-18 11:55").tz("America/Toronto")

```
 \# 用户当前时区
---------

 猜测用户所在时区

 注意

 此功能依赖 Timezone 插件

 
```
dayjs.extend(utc)
dayjs.extend(timezone)
dayjs.tz.guess // America/Chicago

```
\# 设置默认时区
---------

 将默认时区从本地时区变为自定义时区。

 你仍然可以在指定的 dayjs 对象中自定义不同的时区。

 注意

 此功能依赖 Timezone 插件

 
```
dayjs.extend(utc)
dayjs.extend(timezone)

dayjs.tz.setDefault("America/New_York")

// The same behavior with dayjs.tz("2014-06-01 12:00", "America/New_York")
dayjs.tz("2014-06-01 12:00")  // 2014-06-01T12:00:00-04:00

// use another timezone
dayjs.tz("2014-06-01 12:00", "Asia/Tokyo")  // 2014-06-01T12:00:00+09:00

// reset timezone
dayjs.tz.setDefault

```
注意

 `dayjs.tz.setDefault` 不会影响现有的 `dayjs` 对象。



\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-

\# 节假日
======

 ![包图网] Day.js 本身并不支持节假日的判断，不过可以通过三方插件或类库实现，下面列举两个示例：

 \# chinese\-workday
-------------------

 ### \# 安装

   (opens new window)

 
```
npm install chinese-workday

```
### \# 使用

 
```
// const { isWorkday, isHoliday, getFestival } = require('chinese-workday');
const ww = require('chinese-workday');
const isWorkday = ww.isWorkday;
const isHoliday = ww.isHoliday;
const getFestival = ww.getFestival;
const isAddtionalWorkday = ww.isAddtionalWorkday;

isWorkday('2022-10-01')
// => false
isHoliday('2022-10-01')
// => true
isAddtionalWorkday('2022-01-29')
// => true
getFestival('2022-10-01')
// => 国庆节

```
\# chinese\-calendar
--------------------

 ### \# 安装

   (opens new window)

 
```
npm i chinese-calendar

```

```
yarn add chinese-calendar

```
### \# 使用

 浏览器下(基于 webpack 构建)

 
```
import calendar from 'chinese-calendar'
calendar.isHoliday('2020-10-08') // true
calendar.getHolidayDetail('2020-10-07') // ['National Day', '国庆节', 3]

```
NodeJS 下

 
```
const calendar = require('chinese-calendar')
calendar.isHoliday('2020-10-08') // true
calendar.getHolidayDetail('2020-10-07') // ['National Day', '国庆节', 3]

```
\# 更多
-----

 * npm：  (opens new window)
* github：  (opens new window)


