import { ExtendedFormProps } from './form'
import { ExtendedCellRender } from './grid'

/**
 * 表单对话框选项接口
 */
export interface FormDialogOptions {
  /** 对话框是否可见 */
  visible: boolean
  /** 对话框标题 */
  title: string
  /** 对话框宽度 */
  width?: string | number
  /** 对话框高度 */
  height?: string | number
  /** 是否显示关闭按钮 */
  showClose?: boolean
  /** 是否显示底部按钮区域 */
  showFooter?: boolean
  /** 是否显示取消按钮 */
  showCancelButton?: boolean
  /** 是否显示确认按钮 */
  showConfirmButton?: boolean
  /** 取消按钮文本 */
  cancelButtonText?: string
  /** 确认按钮文本 */
  confirmButtonText?: string
  /** 加载状态 */
  loading?: boolean
  /** 表单模式: 'add', 'edit', 'view' */
  mode?: 'add' | 'edit' | 'view'
  /** 表单属性 */
  formProps: ExtendedFormProps
  /** 按钮配置 */
  buttons?: {
    position?: 'left' | 'right' | 'center'
    itemRenders?: ExtendedCellRender<any>[]
  }
}

/**
 * 工具项接口
 */
export interface ToolItem {
  /** 位置: 'left', 'right', 'center' */
  position: 'left' | 'right' | 'center'
  /** 边距 */
  margin?: {
    left?: string
    right?: string
    top?: string
    bottom?: string
  }
  /** 宽度 */
  width?: string
  /** 组件名称 */
  name: string
  /** 组件属性 */
  props?: Record<string, any>
  /** 组件事件 */
  events?: {
    /** 点击事件 */
    click?: (formData: any, formRef: any) => void
    /** 其他事件 */
    [key: string]: ((...args: any[]) => void) | undefined
  }
}
