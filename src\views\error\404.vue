<template>
  <div class="error-container">
    <vxe-card class="error-card">
      <div class="text-center">
        <h2 class="text-9xl font-extrabold text-blue-600">404</h2>
        <h1 class="mt-6 text-3xl font-bold text-gray-900">页面不存在</h1>
        <p class="mt-2 text-sm text-gray-600">
          抱歉，您访问的页面不存在或已被删除。
        </p>
      </div>
      <div class="mt-8 text-center">
        <vxe-button 
          status="primary" 
          @click="$router.push('/dashboard')"
          content="返回首页"
        />
      </div>
    </vxe-card>
  </div>
</template>

<script setup>
// 404 page component
</script>

<style scoped>
.error-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f3f4f6;
  padding: 3rem 1rem;
}

.error-card {
  max-width: 400px;
  margin: 0 auto;
  padding: 2rem;
}
</style> 