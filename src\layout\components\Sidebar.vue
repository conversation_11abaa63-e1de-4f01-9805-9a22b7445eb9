<template>
  <vxe-layout-aside class="h-full bg-white border-r border-gray-200 flex flex-col" :width="collapsed ? 60 : 240" :style="{ transition: 'width 0.3s' }">
    <!-- <div class="flex items-center px-4 py-4 bg-blue-50 border-b border-gray-200">
      <vxe-icon name="home" class="text-blue-500" />
      <span v-if="!collapsed" class="ml-2 text-lg font-semibold text-blue-600">首页</span>
    </div> -->
    <div class="flex-1 overflow-y-auto overflow-x-hidden" style="height: calc(100% - 0px);">
      <vxe-menu 
        v-model="activeMenu" 
        :options="menuOptions"
        class="p-0 block"
        @click="handleMenuClick">
      </vxe-menu>
    </div>
  </vxe-layout-aside>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const props = defineProps({
  collapsed: {
    type: Boolean,
    default: false
  }
})

const route = useRoute()
const router = useRouter()
const openSubmenus = ref(new Set(['system']))
const activeMenu = ref('')

// 导航到指定路径
const navigateTo = (path) => {
  router.push(path)
}

// 检查路径是否激活
const isActive = (path) => {
  if (path === '/dashboard' && (route.path === '/dashboard' || route.path === '/')) {
    return true;
  }
  return route.path === path || route.path.startsWith(path + '/');
}

// 菜单项点击处理
const handleMenuClick = ( { menu, $event }) => {
  if (menu.path) {
    navigateTo(menu.path)
  }
}

// 菜单配置
const menuOptions = computed(() => {
  return [
    // 首页
    { 
      name: 'dashboard', 
      title: '首页', 
      icon: 'home',
      iconClass: 'text-blue-500',
      path: '/dashboard'
    },
    // 示例一（编辑模式）
    {
      name: 'example',
      title: '示例菜单',
      icon: 'edit',
      iconClass: 'text-gray-500',
      expanded: openSubmenus.value.has('example1'),
      children: [
        { 
          name: 'example1-list', 
          title: '列表1',
          path: '/example1/list' 
        },
        { 
          name: 'example2-list', 
          title: '列表2',
          path: '/example2/list' 
        },
        { 
          name: 'example3-list', 
          title: '列表3',
          path: '/example3/list' 
        },
        { 
          name: 'example4-list', 
          title: '列表4',
          path: '/example4/list' 
        }
      ]
    },
    // 系统管理
    {
      name: 'system',
      title: '系统管理',
      icon: 'setting',
      iconClass: 'text-gray-500',
      expanded: openSubmenus.value.has('system'),
      children: [
        { 
          name: 'User', 
          title: '用户管理',
          path: '/system/user' 
        },
        { 
          name: 'Role', 
          title: '角色管理',
          path: '/system/role' 
        },
        {
          name: 'Route',
          title: '路由管理',
          path: '/system/route'
        },
        {
          name: 'Dict',
          title: '字典管理',
          path: '/system/dict'
        },
      ]
    },
    // 关于我们
    { 
      name: 'about', 
      title: '关于我们', 
      icon: 'message',
      iconClass: 'text-gray-500',
      path: '/about'
    }
  ]
})

// 设置当前活动菜单
const setActiveMenu = () => {
  // 根据当前路由设置活动菜单
  const path = route.path === '/' ? '/dashboard' : route.path
  
  for (const menu of menuOptions.value) {
    if (menu.path && isActive(menu.path)) {
      activeMenu.value = menu.name
      return
    }
    
    if (menu.children) {
      for (const submenu of menu.children) {
        if (submenu.path && isActive(submenu.path)) {
          activeMenu.value = submenu.name
          // 确保父菜单展开
          openSubmenus.value.add(menu.name)
          return
        }
      }
    }
  }
}

// 根据当前路由打开相应的子菜单
onMounted(() => {
  // 系统管理
  if (route.path.startsWith('/system')) {
    openSubmenus.value.add('system')
  }

  // 示例菜单
  if (route.path.startsWith('/example1')) {
    openSubmenus.value.add('example1')
  }

  if (route.path.startsWith('/example2')) {
    openSubmenus.value.add('example2')
  }

  if (route.path.startsWith('/example3')) {
    openSubmenus.value.add('example3')
  }

  if (route.path.startsWith('/example4')) {
    openSubmenus.value.add('example4')
  }
  
  // 设置当前活动菜单
  setActiveMenu()
})
</script>