import { VxeUI } from 'vxe-pc-ui'

/**
 * 成功状态
 * @type {string}
 */
export const CODE_SUCCESS: string = '0'
/**
 * 无效Token
 * @type {string}
 */
export const CODE_TOKEN_FAIL: string = '2001'
/**
 * 无效用户
 * @type {string}
 */
export const CODE_USER_FAIL: string = '2002'

/**
 * @description: 校验网络请求状态码
 * @param {Number} status
 * @return void
 */
export const checkStatus = (status: number, message?: string) => {
  switch (status) {
    case 400:
      VxeUI.modal.message({
        content: message || '请求失败！请您稍后重试',
        status: 'error'
      })
      break
    case 401:
      VxeUI.modal.message({
        content: message || '登录失效！请您重新登录',
        status: 'error'
      })
      break
    case 403:
      VxeUI.modal.message({
        content: message || '当前账号无权限访问！',
        status: 'error'
      })
      break
    case 404:
      VxeUI.modal.message({
        content: message || '你所访问的资源不存在！',
        status: 'error'
      })
      break
    case 405:
      VxeUI.modal.message({
        content: message || '请求方式错误！请您稍后重试',
        status: 'error'
      })
      break
    case 408:
      VxeUI.modal.message({
        content: message || '请求超时！请您稍后重试',
        status: 'error'
      })
      break
    case 422:
      VxeUI.modal.message({
        content: message || '请求参数异常！',
        status: 'error'
      })
      break
    case 500:
      VxeUI.modal.message({
        content: message || '服务异常！',
        status: 'error'
      })
      break
    case 502:
      VxeUI.modal.message({
        content: message || '网关错误！',
        status: 'error'
      })
      break
    case 503:
      VxeUI.modal.message({
        content: message || '服务不可用！',
        status: 'error'
      })
      break
    case 504:
      VxeUI.modal.message({
        content: message || '网关超时！',
        status: 'error'
      })
      break
    default:
      VxeUI.modal.message({
        content: message || '请求失败！',
        status: 'error'
      })
  }
}
