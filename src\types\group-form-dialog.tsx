import { ExtendedFormProps } from './form'
import { ExtendedGridProps } from './grid'

/**
 * 组合表单对话框选项接口
 */
export interface GroupFormDialogOptions {
  /** 对话框是否可见 */
  visible: boolean
  /** 对话框标题 */
  title: string
  /** 对话框宽度 */
  width?: string | number
  /** 对话框高度 */
  height?: string | number
  /** 是否显示关闭按钮 */
  showClose?: boolean
  /** 是否显示底部按钮区域 */
  showFooter?: boolean
  /** 是否显示取消按钮 */
  showCancelButton?: boolean
  /** 是否显示确认按钮 */
  showConfirmButton?: boolean
  /** 取消按钮文本 */
  cancelButtonText?: string
  /** 确认按钮文本 */
  confirmButtonText?: string
  /** 对话框内容项数组 */
  items: GroupFormDialogItem[]
  /** 工具栏配置 */
  tools?: ToolItem[]
}

/**
 * 对话框内容项接口
 */
export interface GroupFormDialogItem {
  /** 唯一标识符 */
  id: string
  /** 内容类型: 'form' 或 'grid' */
  type: 'form' | 'grid'
  /** 分组标题 */
  title: string
  /** 是否展开 */
  expanded?: boolean
  /** 最大高度 */
  maxHeight?: string
  /** 最小高度 */
  minHeight?: string
  /** 表单配置，当type为'form'时使用 */
  formOptions?: ExtendedFormProps
  /** 表格配置，当type为'grid'时使用 */
  gridOptions?: ExtendedGridProps
}

/**
 * 工具项接口
 */
export interface ToolItem {
  /** 位置: 'left', 'right', 'center' */
  position: 'left' | 'right' | 'center'
  /** 边距 */
  marign?: {
    left?: string
    right?: string
    top?: string
    bottom?: string
  }
  /** 宽度 */
  width?: string
  /** 组件名称 */
  name: string
  /** 组件属性 */
  props?: Record<string, any>
  /** 组件事件 */
  events?: {
    /** 点击事件 */
    click?: (formData: any, formRef: any) => void
    /** 其他事件 */
    [key: string]: ((...args: any[]) => void) | undefined
  }
}