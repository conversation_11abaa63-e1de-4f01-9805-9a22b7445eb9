<template>
  <vxe-modal
    v-model="formOptions.visible"
    :title="formOptions.title"
    :width="formattedWidth"
    :height="formattedHeight"
    :loading="formOptions.loading"
    :show-close="formOptions.showClose !== false"
    :show-footer="formOptions.showFooter !== false"
    :show-cancel-button="formOptions.showCancelButton !== false"
    :show-confirm-button="formOptions.showConfirmButton !== false"
    :cancel-button-text="formOptions.cancelButtonText || '取消'"
    :confirm-button-text="formOptions.confirmButtonText || '确定'"
    @confirm="handleConfirm"
    @cancel="handleCancel"
    destroy-on-close
    resize
    show-zoom
  >
    <div class="form-dialog-container">
      <bm-form
        ref="formRef"
        :form-options="computedFormProps"
        v-model="formData"
        :loading="formOptions.loading"
        @submit="handleSubmit"
      />

      <div v-if="formOptions.buttons?.itemRenders && formOptions.buttons.itemRenders.length > 0" class="buttons-container">
        <template v-for="(button, index) in formOptions.buttons.itemRenders" :key="index">
          <vxe-button v-bind="button.props" v-on="getButtonEvents(button)">
            {{ button.name }}
          </vxe-button>
        </template>
      </div>
    </div>
  </vxe-modal>
</template>

<script lang="ts" setup>
import { ref, watch, computed, onMounted } from 'vue'
import XEUtils from 'xe-utils'
import { FormDialogOptions } from '@/types/form-dialog'
import BmForm from './BmForm.vue'
import { VxeUI } from 'vxe-pc-ui'
import { VxeButton } from 'vxe-pc-ui'

interface Props {
  formOptions: FormDialogOptions
}

const props = withDefaults(defineProps<Props>(), {
  formOptions: () => ({
    visible: false,
    title: '',
    width: '60%',
    height: '50%',
    loading: false,
    mode: 'add',
    formProps: {
      data: {},
      getData: () => ({}),
      applyData: () => {},
      formType: 'edit',
      items: []
    }
  })
})

const emit = defineEmits<{
  (e: 'confirm', data: any): void
  (e: 'cancel'): void
  (e: 'update:formOptions', options: FormDialogOptions): void
}>()

// 表单引用
const formRef = ref()
const formData = ref<any>(XEUtils.clone(props.formOptions.formProps?.data || {}, true))

// 监听formProps.data变化
watch(() => props.formOptions.formProps?.data, (newVal) => {
  if (props.formOptions.mode !== 'add') {
    formData.value = XEUtils.clone(newVal, true)
  }
}, { deep: true })

// 格式化宽度，支持像素值和百分比
const formattedWidth = computed(() => {
  const width = props.formOptions.width
  if (!width) return '800px' // 默认宽度

  // 如果已经是字符串且包含单位（px 或 %），直接返回
  if (typeof width === 'string' && (width.includes('px') || width.includes('%'))) {
    return width
  }

  // 如果是数字或不带单位的字符串，添加 px 单位
  return `${width}px`
})

// 格式化高度，支持像素值和百分比
const formattedHeight = computed(() => {
  const height = props.formOptions.height
  if (!height) return undefined // 默认高度（由组件自动计算）

  // 如果已经是字符串且包含单位（px 或 %），直接返回
  if (typeof height === 'string' && (height.includes('px') || height.includes('%'))) {
    return height
  }

  // 如果是数字或不带单位的字符串，添加 px 单位
  return `${height}px`
})

// 计算最终的表单属性
const computedFormProps = computed(() => {
  // 确保 formType 是有效的值
  const mode = props.formOptions.mode || 'edit';

  return {
    ...props.formOptions.formProps,
    data: mode === 'add' ? {} : formData.value,
    disabled: mode === 'view',
    formType: mode
  };
})

// 不需要组件映射，因为我们只使用 VxeButton

// 按钮事件处理器
const getButtonEvents = (button: any) => {
  const events: Record<string, any> = {}

  if (button.events) {
    Object.entries(button.events).forEach(([eventName, handler]) => {
      events[eventName] = async (...args: any[]) => {
        // 获取表单实例和数据
        const formInstance = formRef.value
        // 调用事件处理函数
        if (typeof handler === 'function') {
          await handler(formInstance.getFormData(), formInstance, ...args)
        }
      }
    })
  }

  return events
}

// 处理提交
const handleSubmit = async () => {
  try {
    const form = formRef.value
    await form?.formRef?.validate()

    // 检查 applyData 是否是一个函数并且不是默认的空函数
    const hasApplyData = typeof props.formOptions.formProps.applyData === 'function' &&
                         props.formOptions.formProps.applyData.toString() !== '() => {}';

    if (hasApplyData) {
      const result = await form?.submitForm()
      if (result) {
        VxeUI.modal.message({ content: '保存成功', status: 'success' })
        setVisible(false)
        emit('confirm', result)
      }
    } else {
      const formData = form?.getFormData()
      emit('confirm', formData)
      setVisible(false)
    }
  } catch (error) {
    console.error('表单验证失败:', error)
    VxeUI.modal.message({ content: '表单验证失败', status: 'error' })
  }
}

// 确认按钮处理
const handleConfirm = async () => {
  await handleSubmit()
}

// 取消按钮处理
const handleCancel = () => {
  setVisible(false)
  emit('cancel')
}

// 设置对话框可见性
const setVisible = (visible: boolean) => {
  const newOptions = {
    ...props.formOptions,
    visible: visible
  }
  emit('update:formOptions', newOptions)
}

// 设置对话框宽度
const setWidth = (width: string | number) => {
  const newOptions = {
    ...props.formOptions,
    width: width
  }
  emit('update:formOptions', newOptions)
}

// 设置对话框高度
const setHeight = (height: string | number) => {
  const newOptions = {
    ...props.formOptions,
    height: height
  }
  emit('update:formOptions', newOptions)
}

// 同时设置对话框宽度和高度
const setSize = (width: string | number, height: string | number) => {
  const newOptions = {
    ...props.formOptions,
    width: width,
    height: height
  }
  emit('update:formOptions', newOptions)
}

// 获取表单数据
const getFormData = () => {
  if (formRef.value && formRef.value.getFormData) {
    return formRef.value.getFormData()
  }
  return {}
}

// 初始化组件
onMounted(() => {
  // 初始化表单数据
  if (props.formOptions.formProps?.data) {
    formData.value = XEUtils.clone(props.formOptions.formProps.data, true)
  }
})

// 暴露方法
defineExpose({
  formRef,
  setVisible,
  setWidth,
  setHeight,
  setSize,
  getFormData
})
</script>

<style scoped>
.form-dialog-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  height: 100%;
  overflow-y: auto;
}

.bm-form {
  flex: 1;
  overflow: auto;
}

.buttons-container {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  margin-top: 16px;
}
</style>
