import type { Node, Position, ConnectionLineType } from '@vue-flow/core'
import type { ExtendedFormProps } from './form'

/**
 * BmFlow 组件配置接口
 */
export interface BmFlowConfig {
  /** 节点配置数组 */
  nodes?: BmFlowNode[]
  /** 边配置数组 */
  edges?: BmFlowEdge[]
  /** 流程图配置选项 */
  options?: BmFlowOptions
  /** 工具栏配置 */
  toolbar?: BmFlowToolbar
  /** 右键对话框配置 */
  rightClickDialog?: BmFlowRightClickDialog
}

/**
 * BmFlow 节点配置接口
 */
export interface BmFlowNode extends Omit<Node, 'data'> {
  /** 节点唯一标识 */
  id: string
  /** 节点类型 */
  type?: 'default' | 'input' | 'output' | 'bmForm' | string
  /** 节点位置 */
  position: { x: number; y: number }
  /** 节点数据 */
  data: BmFlowNodeData
  /** 节点样式 */
  style?: Record<string, any>
  /** 节点类名 */
  class?: string
  /** 是否可拖拽 */
  draggable?: boolean
  /** 是否可选择 */
  selectable?: boolean
  /** 是否可连接 */
  connectable?: boolean
  /** 源连接点位置 */
  sourcePosition?: Position
  /** 目标连接点位置 */
  targetPosition?: Position
}

/**
 * BmFlow 节点数据接口
 */
export interface BmFlowNodeData {
  /** 节点标签 */
  label?: string
  /** 节点描述 */
  description?: string
  /** 节点图标 */
  icon?: string
  /** BmForm 配置（当节点类型为 bmForm 时使用） */
  formConfig?: ExtendedFormProps
  /** 自定义数据 */
  [key: string]: any
}

/**
 * BmFlow 边配置接口
 */
export interface BmFlowEdge {
  /** 边唯一标识 */
  id: string
  /** 源节点ID */
  source: string
  /** 目标节点ID */
  target: string
  /** 边类型 */
  type?: 'default' | 'step' | 'smoothstep' | 'straight' | string
  /** 边标签 */
  label?: string
  /** 边样式 */
  style?: Record<string, any>
  /** 是否显示箭头 */
  markerEnd?: string
  /** 是否可选择 */
  selectable?: boolean
  /** 是否可删除 */
  deletable?: boolean
  /** 边数据 */
  data?: Record<string, any>
}

/**
 * BmFlow 配置选项接口
 */
export interface BmFlowOptions {
  /** 是否显示网格背景 */
  showBackground?: boolean
  /** 背景类型 */
  backgroundType?: 'dots' | 'lines' | 'cross'
  /** 背景颜色 */
  backgroundColor?: string
  /** 是否显示控制面板 */
  showControls?: boolean
  /** 是否显示小地图 */
  showMinimap?: boolean
  /** 是否启用缩放 */
  zoomOnScroll?: boolean
  /** 是否启用平移 */
  panOnScroll?: boolean
  /** 是否启用拖拽 */
  nodesDraggable?: boolean
  /** 是否启用连接 */
  nodesConnectable?: boolean
  /** 是否启用删除 */
  elementsSelectable?: boolean
  /** 最小缩放比例 */
  minZoom?: number
  /** 最大缩放比例 */
  maxZoom?: number
  /** 默认缩放比例 */
  defaultZoom?: number
  /** 是否适应视图 */
  fitView?: boolean
  /** 连接线类型 */
  connectionLineType?: ConnectionLineType
  /** 连接线样式 */
  connectionLineStyle?: Record<string, any>
  /** 网格大小 */
  snapToGrid?: boolean
  /** 网格间距 */
  snapGrid?: [number, number]
}

/**
 * BmFlow 工具栏配置接口
 */
export interface BmFlowToolbar {
  /** 是否显示工具栏 */
  show?: boolean
  /** 工具栏位置 */
  position?: 'top' | 'bottom' | 'left' | 'right'
  /** 工具按钮配置 */
  tools?: BmFlowToolItem[]
  /** 工具按钮配置（别名） */
  items?: BmFlowToolItem[]
}

/**
 * BmFlow 工具项配置接口
 */
export interface BmFlowToolItem {
  /** 工具名称 */
  name: string
  /** 工具标签 */
  label?: string
  /** 工具标题 */
  title?: string
  /** 工具图标 */
  icon?: string
  /** 工具类型 */
  type?: 'button' | 'separator' | 'dropdown'
  /** 点击事件 */
  onClick?: (flowInstance: any) => void
  /** 是否禁用 */
  disabled?: boolean
  /** 子工具项（用于下拉菜单） */
  children?: BmFlowToolItem[]
}

/**
 * BmFlow 右键对话框配置接口
 */
export interface BmFlowRightClickDialog {
  /** 是否启用右键对话框 */
  enabled?: boolean
  /** 对话框宽度 */
  width?: string | number
  /** 对话框高度 */
  height?: string | number
  /** 对话框标题 */
  title?: string
  /** 表单配置 */
  formConfig: ExtendedFormProps
  /** 对话框样式 */
  style?: Record<string, any>
  /** 对话框类名 */
  class?: string
}

/**
 * BmFlow 事件接口
 */
export interface BmFlowEvents {
  /** 节点点击事件 */
  onNodeClick?: (event: any, node: BmFlowNode) => void
  /** 节点双击事件 */
  onNodeDoubleClick?: (event: any, node: BmFlowNode) => void
  /** 节点右键事件 */
  onNodeContextMenu?: (event: any, node: BmFlowNode) => void
  /** 节点拖拽开始事件 */
  onNodeDragStart?: (event: any, node: BmFlowNode) => void
  /** 节点拖拽事件 */
  onNodeDrag?: (event: any, node: BmFlowNode) => void
  /** 节点拖拽结束事件 */
  onNodeDragStop?: (event: any, node: BmFlowNode) => void
  /** 边点击事件 */
  onEdgeClick?: (event: any, edge: BmFlowEdge) => void
  /** 连接事件 */
  onConnect?: (connection: any) => void
  /** 连接开始事件 */
  onConnectStart?: (event: any, params: any) => void
  /** 连接结束事件 */
  onConnectEnd?: (event: any) => void
  /** 元素删除事件 */
  onElementsRemove?: (elements: any[]) => void
  /** 画布点击事件 */
  onPaneClick?: (event: any) => void
  /** 画布右键事件 */
  onPaneContextMenu?: (event: any) => void
}

/**
 * BmFlow 组件属性接口
 */
export interface BmFlowProps {
  /** 流程图配置 */
  config: BmFlowConfig
  /** 事件配置 */
  events?: BmFlowEvents
  /** 组件样式 */
  style?: Record<string, any>
  /** 组件类名 */
  class?: string
  /** 是否只读模式 */
  readonly?: boolean
}

/**
 * BmFlow 实例方法接口
 */
export interface BmFlowInstance {
  /** 获取所有节点 */
  getNodes: () => BmFlowNode[]
  /** 获取所有边 */
  getEdges: () => BmFlowEdge[]
  /** 添加节点 */
  addNode: (node: BmFlowNode) => void
  /** 删除节点 */
  removeNode: (nodeId: string) => void
  /** 更新节点 */
  updateNode: (nodeId: string, updates: Partial<BmFlowNode>) => void
  /** 添加边 */
  addEdge: (edge: BmFlowEdge) => void
  /** 删除边 */
  removeEdge: (edgeId: string) => void
  /** 更新边 */
  updateEdge: (edgeId: string, updates: Partial<BmFlowEdge>) => void
  /** 适应视图 */
  fitView: () => void
  /** 缩放到指定比例 */
  zoomTo: (zoom: number) => void
  /** 获取当前缩放比例 */
  getZoom: () => number
  /** 获取视图中心 */
  getViewport: () => { x: number; y: number; zoom: number }
  /** 设置视图中心 */
  setViewport: (viewport: { x: number; y: number; zoom: number }) => void
  /** 导出为图片 */
  toObject: () => { nodes: BmFlowNode[]; edges: BmFlowEdge[] }
}