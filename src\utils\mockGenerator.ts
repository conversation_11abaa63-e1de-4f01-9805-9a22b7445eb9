// Mock data generator utility
import dayjs from 'dayjs'

// Field type definitions
export type FieldType = 
  'date' | 
  'datetime' |
  'boolean' |
  'number' |
  'string' |
  'array' |
  'null'

// Field configuration interface
export interface FieldConfig {
  fieldName: string
  fieldType: FieldType
  length?: number
  options?: any
  custom?: () => any
}

class MockGenerator {
  // Generate a random integer between min and max (inclusive)
  private getRandomInt(min: number, max: number): number {
    min = Math.ceil(min)
    max = Math.floor(max)
    return Math.floor(Math.random() * (max - min + 1)) + min
  }

  // Get a random item from an array
  private getRandomItem<T>(array: T[]): T {
    return array[Math.floor(Math.random() * array.length)]
  }

  // Get multiple random items from an array
  private getRandomItems<T>(array: T[], count: number): T[] {
    const shuffled = [...array].sort(() => 0.5 - Math.random())
    return shuffled.slice(0, count)
  }

  // Generate a random date within the last 3 years
  private getRandomDate(format = 'YYYY-MM-DD'): string {
    const now = new Date()
    const pastDate = new Date(now)
    pastDate.setFullYear(now.getFullYear() - this.getRandomInt(0, 3))
    pastDate.setMonth(this.getRandomInt(0, 11))
    pastDate.setDate(this.getRandomInt(1, 28))
    
    return dayjs(pastDate).format(format)
  }

  // Generate a random datetime within the last 3 years
  private getRandomDateTime(): string {
    const date = this.getRandomDate('YYYY-MM-DD')
    const hours = this.getRandomInt(0, 23).toString().padStart(2, '0')
    const minutes = this.getRandomInt(0, 59).toString().padStart(2, '0')
    const seconds = this.getRandomInt(0, 59).toString().padStart(2, '0')
    
    return `${date} ${hours}:${minutes}:${seconds}`
  }

  // Generate random string
  private getRandomString(length = 10): string {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    let result = ''
    for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * characters.length))
    }
    return result
  }

  // Generate random array of strings or numbers
  private getRandomArray(length = 5): (string | number)[] {
    const result: (string | number)[] = []
    for (let i = 0; i < length; i++) {
      if (Math.random() > 0.5) {
        result.push(this.getRandomString(5))
      } else {
        result.push(this.getRandomInt(1, 100))
      }
    }
    return result
  }

  // Generate a single field value based on type
  private generateFieldValue(field: FieldConfig): any {
    switch(field.fieldType) {
      case 'date':
        return this.getRandomDate()
      case 'datetime':
        return this.getRandomDateTime()
      case 'boolean':
        return Math.random() > 0.5
      case 'number':
        return this.getRandomInt(field.options?.min || 0, field.options?.max || 1000)
      case 'string':
        return this.getRandomString(field.length || 10)
      case 'array':
        return this.getRandomArray(field.length || 5)
      case 'null':
        return null
      default:
        return null
    }
  }

  // Generate mock data based on field definitions and count
  generateMockData(fields: FieldConfig[], count = 10): Record<string, any>[] {
    const result: Record<string, any>[] = []

    for (let i = 0; i < count; i++) {
      const item: Record<string, any> = {}
      
      fields.forEach(field => {
        item[field.fieldName] = this.generateFieldValue(field)
      })
      
      result.push(item)
    }

    return result
  }

  // Generate a response object with mock data
  generateMockResponse(fields: FieldConfig[], count = 10, options = { code: 200, message: 'Success' }): any {
    return {
      code: options.code,
      message: options.message,
      data: this.generateMockData(fields, count)
    }
  }

  generateMockTreeData(fields: FieldConfig[], childrenField: string, treeLevel = 3, count = 10): any[] {
    // Generate the root level nodes
    return this._generateTreeLevel(fields, childrenField, 1, treeLevel, count)
  }

  // Private recursive helper to generate tree data
  private _generateTreeLevel(
    fields: FieldConfig[], 
    childrenField: string, 
    currentLevel: number, 
    maxLevel: number, 
    count: number
  ): any[] {
    // Generate data for current level
    const items = this.generateMockData(fields, count)
    
    // If we haven't reached max depth, add children
    if (currentLevel < maxLevel) {
      // Calculate fewer children for each deeper level
      const childCount = Math.max(2, Math.floor(count / currentLevel))
      
      // Add children to each item
      items.forEach(item => {
        // Recursively generate child nodes
        item[childrenField] = this._generateTreeLevel(
          fields, 
          childrenField, 
          currentLevel + 1, 
          maxLevel, 
          childCount
        )
      })
    } else {
      // For leaf nodes, add empty children array
      items.forEach(item => {
        item[childrenField] = []
      })
    }
    
    return items
  }
}

export const mockGenerator = new MockGenerator()