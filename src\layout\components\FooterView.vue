<template>
  <div class="footer-view">
    <p class="footer-text">
      Copyright © {{ currentYear }} VXE Admin Template. All Rights Reserved.
    </p>
  </div>
</template>

<script setup>
import { computed } from 'vue'

// Get current year for copyright
const currentYear = computed(() => new Date().getFullYear())
</script>

<style scoped>
.footer-view {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.footer-text {
  color: #6b7280;
  font-size: 0.875rem;
}
</style>
