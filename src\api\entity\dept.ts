import type { IPage } from './index'

// 部门实体接口
export interface IDept {
  deptId: string
  createUserid: string
  createDate: string
  updateUserid: string
  updateDate: string
  isFlag: boolean
  rootId: string
  pid: string
  sname: string
  alias: string
  belongId: string
  deptNo: string
  creditCode: string
  remark: string
  level: boolean
  sort: string
  status: boolean
}

// 部门分页查询参数接口
export interface IDeptQuery {
  pageNumber?: number
  pageSize?: number
  sname?: string
  deptNo?: string
  [key: string]: any
}

// 部门列表接口
export interface IDeptList extends IPage<IDept> {
  records: IDept[]
}
