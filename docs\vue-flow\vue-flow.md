TITLE: Initializing Vue Flow with useVueFlow (Vue)
DESCRIPTION: Demonstrates how to use the `useVueFlow` composable within a Vue setup script to access graph methods like `onInit`, `findNode`, `fitView`, and `snapToGrid`. It shows basic graph initialization, node manipulation, and grid snapping.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/composables.md#_snippet_0

LANGUAGE: javascript
CODE:
```
import { ref } from 'vue'
import { useVueFlow, VueFlow } from '@vue-flow/core'

const { onInit, findNode, fitView, snapToGrid } = useVueFlow()

const nodes = ref([/* ... */])

const edges = ref([/* ... */])

// to enable snapping to grid
snapToGrid.value = true

// any event that is emitted from the <VueFlow /> component can be listened to using the `onEventName` method
onInit((instance) => {
  // `instance` is the same type as the return of `useVueFlow` (VueFlowStore)
  
  fitView()
  
  const node = findNode('1')
  
  if (node) {
    node.position = { x: 100, y: 100 }
  }
})
```

LANGUAGE: html
CODE:
```
<template>
  <VueFlow :nodes="nodes" :edges="edges" />
</template>
```

----------------------------------------

TITLE: Installing Vue Flow Core Package - Bash
DESCRIPTION: Provides command-line instructions for installing the `@vue-flow/core` package using different package managers: npm, pnpm, and yarn. This package is the core dependency for using the Vue Flow component in a project.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/README.md#_snippet_0

LANGUAGE: bash
CODE:
```
$ npm i @vue-flow/core

# or
$ pnpm i @vue-flow/core

# or
$ yarn add @vue-flow/core
```

----------------------------------------

TITLE: Accessing State and Handling Events with useVueFlow (Composition API)
DESCRIPTION: Demonstrates using `useVueFlow` within a Vue 3 `<script setup>` component to access state properties like `getNodes`, register event handlers like `onPaneReady` to fit the view, and reactively watch state changes using Vue's `watch` function.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/vue-flow/state.md#_snippet_0

LANGUAGE: vue
CODE:
```
<script setup>
import { useVueFlow } from '@vue-flow/core'

const { getNodes, onPaneReady } = useVueFlow()

// event handler
onPaneReady((i) => i.fitView())

// watch the stored nodes
watch(getNodes, (nodes) => console.log('nodes changed', nodes))
</script>
```

----------------------------------------

TITLE: Listening to Events using useVueFlow Hook (Vue)
DESCRIPTION: Illustrates how to use the `useVueFlow` composition API hook to access dedicated event listener functions (e.g., `onNodeClick`, `onEdgeClick`). These functions are called within the script setup to register event handlers, providing an alternative method to component listeners. Handlers receive the same event details object.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/vue-flow/events.md#_snippet_1

LANGUAGE: Vue
CODE:
```
<script setup>\nimport { ref } from 'vue';\nimport { VueFlow, useVueFlow } from '@vue-flow/core';\n\nconst nodes = ref([/* ... */]);\nconst edges = ref([/* ... */]);\n\n// All events are available from `useVueFlow` as `on<EventName>`\nconst { onNodeClick, onEdgeClick } = useVueFlow();\n\n// Node click event handler\nonNodeClick(({ event, node }) => {\n  console.log('Node clicked:', node, event);\n});\n\n// Edge click event handler\nonEdgeClick(({ event, edge }) => {\n  console.log('Edge clicked:', edge, event);\n});\n<\/script>\n\n<template>\n  <VueFlow :nodes="nodges" :edges="edges" @node-click="onNodeClick" @edge-click="onEdgeClick"><\/VueFlow>\n<\/template>
```

----------------------------------------

TITLE: Validating and Applying Changes Vue Flow Vue/JavaScript
DESCRIPTION: This comprehensive example combines disabling default changes (`apply-default="false"`), listening to `onNodesChange`, validating 'remove' type changes with a confirmation prompt, and then manually applying only the validated changes using `applyNodeChanges`. This demonstrates how to implement custom logic before updating the flow state.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/controlled-flow.md#_snippet_5

LANGUAGE: javascript
CODE:
```
import { ref } from 'vue';
import { useVueFlow, VueFlow } from '@vue-flow/core';

const { applyNodeChanges } = useVueFlow();

// Assuming 'useConfirm' is a hypothetical composable for confirmation dialogs
const { confirm } = useConfirm();

const nodes = ref([
  {
    id: '1',
    position: { x: 0, y: 0 },
    data: { label: 'Node 1' },
  },
  {
    id: '2',
    position: { x: 100, y: 100 },
    data: { label: 'Node 2' },
  },
]);

const edges = ref([
  {
    id: 'e1->2',
    source: '1',
    target: '2',
  },
]);

const onNodesChange = async (changes) => {
  const nextChanges = [];

  for (const change of changes) {
    if (change.type === 'remove') {
      const isConfirmed = await confirm('Are you sure you want to delete this node?');

      if (isConfirmed) {
        nextChanges.push(change);
      }
    } else {
      nextChanges.push(change);
    }
  }

  applyNodeChanges(nextChanges);
};
```

LANGUAGE: vue
CODE:
```
<template>
  <VueFlow :nodes="nodes" :edges="edges" :apply-default="false" @nodes-change="onNodesChange" />
</template>
```

----------------------------------------

TITLE: Implementing Basic Flowchart - Vue
DESCRIPTION: Illustrates a basic Vue 3 component (`Flowchart.vue`) using the Vue Flow library. It demonstrates importing the component, defining initial nodes with unique IDs and positions, and defining edges connecting nodes by their IDs. The `VueFlow` component is then rendered in the template, bound to the `nodes` and `edges` data.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/README.md#_snippet_1

LANGUAGE: vue
CODE:
```
<!-- Flowchart.vue -->
<script setup>
import { ref } from 'vue'
import { VueFlow } from '@vue-flow/core'

const nodes = ref([
  { id: '1', type: 'input', label: 'Node 1', position: { x: 250, y: 5 } },
  { id: '2', label: 'Node 2', position: { x: 100, y: 100 } },
  { id: '3', label: 'Node 3', position: { x: 400, y: 100 } },
  { id: '4', label: 'Node 4', position: { x: 400, y: 200 } },
])

const edges = ref([
  { id: 'e1-2', source: '1', target: '2', animated: true },
  { id: 'e1-3', source: '1', target: '3' },
])
</script>

<template>
  <VueFlow v-model:nodes="nodes" v-model:edges="edges"></VueFlow>
</template>
```

----------------------------------------

TITLE: Providing Initial Nodes to VueFlow (Vue)
DESCRIPTION: This snippet shows how to define an array of node objects with unique IDs, types, positions, and data using `ref` from Vue. It then passes this reactive array to the `<VueFlow>` component using the `:nodes` prop to render the initial graph nodes.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/vue-flow/config.md#_snippet_3

LANGUAGE: vue
CODE:
```
<script setup>
import { ref } from 'vue'  
import { VueFlow } from '@vue-flow/core'

const nodes = ref([
  { 
    id: '1', 
    type: 'input',
    position: { x: 250, y: 5 },
    data: { label: 'Node 1' },
  },
  { 
    id: '2', 
    position: { x: 100, y: 100 },
    data: { label: 'Node 2' },
  },
  { 
    id: '3', 
    position: { x: 400, y: 100 },
    data: { label: 'Node 3' },
  },
  { 
    id: '4', 
    type: 'output',
    position: { x: 400, y: 200 },
    data: { label: 'Node 4' },
  },
])
</script>
<template>
  <VueFlow :nodes="nodes" />
</template>
```

----------------------------------------

TITLE: Installing Vue Flow Core with npm
DESCRIPTION: Installs the `@vue-flow/core` package using the npm package manager. This adds the core Vue Flow library to your project dependencies. Requires Node.js and npm to be installed.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/getting-started.md#_snippet_0

LANGUAGE: Shell
CODE:
```
npm add @vue-flow/core
```

----------------------------------------

TITLE: Providing Initial Nodes and Edges to VueFlow (Vue)
DESCRIPTION: This snippet shows how to define reactive arrays for both nodes and edges using `ref`. Each edge specifies its `source` and `target` node IDs. The snippet then passes both arrays to the `<VueFlow>` component using the `:nodes` and `:edges` props to render a graph with connections.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/vue-flow/config.md#_snippet_4

LANGUAGE: vue
CODE:
```
<script setup>
import { VueFlow } from '@vue-flow/core'

const nodes = ref([
  {
    id: '1',
    type: 'input',
    position: { x: 250, y: 5 },
    data: { label: 'Node 1' },
  },
  {
    id: '2',
    position: { x: 100, y: 100 },
    data: { label: 'Node 2' },
  },
  {
    id: '3',
    position: { x: 400, y: 100 },
    data: { label: 'Node 3' },
  },
  {
    id: '4',
    type: 'output',
    position: { x: 400, y: 200 },
    data: { label: 'Node 4' },
  },
])

const edges = ref([
  { 
    id: 'e1->3', 
    source: '1',
    target: '3' 
  },
  { 
    id: 'e1->2', 
    source: '1', 
    target: '2',  
  },
])
</script>
<template>
  <VueFlow :nodes="nodes" :edges="edges" />
</template>
```

----------------------------------------

TITLE: Registering Custom Edge Types in VueFlow (Vue)
DESCRIPTION: This snippet shows how to define custom edge components (like `CustomEdge.vue`) and register them with Vue Flow. An object `edgeTypes` maps a string key ('custom') to the component, and this object is passed to the `<VueFlow>` component via the `:edge-types` prop, allowing edges with `type: 'custom'` to use this component.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/vue-flow/config.md#_snippet_7

LANGUAGE: vue
CODE:
```
<script setup>
import { ref } from 'vue'  
import { VueFlow } from '@vue-flow/core'
import CustomEdge from './CustomEdge.vue'

const edgeTypes = {
  custom: CustomEdge,
}

const nodes = ref([
  {
    id: '1',
    type: 'custom',
    position: { x: 250, y: 5 },
    data: { label: 'Node 1' },
  },
  {
    id: '2',
    position: { x: 100, y: 100 },
    data: { label: 'Node 2' },
  },
])

const edges = ref([
  { 
    id: 'e1->2', 
    type: 'custom',
    source: '1', 
    target: '2' 
  },
])
</script>
<template>
  <VueFlow :nodes="nodes" :edges="edges" :edge-types="edgeTypes" />
</template>
```

----------------------------------------

TITLE: Defining Nodes and Edges - App.vue (JS)
DESCRIPTION: This Vue component demonstrates how to define a graph consisting of nodes and edges using the Composition API with JavaScript. It uses `ref` to make the node and edge arrays reactive and includes examples of input, output, default, and custom node/edge types, showing how to add positional data and custom data.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/getting-started.md#_snippet_4

LANGUAGE: javascript
CODE:
```
<script setup>
import { ref } from 'vue'
import { VueFlow } from '@vue-flow/core'

// these components are only shown as examples of how to use a custom node or edge
// you can find many examples of how to create these custom components in the examples page of the docs
import SpecialNode from './components/SpecialNode.vue'
import SpecialEdge from './components/SpecialEdge.vue'

// these are our nodes
const nodes = ref([
  // an input node, specified by using `type: 'input'`
  { 
    id: '1',
    type: 'input', 
    position: { x: 250, y: 5 },
    // all nodes can have a data object containing any data you want to pass to the node
    // a label can property can be used for default nodes
    data: { label: 'Node 1' },
  },

  // default node, you can omit `type: 'default'` as it's the fallback type
  { 
    id: '2', 
    position: { x: 100, y: 100 },
    data: { label: 'Node 2' },
  },

  // An output node, specified by using `type: 'output'`
  { 
    id: '3', 
    type: 'output', 
    position: { x: 400, y: 200 },
    data: { label: 'Node 3' },
  },

  // this is a custom node
  // we set it by using a custom type name we choose, in this example `special`
  // the name can be freely chosen, there are no restrictions as long as it's a string
  {
    id: '4',
    type: 'special', // <-- this is the custom node type name
    position: { x: 400, y: 200 },
    data: {
      label: 'Node 4',
      hello: 'world',
    },
  },
])

// these are our edges
const edges = ref([
  // default bezier edge
  // consists of an edge id, source node id and target node id
  { 
    id: 'e1->2',
    source: '1', 
    target: '2',
  },

  // set `animated: true` to create an animated edge path
  { 
    id: 'e2->3',
    source: '2', 
    target: '3', 
    animated: true,
  },

  // a custom edge, specified by using a custom type name
  // we choose `type: 'special'` for this example
  {
    id: 'e3->4',
    type: 'special',
    source: '3',
    target: '4',

    // all edges can have a data object containing any data you want to pass to the edge
    data: {
      hello: 'world',
    }
  },
])
</script>
```

LANGUAGE: html
CODE:
```
<template>
  <VueFlow :nodes="nodes" :edges="edges">
    <!-- bind your custom node type to a component by using slots, slot names are always `node-<type>` -->
    <template #node-special="specialNodeProps">
      <SpecialNode v-bind="specialNodeProps" />
    </template>

    <!-- bind your custom edge type to a component by using slots, slot names are always `edge-<type>` -->
    <template #edge-special="specialEdgeProps">
      <SpecialEdge v-bind="specialEdgeProps" />
    </template>
  </VueFlow>
</template>
```

LANGUAGE: css
CODE:
```
<style>
/* import the necessary styles for Vue Flow to work */
@import '@vue-flow/core/dist/style.css';

/* import the default theme, this is optional but generally recommended */
@import '@vue-flow/core/dist/theme-default.css';
</style>
```

----------------------------------------

TITLE: Adding Nodes Using useVueFlow Action Vue
DESCRIPTION: These Vue snippets illustrate how to add single or multiple nodes using the `addNodes` action obtained from the `useVueFlow` composable. This method directly interacts with the internal Vue Flow state and is useful for managing the graph from outside the component.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/node.md#_snippet_4

LANGUAGE: Vue
CODE:
```
<script setup>
import { ref } from 'vue'
import { Panel, VueFlow, useVueFlow } from '@vue-flow/core'

const initialNodes = ref([
  {
    id: '1',
    position: { x: 50, y: 50 },
    data: { label: 'Node 1' },
  }
])
const { addNodes } = useVueFlow()

function generateRandomNode() {
  return {
    id: Math.random().toString(),
    position: { x: Math.random() * 500, y: Math.random() * 500 },
    label: 'Random Node',
  }
}

function onAddNode() {
  // add a single node to the graph
  addNodes(generateRandomNode())
}

function onAddNodes() {
  // add multiple nodes to the graph
  addNodes(Array.from({ length: 10 }, generateRandomNode))
}
</script>

<template>
  <VueFlow :nodes="initialNodes">
    <Panel>
      <button type="button" @click="onAddNode">Add a node</button>
      <button type="button" @click="onAddNodes">Add multiple nodes</button>
    </Panel>
  </VueFlow>
</template>
```

LANGUAGE: Vue
CODE:
```
<script setup lang="ts">
import { ref } from 'vue'  
import type { Node } from '@vue-flow/core'  
import { Panel, VueFlow, useVueFlow } from '@vue-flow/core'

const initialNodes = ref<Node[]>([
  {
    id: '1',
    position: { x: 50, y: 50 },
    data: { label: 'Node 1' },
  }
])
const { addNodes } = useVueFlow()

function generateRandomNode() {
  return {
    id: Math.random().toString(),
    position: { x: Math.random() * 500, y: Math.random() * 500 },
    label: 'Random Node',
    data: { 
      hello: 'world',
    }
  }
}

function onAddNode() {
  // add a single node to the graph
  addNodes(generateRandomNode())
}

function onAddNodes() {
  // add multiple nodes to the graph
  addNodes(Array.from({ length: 10 }, generateRandomNode))
}
</script>

<template>
  <VueFlow :nodes="initialNodes">
    <Panel>
      <button type="button" @click="onAddNode">Add a node</button>
      <button type="button" @click="onAddNodes">Add multiple nodes</button>
    </Panel>
  </VueFlow>
</template>
```

----------------------------------------

TITLE: Registering Custom Node Types in VueFlow (Vue)
DESCRIPTION: This snippet shows how to define custom node components (like `CustomNode.vue`) and register them with Vue Flow. An object `nodeTypes` maps a string key ('custom') to the component, and this object is passed to the `<VueFlow>` component via the `:node-types` prop, allowing nodes with `type: 'custom'` to use this component.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/vue-flow/config.md#_snippet_6

LANGUAGE: vue
CODE:
```
<script setup>
import { ref } from 'vue'  
import { VueFlow } from '@vue-flow/core'
import CustomNode from './CustomNode.vue'

const nodeTypes = {
  custom: CustomNode,
}

const nodes = ref([
  { 
    id: '1', 
    type: 'custom',
    position: { x: 250, y: 5 },
    data: { label: 'Node 1' },
  },
  { 
    id: '2', 
    position: { x: 100, y: 100 },
    data: { label: 'Node 2' },
  },
])

const edges = ref([
  { id: 'e1->2', source: '1', target: '2' },
])
</script>
<template>
  <VueFlow :nodes="nodes" :edges="edges" :node-types="nodeTypes" />
</template>
```

----------------------------------------

TITLE: Defining Nodes and Edges - App.vue (TS)
DESCRIPTION: This Vue component demonstrates how to define a graph consisting of nodes and edges using the Composition API with TypeScript. It utilizes type annotations (`Node`, `Edge`) for enhanced code safety and includes examples of different node and edge types, showing how to add positional data and custom data.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/getting-started.md#_snippet_7

LANGUAGE: typescript
CODE:
```
<script setup lang="ts">
import { ref } from 'vue'
import type { Node, Edge } from '@vue-flow/core'  
import { VueFlow } from '@vue-flow/core'

// these components are only shown as examples of how to use a custom node or edge
// you can find many examples of how to create these custom components in the examples page of the docs
import SpecialNode from './components/SpecialNode.vue'
import SpecialEdge from './components/SpecialEdge.vue'

// these are our nodes
const nodes = ref<Node[]>([
  // an input node, specified by using `type: 'input'`
  {
    id: '1',
    type: 'input',
    position: { x: 250, y: 5 },
    // all nodes can have a data object containing any data you want to pass to the node
    // a label can property can be used for default nodes
    data: { label: 'Node 1' },
  },

  // default node, you can omit `type: 'default'` as it's the fallback type
  {
    id: '2',
    position: { x: 100, y: 100 },
    data: { label: 'Node 2' },
  },

  // An output node, specified by using `type: 'output'`
  {
    id: '3',
    type: 'output',
    position: { x: 400, y: 200 },
    data: { label: 'Node 3' },
  },

  // this is a custom node
  // we set it by using a custom type name we choose, in this example `special`
  // the name can be freely chosen, there are no restrictions as long as it's a string
  {
    id: '4',
    type: 'special', // <-- this is the custom node type name
    position: { x: 400, y: 200 },
    data: {
      label: 'Node 4',
      hello: 'world',
    },
  },
])

// these are our edges
const edges = ref<Edge[]>([
  // default bezier edge
  // consists of an edge id, source node id and target node id
  {
    id: 'e1->2',
    source: '1',
    target: '2',
  },

  // set `animated: true` to create an animated edge path
  {
    id: 'e2->3',
    source: '2',
    target: '3',
    animated: true,
  },

  // a custom edge, specified by using a custom type name
  // we choose `type: 'special'` for this example
  {
    id: 'e3->4',
    type: 'special',
    source: '3',
    target: '4',

    // all edges can have a data object containing any data you want to pass to the edge
    data: {
      hello: 'world',
    }
  },
])
</script>
```

LANGUAGE: html
CODE:
```
<template>
  <VueFlow :nodes="nodes" :edges="edges">
    <!-- bind your custom node type to a component by using slots, slot names are always `node-<type>` -->
    <template #node-special="specialNodeProps">
      <SpecialNode v-bind="specialNodeProps" />
    </template>

    <!-- bind your custom edge type to a component by using slots, slot names are always `edge-<type>` -->
    <template #edge-special="specialEdgeProps">
      <SpecialEdge v-bind="specialEdgeProps" />
    </template>
  </VueFlow>
</template>
```

LANGUAGE: css
CODE:
```
<style>
/* import the necessary styles for Vue Flow to work */
@import '@vue-flow/core/dist/style.css';

/* import the default theme, this is optional but generally recommended */
@import '@vue-flow/core/dist/theme-default.css';
</style>
```

----------------------------------------

TITLE: Creating Basic Source and Target Handles (Vue)
DESCRIPTION: Demonstrates a basic custom node component utilizing the `<Handle>` component from `@vue-flow/core` to add source and target handles. It shows how to pass props like `id`, `sourcePosition`, `targetPosition`, and `data` to the node and use `type` and `position` props for the handles.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/handle.md#_snippet_0

LANGUAGE: vue
CODE:
```
<script setup>
import { Handle } from '@vue-flow/core'
  
defineProps(['id', 'sourcePosition', 'targetPosition', 'data'])
</script>

<template>
  <Handle type="source" :position="sourcePosition" />
  
  <span>{{ data.label }}</span>
  
  <Handle type="target" :position="targetPosition" />
</template>
```

----------------------------------------

TITLE: Implementing Custom Node Component - SpecialNode.vue (TS)
DESCRIPTION: This component demonstrates how to create a custom node for Vue Flow using the Composition API with TypeScript. It uses `defineProps<NodeProps>()` to inherit standard node properties, defines computed properties, and includes a `Handle` component for connections.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/getting-started.md#_snippet_8

LANGUAGE: typescript
CODE:
```
<script setup lang="ts">
import { computed } from 'vue'
import { Position, Handle } from '@vue-flow/core'
import type { NodeProps } from '@vue-flow/core'
  
const props = defineProps<NodeProps>()

const x = computed(() => `${Math.round(props.position.x)}px`)
const y = computed(() => `${Math.round(props.position.y)}px`)
</script>
```

LANGUAGE: html
CODE:
```
<template>
  <div class="vue-flow__node-default">
    <div>{{ data.label }}</div>

    <div>
      {{ x }} {{ y }}
    </div>

    <Handle type="source" :position="Position.Bottom" />
  </div>
</template>
```

----------------------------------------

TITLE: Integrating Custom Node Template | Vue
DESCRIPTION: Vue component example showing how to integrate the VueFlow component and define a template for a custom node type named 'custom'. It demonstrates binding flow elements using v-model and fitting the initial view. Requires the @vue-flow/core library and a definition for the CustomNode component used in the template.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/packages/node-resizer/README.md#_snippet_1

LANGUAGE: vue
CODE:
```
<script setup>\nimport { VueFlow } from '@vue-flow/core'\nimport initialElements from './initial-elements'\n\n// some nodes and edges\nconst elements = ref(initialElements)\n<\/script>\n\n<template>\n  <VueFlow v-model="elements" fit-view-on-init>\n    <template #node-custom="nodeProps">\n      <CustomNode :data="nodeProps.data" :label="nodeProps.label" />\n    <\/template>\n  <\/VueFlow>\n<\/template>
```

----------------------------------------

TITLE: Registering Custom Nodes via Template Slots (Vue/TS)
DESCRIPTION: This Vue component demonstrates using template slots to render custom nodes in Vue Flow with TypeScript typings. It shows how to define interfaces for custom node data, events, and allowed types, and applies these typings to the node definitions.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/node.md#_snippet_15

LANGUAGE: vue
CODE:
```
<script setup lang="ts">
import { ref } from 'vue'
import type { Node } from '@vue-flow/core'
import { VueFlow } from '@vue-flow/core'

import CustomNode from './CustomNode.vue'
import SpecialNode from './SpecialNode.vue'

// You can pass 3 optional generic arguments to the Node interface, allowing you to define:
// 1. The data object type
// 2. The events object type
// 3. The possible node types

export interface CustomData {
  hello: string
}

export interface CustomEvents {
  onCustomEvent: (event: MouseEvent) => void
}

type CustomNodeTypes = 'custom' | 'special'

type CustomNode = Node<CustomData, CustomEvents, CustomNodeTypes>

export const nodes = ref<CustomNode[]>([
  {
    id: '1',
    data: { label: 'Node 1' },
    // this will create the node-type `custom`
    type: 'custom',
    position: { x: 50, y: 50 },
  },
  {
    id: '2',
    data: { label: 'Node 2' },
    // this will create the node-type `special`
    type: 'special',
    position: { x: 150, y: 50 },
  },
    
  {
    id: '3', 
    data: { label: 'Node 3' },
    // this will throw a type error, as the type is not defined in the CustomEdgeTypes
    // regardless it would be rendered as a default edge type
    type: 'invalid',
    position: { x: 150, y: 50 },
  }
])
</script>

<template>
  <VueFlow :nodes="nodes">
    <template #node-custom="customNodeProps">
      <CustomNode v-bind="customNodeProps" />
    </template>
    
    <template #node-special="specialNodeProps">
      <SpecialNode v-bind="specialNodeProps" />
    </template>
  </VueFlow>
</template>
```

----------------------------------------

TITLE: Setting Container Dimensions in Vue Flow Template
DESCRIPTION: Shows how to ensure the parent container of the <VueFlow> component has explicit width and height styles set to resolve the MISSING_VIEWPORT_DIMENSIONS error. This is a required setup for the component to render correctly.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/troubleshooting.md#_snippet_0

LANGUAGE: vue
CODE:
```
<template>
  <!-- Ensure the parent container has a width and height -->
  <div style="width: 500px; height: 500px">
    <VueFlow :nodes="nodes" :edges="edges" />
  </div>
</template>
```

----------------------------------------

TITLE: Integrating Vue Flow with Custom Node - Vue
DESCRIPTION: Demonstrates how to set up a basic Vue Flow instance and utilize the #node-custom slot to render a custom node component (CustomNode). It requires @vue-flow/core and a ref helper for reactive node data. The CustomNode component receives node data via props.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/components/node-toolbar.md#_snippet_0

LANGUAGE: vue
CODE:
```
<script setup>
import { VueFlow } from '@vue-flow/core'
import initialNodes from './initialNodes'

// some nodes and edges
const nodes = ref(initialNodes)
</script>

<template>
  <VueFlow :nodes="nodes">
    <template #node-custom="nodeProps">
      <CustomNode :data="nodeProps.data" />
    </template>
  </VueFlow>
</template>
```

----------------------------------------

TITLE: Creating Custom Node with Toolbar and Handles - Vue
DESCRIPTION: Defines a custom Vue node component that integrates the NodeToolbar and connection Handle components from Vue Flow. It accepts data (containing toolbar visibility and position) and label props, typically passed from the VueFlow instance. The toolbar's visibility and position are controlled by the data prop, and it contains simple action buttons.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/components/node-toolbar.md#_snippet_1

LANGUAGE: vue
CODE:
```
<script lang="ts" setup>
import { Handle, Position } from '@vue-flow/core'
import { NodeToolbar } from '@vue-flow/node-toolbar'

interface NodeData {
  toolbarVisible: boolean
  toolbarPosition: Position
}

interface Props {
  data: NodeData
  label: string
}

defineProps<Props>()
</script>

<template>
  <NodeToolbar :is-visible="data.toolbarVisible" :position="data.toolbarPosition">
    <button>delete</button>
    <button>copy</button>
    <button>expand</button>
  </NodeToolbar>

  <Handle type="target" :position="Position.Left" />
  <Handle type="source" :position="Position.Right" />
</template>
```

----------------------------------------

TITLE: Syncing Nodes and Edges with V-Model Vue Flow Vue
DESCRIPTION: This template snippet demonstrates using the `v-model` directive (`v-model:nodes` and `v-model:edges`) to achieve two-way data binding between the internal state of nodes/edges within the `<VueFlow>` component and your component's reactive state (`nodes`, `edges`). This ensures that changes made internally (e.g., via API functions) are reflected in your external state.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/controlled-flow.md#_snippet_6

LANGUAGE: vue
CODE:
```
<template>
  <VueFlow v-model:edges="edges" v-model:nodes="nodes" />
</template>
```

----------------------------------------

TITLE: Importing Core Vue Flow Styles | CSS
DESCRIPTION: Shows how to import the necessary core CSS styles for Vue Flow functionality, which are mandatory. It also includes the optional import for the default theme styles, allowing users to decide whether to use the default theme as a baseline or build their own from scratch.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/theming.md#_snippet_1

LANGUAGE: css
CODE:
```
/* these are necessary styles for vue flow */
@import '@vue-flow/core/dist/style.css';

/* this contains the default theme, these are optional styles */
@import '@vue-flow/core/dist/theme-default.css';
```

----------------------------------------

TITLE: Integrating Vue Flow with Custom Node Slot (Vue)
DESCRIPTION: Demonstrates the basic setup of a Vue Flow component instance within a Vue application. It shows how to import the main `VueFlow` component, manage elements reactively, and utilize the `#node-custom` slot to render custom node components, which is where the toolbar node will be placed.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/packages/node-toolbar/README.md#_snippet_1

LANGUAGE: vue
CODE:
```
<script setup>
import { VueFlow } from '@vue-flow/core'
import initialElements from './initial-elements'

// some nodes and edges
const elements = ref(initialElements)
</script>

<template>
  <VueFlow v-model="elements" fit-view-on-init>
    <template #node-custom="nodeProps">
      <CustomNode :data="nodeProps.data" :label="nodeProps.label" />
    </template>
  </VueFlow>
</template>
```

----------------------------------------

TITLE: Importing Core Vue Flow Styles
DESCRIPTION: These CSS imports are necessary to ensure Vue Flow is displayed correctly. The first import provides the core styles, while the second imports the default theme, which is optional but recommended for a standard look.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/getting-started.md#_snippet_3

LANGUAGE: css
CODE:
```
/* these are necessary styles for vue flow */
@import '@vue-flow/core/dist/style.css';

/* this contains the default theme, these are optional styles */
@import '@vue-flow/core/dist/theme-default.css';
```

----------------------------------------

TITLE: Initializing Vue Flow Viewport (Composable)
DESCRIPTION: Demonstrates how to use the `onPaneReady` event hook from the `useVueFlow` composable to fit the entire flow view once the pane is initialized and ready. This approach is common in Vue 3 Composition API setups.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/utils/instance.md#_snippet_0

LANGUAGE: vue
CODE:
```
<script setup>
import { VueFlow, useVueFlow } from '@vue-flow/core'

const { onPaneReady } = useVueFlow()

// event handler
onPaneReady((instance) => instance.fitView())
</script>
```

----------------------------------------

TITLE: Importing Vue Flow Components - New Scope (@vue-flow) - JavaScript
DESCRIPTION: This snippet demonstrates the new way to import Vue Flow components after the package scope change in version 1.0.0. Core components like `VueFlow` are imported from `@vue-flow/core`, while additional components like `Background`, `MiniMap`, and `Controls` are imported from the new `@vue-flow/additional-components` package. This requires splitting imports across different packages.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/packages/pathfinding-edge/CHANGELOG.md#_snippet_3

LANGUAGE: javascript
CODE:
```
import { VueFlow } from "@vue-flow/core";
import {
  Background,
  MiniMap,
  Controls,
} from "@vue-flow/additional-components";
```

----------------------------------------

TITLE: Implementing a Custom Edge Component - Vue/JavaScript
DESCRIPTION: This component demonstrates a minimal custom edge implementation. It receives standard edge rendering props via `defineProps` and renders a `BezierEdge` from Vue Flow, passing those props down. This serves as a wrapper allowing for custom styling or behavior.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/edge.md#_snippet_9

LANGUAGE: vue
CODE:
```
<script setup>
import { BezierEdge } from '@vue-flow/core';

// props were passed from the slot using `v-bind="customEdgeProps"`
const props = defineProps(['sourceX', 'sourceY', 'targetX', 'targetY', 'sourcePosition', 'targetPosition']);
</script>

<script lang="ts">
export default {
  name: 'CustomEdge',
};
</script>

<template>
  <BezierEdge
      :source-x="sourceX"
      :source-y="sourceY"
      :target-x="targetX"
      :target-y="targetY"
      :source-position="sourcePosition"
      :target-position="targetPosition"
  />
</template>
```

----------------------------------------

TITLE: Listening to Changes Vue Flow Vue/JavaScript
DESCRIPTION: This snippet illustrates how to listen for node and edge changes using the `onNodesChange` and `onEdgesChange` event handlers from the `useVueFlow` composable or by binding to the `@nodes-change` and `@edges-change` events directly in the template. These events provide an array of `NodeChange` or `EdgeChange` objects regardless of the `applyDefault` setting.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/controlled-flow.md#_snippet_3

LANGUAGE: javascript
CODE:
```
import { VueFlow, useVueFlow } from '@vue-flow/core';

const { onNodesChange, onEdgesChange } = useVueFlow();

onNodesChange((changes) => {
  // changes are arrays of type `NodeChange`
  console.log(changes);
});

onEdgesChange((changes) => {
  // changes are arrays of type `EdgeChange`
  console.log(changes);
});

const onChange = (changes) => {
  // changes are arrays of type `NodeChange` or `EdgeChange`
  console.log(changes);
};
```

LANGUAGE: vue
CODE:
```
<template>
  <VueFlow :nodes="nodes" :edges="edges" @nodes-change="onChange" @edges-change="onChange" />
</template>
```

----------------------------------------

TITLE: Accessing Injected State and Selecting Nodes with useVueFlow (Composition API)
DESCRIPTION: Shows how a child component (like a sidebar) can access the Vue Flow state that was injected by a parent component using `useVueFlow` within `<script setup>`. Demonstrates retrieving state properties (`nodesSelectionActive`, `getNodes`) and using state methods (`addSelectedNodes`) to manipulate the graph state, specifically selecting all nodes via a button click.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/vue-flow/state.md#_snippet_2

LANGUAGE: vue
CODE:
```
<script setup>
import { useVueFlow } from '@vue-flow/core'

const { nodesSelectionActive, addSelectedNodes, getNodes } = useVueFlow()

const selectAll = () => {
  addSelectedNodes(getNodes.value)
  nodesSelectionActive.value = true
}
</script>
<template>
  <aside>
    <div class="description">
      This is an example of how you can access the internal state outside of the Vue VueFlow component.
    </div>
    <div class="selectall">
      <button @click="selectAll">select all nodes</button>
    </div>
  </aside>
</template>
```

----------------------------------------

TITLE: Handling Node Events in Vue Flow
DESCRIPTION: Demonstrates two methods for subscribing to node events in Vue Flow: using the `useVueFlow` composable to bind listeners to event handlers or binding directly to event props on the `<VueFlow>` component. Required dependency is `@vue-flow/core`.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/node.md#_snippet_19

LANGUAGE: Vue
CODE:
```
<script setup>
import { ref } from 'vue'
import { VueFlow, useVueFlow } from '@vue-flow/core'

// useVueFlow provides access to the event handlers
const { 
  onNodeDragStart, 
  onNodeDrag,
  onNodeDragStop, 
  onNodeClick, 
  onNodeDoubleClick, 
  onNodeContextMenu, 
  onNodeMouseEnter, 
  onNodeMouseLeave, 
  onNodeMouseMove 
} = useVueFlow()
  
const nodes = ref([
  {
    id: '1',
    data: { label: 'Node 1' },
    position: { x: 50, y: 50 },
  },
])
  
// bind listeners to the event handlers
onNodeDragStart((event) => {
  console.log('Node drag started', event)
})

onNodeDrag((event) => {
  console.log('Node dragged', event)
})

onNodeDragStop((event) => {
  console.log('Node drag stopped', event)
})
  
// ... and so on  
</script>

<template>
  <VueFlow :nodes="nodes" />
</template>
```

LANGUAGE: Vue
CODE:
```
<script setup>
import { ref } from 'vue'
import { VueFlow } from '@vue-flow/core'

const nodes = ref([
  {
    id: '1',
    data: { label: 'Node 1' },
    position: { x: 50, y: 50 },
  },
])
  
function logEvent(name, data) {
  console.log(name, data)
}
</script>

<template>
  <!-- bind listeners to the event handlers -->
  <VueFlow
    :nodes="nodes"
    @node-drag-start="logEvent('drag start', $event)"
    @node-drag="logEvent('drag', $event)"
    @node-drag-stop="logEvent('drag stop', $event)"
    @node-click="logEvent('click', $event)"
    @node-double-click="logEvent('dblclick', $event)"
    @node-contextmenu="logEvent('contextmenu', $event)"
    @node-mouse-enter="logEvent('mouseenter', $event)"
    @node-mouse-leave="logEvent('mouseleave', $event)"
    @node-mouse-move="logEvent('mousemove', $event)"
  />
</template>
```

----------------------------------------

TITLE: Implementing Custom Node Component - SpecialNode.vue (JS)
DESCRIPTION: This component demonstrates how to create a custom node for Vue Flow using the Composition API with JavaScript. It defines props to receive node data, uses `computed` properties, and includes a `Handle` component to define a connection point for edges.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/getting-started.md#_snippet_5

LANGUAGE: javascript
CODE:
```
<script setup>
import { computed } from 'vue'
import { Position, Handle } from '@vue-flow/core'

const props = defineProps({
  position: {
    type: Object,
    required: true,
  }
})

const x = computed(() => `${Math.round(props.position.x)}px`)
const y = computed(() => `${Math.round(props.position.y)}px`)
</script>
```

LANGUAGE: html
CODE:
```
<template>
  <div class="vue-flow__node-default">
    <div>{{ data.label }}</div>

    <div>
      {{ x }} {{ y }}
    </div>

    <Handle type="source" :position="Position.Bottom" />
  </div>
</template>
```

----------------------------------------

TITLE: Setting up Edge Demo Data in Vue Flow
DESCRIPTION: Initial script block demonstrating how to define nodes and edges using Vue's `ref` for different predefined edge types (bezier, step, smoothstep, straight). This setup is used to power the visual examples on the page.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/edge.md#_snippet_0

LANGUAGE: vue
CODE:
```
<script setup>
import LogosJavascript from '~icons/logos/javascript';
import LogosTypescript from '~icons/types/typescript-icon';
import { VueFlow, Panel } from '@vue-flow/core';
import { Background } from '@vue-flow/background';
import Check from '~icons/mdi/check';
import Close from '~icons/mdi/close';
import { ref } from 'vue';

const nodes = [
  {
    id: '1',
    type: 'input',
    label: 'Node 1',
    position: { x: 50, y: 25 },
  },
  {
    id: '2',
    label: 'Node 2',
    position: { x: 100, y: 125 },
  },
];

const bezierEdge = ref([
  ...nodes,
  {
    id: 'e1-2',
    source: '1',
    target: '2',
  }
]);

const stepEdge = ref([
  ...nodes,
  {
    id: 'e1-2',
    type: 'step',
    source: '1',
    target: '2',
  },
]);

const smoothStepEdge = ref([
  ...nodes,
  {
    id: 'e1-2',
    type: 'smoothstep',
    source: '1',
    target: '2',
  },
]);

const straightEdge = ref([
  {
    id: '1',
    type: 'input',
    label: 'Node 1',
    position: { x: 50, y: 25 },
  },
  {
    id: '2',
    label: 'Node 2',
    position: { x: 50, y: 125 },
  },
  {
    id: 'e1-2',
    type: 'straight',
    source: '1',
    target: '2',
  },
]);

function logEvent(name, data) {
  console.log(name, data)
}
</script>
```

----------------------------------------

TITLE: Accessing Handle IDs in onConnect Callback (TypeScript)
DESCRIPTION: Demonstrates how to use the `onConnect` event from `useVueFlow()` to capture connection details. It shows how the `sourceHandle` and `targetHandle` properties in the connection object provide the IDs of the handles used, or `null` if no ID was specified.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/handle.md#_snippet_3

LANGUAGE: ts
CODE:
```
const { onConnect } = useVueFlow()

onConnect(({ source, target, sourceHandle, targetHandle }) => {
  console.log('source', source)
  console.log('target', target)
  // these are the handle ids of the source and target node
  // if no id is specified these will be `null`, meaning the first handle of the necessary type will be used
  console.log('sourceHandle', sourceHandle)
  console.log('targetHandle', targetHandle)
})
```

----------------------------------------

TITLE: Defining Handles in Custom Vue Flow Node
DESCRIPTION: This snippet demonstrates how to import and use the Handle and Position components from `@vue-flow/core` to define source and target connection points in a custom Vue Flow node component. It sets up a source handle on the right side and a target handle on the left side of the node.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/examples/edges/validation.md#_snippet_0

LANGUAGE: Vue
CODE:
```
<script setup>
import { Handle, Position } from '@vue-flow/core'
</script>

<template>
  <Handle type="source" :position="Position.Right" />
  <Handle type="target" :position="Position.Left" />
</template>
```

----------------------------------------

TITLE: Initializing Default Node Data Vue
DESCRIPTION: These Vue snippets demonstrate initializing a reactive reference (`ref`) containing an array with a single node object. Each object defines a node with a unique `id`, `label`, and `position`, representing different default, input, and output node types.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/node.md#_snippet_1

LANGUAGE: Vue
CODE:
```
const defaultNode = ref([
  {
    id: '1',
    label: 'Default Node',
    position: { x: 50, y: 75 },
  }
]);
```

LANGUAGE: Vue
CODE:
```
const inputNode = ref([
  {
    id: '1',
    type: 'input',
    label: 'Input Node',
    position: { x: 50, y: 75 },
  }
]);
```

LANGUAGE: Vue
CODE:
```
const outputNode = ref([
  {
    id: '1',
    type: 'output',
    label: 'Output Node',
    position: { x: 50, y: 75 },
  }
]);
```

----------------------------------------

TITLE: Updating Edge Data in Custom Edge Component via useEdge
DESCRIPTION: Demonstrates how a custom edge component can use the `useEdge` composable to get direct access to the reactive edge object. Mutating the properties (`data`, `selectable`, `animated`, etc.) of this object updates the edge in the graph state.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/edge.md#_snippet_6

LANGUAGE: vue
CODE:
```
<!-- CustomEdge.vue -->
<script setup>
import { useEdge } from '@vue-flow/core'

// `useEdge` returns us the edge object straight from the state
// since the edge obj is reactive, we can mutate it to update our edges' data
const { edge } = useEdge()

function onSomeEvent() {
  edge.data = {
    ...edge.data,  
    hello: 'world',
  }
  
  // you can also mutate properties like `selectable` or `animated`
  edge.selectable = !edge.selectable
  edge.animated = !edge.animated
}
</script>
```

----------------------------------------

TITLE: Binding Edge Events using useVueFlow (Vue)
DESCRIPTION: This snippet shows how to bind listeners to edge events within a Vue 3 component using the `useVueFlow` composable. It imports necessary functions like `onEdgeClick`, `onEdgeDoubleClick`, etc., and calls them with handler functions that receive the event object and the edge data when an event occurs on an edge. Dependencies include `@vue-flow/core` and `vue`.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/edge.md#_snippet_14

LANGUAGE: vue
CODE:
```
<script setup>
import { ref } from 'vue'
import { VueFlow, useVueFlow } from '@vue-flow/core'

// useVueFlow provides access to the event handlers
const {
  onEdgeClick,
  onEdgeDoubleClick,
  onEdgeContextMenu,
  onEdgeMouseEnter,
  onEdgeMouseLeave,
  onEdgeMouseMove,
  onEdgeUpdateStart,
  onEdgeUpdate,
  onEdgeUpdateEnd,
} = useVueFlow()

const nodes = ref([
  {
    id: '1',
    position: { x: 50, y: 50 },
    data: { label: 'Node 1', },
  },
  {
    id: '2',
    position: { x: 50, y: 250 },
    data: { label: 'Node 2', },
  },
])

const edges = ref([
  {
    id: 'e1->2',
    source: '1',
    target: '2',
  },
])

// bind listeners to the event handlers
onEdgeClick((event, edge) => {
  console.log('edge clicked', edge)
})

onEdgeDoubleClick((event, edge) => {
  console.log('edge double clicked', edge)
})

onEdgeContextMenu((event, edge) => {
  console.log('edge context menu', edge)
})

// ... and so on
</script>

<template>
  <VueFlow :nodes="nodes" :edges="edges" />
</template>
```

----------------------------------------

TITLE: Implementing Custom Edge Component - SpecialEdge.vue (TS)
DESCRIPTION: This component shows how to build a custom edge for Vue Flow using the Composition API with TypeScript. It uses `defineProps<EdgeProps>()` to inherit standard edge properties, calculates the path with `getBezierPath`, and renders the edge and a custom label using `BaseEdge` and `EdgeLabelRenderer`.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/getting-started.md#_snippet_9

LANGUAGE: typescript
CODE:
```
<script setup lang="ts">
import { BaseEdge, EdgeLabelRenderer, getBezierPath, type EdgeProps } from '@vue-flow/core'
import { computed } from 'vue'

const props = defineProps<EdgeProps>()

const path = computed(() => getBezierPath(props))
</script>
```

LANGUAGE: javascript
CODE:
```
<script>
export default {
  inheritAttrs: false,
}
</script>
```

LANGUAGE: html
CODE:
```
<template>
  <!-- You can use the `BaseEdge` component to create your own custom edge more easily -->
  <BaseEdge :path="path[0]" />

  <!-- Use the `EdgeLabelRenderer` to escape the SVG world of edges and render your own custom label in a `<div>` ctx -->
  <EdgeLabelRenderer>
    <div
      :style="{
        pointerEvents: 'all',
        position: 'absolute',
        transform: `translate(-50%, -50%) translate(${path[1]}px,${path[2]}px)`,
      }"
      class="nodrag nopan"
    >
      {{ data.hello }}
    </div>
  </EdgeLabelRenderer>
</template>
```

----------------------------------------

TITLE: Getting Edge Path and Center - New API (@vue-flow/core) - JavaScript
DESCRIPTION: This snippet shows the updated API for getting edge path and center positions using the new `@vue-flow/core` package. The `getBezierPath` function now returns an array containing the path string, centerX, and centerY, eliminating the need for a separate `getEdgeCenter` call. Dependencies include `getBezierPath` from `@vue-flow/core` and potentially `computed` from Vue.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/packages/pathfinding-edge/CHANGELOG.md#_snippet_1

LANGUAGE: javascript
CODE:
```
import { getBezierPath } from "@vue-flow/core";

// returns the path string and the center positions
const [path, centerX, centerY] = computed(() => getBezierPath(pathParams));
```

----------------------------------------

TITLE: Updating Node Data with updateNodeData in TypeScript
DESCRIPTION: This snippet demonstrates how to use the `updateNodeData` action from `useVueFlow` to modify the `data` property of a specific node. It supports updating with an object (merging by default) or a function, and an option to replace the entire data object.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/packages/core/CHANGELOG.md#_snippet_5

LANGUAGE: ts
CODE:
```
const { updateNodeData } = useVueFlow();

updateNodeData("1", { foo: "bar" });

// or using a function to update the data
updateNodeData("1", (data) => ({ ...data, foo: "bar" }));

// passing options - `replace` will replace the data instead of merging it
updateNodeData("1", { foo: "bar" }, { replace: true });
```

----------------------------------------

TITLE: Defining Custom Vue Flow Nodes
DESCRIPTION: These snippets show how to define custom node components using Vue's `h` function. `ScrollableNode` creates a div with a scrollable list, while `InputFieldNode` creates a div containing an input field, demonstrating how to render complex content within a node.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/node.md#_snippet_0

LANGUAGE: Vue
CODE:
```
const ScrollableNode = () => h('div', { class: 'custom-node-container' }, [
  h('ul', { class: 'nowheel' }, Array.from({ length: 100 }, (_, i) => h('li', { key: i }, `Item ${i}`)))
]);
```

LANGUAGE: Vue
CODE:
```
const InputFieldNode = () => h('div', { class: 'custom-node-container' }, [
  h('input', { class: 'nodrag placeholder-white', placeholder: 'Type something...' })
]);
```

----------------------------------------

TITLE: Binding Edge Events to VueFlow Component (Vue)
DESCRIPTION: This snippet illustrates how to handle edge events by binding listeners directly to the `<VueFlow>` component tag in the template. It uses standard Vue event syntax (`@edge-click`, `@edge-double-click`, etc.) to call a handler function that receives the edge event data. Dependencies include `@vue-flow/core` and `vue`.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/edge.md#_snippet_15

LANGUAGE: vue
CODE:
```
<script setup>
import { ref } from 'vue'
import { VueFlow } from '@vue-flow/core'

const nodes = ref([
  {
    id: '1',
    position: { x: 50, y: 50 },
    data: { label: 'Node 1', },
  },
  {
    id: '2',
    position: { x: 50, y: 250 },
    data: { label: 'Node 2', },
  },
])

const edges = ref([
  {
    id: 'e1->2',
    source: '1',
    target: '2',
  },
])

function logEvent(eventName, data) {
  console.log(eventName, data)
}
</script>

<template>
  <VueFlow
    :nodes="nodes"
    :edges="edges"
    @edge-click="logEvent('edge clicked', $event)"
    @edge-double-click="logEvent('edge double clicked', $event)"
    @edge-context-menu="logEvent('edge context menu', $event)"
    @edge-mouse-enter="logEvent('edge mouse enter', $event)"
    @edge-mouse-leave="logEvent('edge mouse leave', $event)"
    @edge-mouse-move="logEvent('edge mouse move', $event)"
    @edge-update-start="logEvent('edge update start', $event)"
    @edge-update="logEvent('edge update', $event)"
    @edge-update-end="logEvent('edge update end', $event)"
  />
</template>
```

----------------------------------------

TITLE: Implementing Custom Edge Component - SpecialEdge.vue (JS)
DESCRIPTION: This component shows how to build a custom edge for Vue Flow using the Composition API with JavaScript. It defines props for source/target details, uses `getBezierPath` to calculate the edge path, and renders the base edge and a custom label using `BaseEdge` and `EdgeLabelRenderer`.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/getting-started.md#_snippet_6

LANGUAGE: javascript
CODE:
```
<script setup>
import { BaseEdge, EdgeLabelRenderer, getBezierPath } from '@vue-flow/core'
import { computed } from 'vue'

const props = defineProps({
  sourceX: {
    type: Number,
    required: true,
  },
  sourceY: {
    type: Number,
    required: true,
  },
  targetX: {
    type: Number,
    required: true,
  },
  targetY: {
    type: Number,
    required: true,
  },
  sourcePosition: {
    type: String,
    required: true,
  },
  targetPosition: {
    type: String,
    required: true,
  },
  data: {
    type: Object,
    required: true,
  }
})

const path = computed(() => getBezierPath(props))
</script>
```

LANGUAGE: javascript
CODE:
```
<script>
export default {
  inheritAttrs: false,
}
</script>
```

LANGUAGE: html
CODE:
```
<template>
  <!-- You can use the `BaseEdge` component to create your own custom edge more easily -->
  <BaseEdge :path="path[0]" />

  <!-- Use the `EdgeLabelRenderer` to escape the SVG world of edges and render your own custom label in a `<div>` ctx -->
  <EdgeLabelRenderer>
    <div
      :style="{
        pointerEvents: 'all',
        position: 'absolute',
        transform: `translate(-50%, -50%) translate(${path[1]}px,${path[2]}px)`,
      }"
      class="nodrag nopan"
    >
      {{ data.hello }}
    </div>
  </EdgeLabelRenderer>
</template>
```

----------------------------------------

TITLE: Registering Custom Nodes via nodeTypes Prop (Vue/JS)
DESCRIPTION: This snippet demonstrates an alternative method to register custom node components. It defines a `nodeTypes` object mapping node type strings to components, using `markRaw` to prevent reactivity issues, and passes this object as a prop to `<VueFlow>`.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/node.md#_snippet_18

LANGUAGE: vue
CODE:
```
<script setup>
import { markRaw } from 'vue'
import CustomNode from './CustomNode.vue'
import SpecialNode from './SpecialNode.vue'

const nodeTypes = {
  custom: markRaw(CustomNode),
  special: markRaw(SpecialNode),
}

const nodes = ref([
  {
    id: '1',
    data: { label: 'Node 1' },
    type: 'custom',
  },
  {
    id: '1',
    data: { label: 'Node 1' },
    type: 'special',
  }
])
</script>

<template>
  <VueFlow :nodes="nodes" :nodeTypes="nodeTypes" />
</template>
```

----------------------------------------

TITLE: Listening to Events on VueFlow Component (Vue)
DESCRIPTION: Demonstrates how to attach event listeners directly to the <VueFlow> component using the standard Vue `@` directive. Event handler functions are defined in the script and bound to specific events on the component. Handlers receive an object containing event details and the relevant flow element.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/vue-flow/events.md#_snippet_0

LANGUAGE: Vue
CODE:
```
<script setup>\nimport { ref } from 'vue';  \nimport { VueFlow } from '@vue-flow/core';\n\nconst nodes = ref([/* ... */]);\nconst edges = ref([/* ... */]);\n\n// Node click event handler\nfunction onNodeClick({ event, node }) {\n  console.log('Node clicked:', node, event);\n}\n\n// Edge click event handler\nfunction onEdgeClick({ event, edge }) {\n  console.log('Edge clicked:', edge, event);\n}\n<\/script>\n\n<template>\n  <VueFlow :nodes="nodges" :edges="edges" @node-click="onNodeClick" @edge-click="onEdgeClick"><\/VueFlow>\n<\/template>
```

----------------------------------------

TITLE: Styling Nodes with CSS Variables | Vue Flow | JavaScript
DESCRIPTION: Demonstrates how to apply CSS variables directly to the `style` property of an individual node object. This allows overriding global or default variable values on a per-element basis, providing fine-grained control over styling using CSS variables.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/theming.md#_snippet_6

LANGUAGE: javascript
CODE:
```
const nodes = ref([
  { 
    id: '1', 
    position: { x: 100, y: 100 }, 
    data: { label: 'Node 1' },
    /* Overriding the `--vf-node-color` variable to change node border, box-shadow and handle color */
    style: { '--vf-node-color': 'blue' } 
  },
])
```

----------------------------------------

TITLE: Using Template Slot for Custom Edge - Vue
DESCRIPTION: A simplified example illustrating the basic pattern for defining a single custom edge type ('custom') and rendering it using a template slot (`#edge-custom`) that binds standard edge props to a custom component (`CustomEdge`).
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/edge.md#_snippet_12

LANGUAGE: vue
CODE:
```
<script setup>
import { ref } from 'vue'
import { VueFlow } from '@vue-flow/core'
import CustomEdge from './CustomEdge.vue'

const nodes = ref([
  {
    id: '1',
    position: { x: 50, y: 50 },
    data: { label: 'Node 1', },
  },
  {
    id: '2',
    position: { x: 50, y: 250 },
    data: { label: 'Node 2', },
  },
])

const edges = ref([
  {
    id: 'e1->2',
    type: 'custom',
    source: '1',
    target: '2',
  },
])
</script>

<template>
  <VueFlow :nodes="nodes" :edges="edges">
    <template #edge-custom="props">
      <CustomEdge v-bind="props" />
    </template>
  </VueFlow>
</template>
```

----------------------------------------

TITLE: Defining Custom Edges with Slots - Vue/JavaScript
DESCRIPTION: This snippet shows how to define custom edge types ('custom', 'special') within the edges array and render them using template slots (`#edge-custom`, `#edge-special`) in the `<VueFlow>` component. It requires importing and using the corresponding custom edge components.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/edge.md#_snippet_8

LANGUAGE: vue
CODE:
```
<script setup>
import { ref } from 'vue'
import { VueFlow } from '@vue-flow/core'

import CustomEdge from './CustomEdge.vue'
import SpecialEdge from './SpecialEdge.vue'

export const edges = ref([
  {
    id: 'e1->2',
    source: '1',
    target: '2',
    // this will create the edge-type `custom`
    type: 'custom',
  },
  {
    id: 'e1->3',
    source: '1',
    target: '3',
    // this will create the edge-type `special`
    type: 'special',
  }
])
  
const nodes = ref([
  {
    id: '1',
    position: { x: 50, y: 50 },
    data: { label: 'Node 1', },
  },
  {
    id: '2',
    position: { x: 50, y: 250 },
    data: { label: 'Node 2', },
  },
  {
    id: '3',
    position: { x: 250, y: 50 },
    data: { label: 'Node 3', },
  },
  {
    id: '4',
    position: { x: 250, y: 250 },
    data: { label: 'Node 4', },
  },
])
</script>

<template>
  <VueFlow :nodes="nodes" :edges="edges">
    <template #edge-custom="customEdgeProps">
      <CustomEdge v-bind="customEdgeProps" />
    </template>
    
    <template #edge-special="specialEdgeProps">
      <SpecialEdge v-bind="specialEdgeProps" />
    </template>
  </VueFlow>
</template>
```

----------------------------------------

TITLE: Using Node Intersection Utilities in Vue Flow
DESCRIPTION: Demonstrates different ways to check for node intersections within Vue Flow workflows. This includes using the 'getIntersectingNodes' action to find all nodes intersecting a given node, accessing intersecting nodes directly from the 'onNodeDrag' event arguments, and utilizing the 'isNodeIntersecting' utility to check if a node intersects with a specific area. These examples are typically used within event handlers like 'onNodeDrag'.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/packages/core/CHANGELOG.md#_snippet_11

LANGUAGE: javascript
CODE:
```
const { onNodeDrag, getIntersectingNodes, getNodes } = useVueFlow();

onNodeDrag(({ node }) => {
  const intersections = getIntersectingNodes(node).map((n) => n.id);

  getNodes.value.forEach((n) => {
    // highlight nodes that are intersecting with the dragged node
    n.class = intersections.includes(n.id) ? "highlight" : "";
  });
});
```

LANGUAGE: javascript
CODE:
```
onNodeDrag(({ intersections }) => {
  getNodes.value.forEach((n) => {
    n.class = intersections?.some((i) => i.id === n.id) ? "highlight" : "";
  });
});
```

LANGUAGE: javascript
CODE:
```
const { onNodeDrag, isNodeIntersecting } = useVueFlow();

onNodeDrag(({ node }) => {
  // highlight the node if it is intersecting with the given area
  node.class = isNodeIntersecting(node, {
    x: 0,
    y: 0,
    width: 100,
    height: 100,
  })
    ? "highlight"
    : "";
});
```

----------------------------------------

TITLE: Registering Custom Edge with edgeTypes Object - Vue
DESCRIPTION: This example demonstrates an alternative method for registering custom edge components by passing an `edgeTypes` object to the `<VueFlow>` component. The object maps edge type names to component references, and it is recommended to wrap components with `markRaw`.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/edge.md#_snippet_13

LANGUAGE: vue
CODE:
```
<script setup>
import { markRaw } from 'vue'
import CustomEdge from './CustomEdge.vue'

const edgeTypes = {
  custom: markRaw(CustomEdge),
}

const nodes = ref([
  {
    id: '1',
    position: { x: 50, y: 50 },
    data: { label: 'Node 1', },
  },
  {
    id: '2',
    position: { x: 50, y: 250 },
    data: { label: 'Node 2', },
  },
])

const edges = ref([
  {
    id: 'e1->2',
    type: 'custom',
    source: '1',
    target: '2',
  },
])
</script>
<template>
  <VueFlow :nodes="nodes" :edges="edges" :edgeTypes="edgeTypes" />
</template>
```

----------------------------------------

TITLE: Updating Node Data Using useVueFlow Typescript
DESCRIPTION: This TypeScript snippet shows multiple ways to update node data and properties using the `useVueFlow` instance. It demonstrates using `updateNodeData`, finding and mutating a node object directly, and using the `updateNode` method.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/node.md#_snippet_7

LANGUAGE: Typescript
CODE:
```
import  { useVueFlow } from '@vue-flow/core'

const instance = useVueFlow()

// use the `updateNodeData` method to update the data of an edge
instance.updateNodeData(edgeId, { hello: 'mona' })

// find the node in the state by its id
const node = instance.findNode(nodeId)

node.data = {
  ...node.data,
  hello: 'world',
}

// you can also mutate properties like `selectable` or `draggable`
node.selectable = false
node.draggable = false

// or use `updateNode` to update the node directly
instance.updateNode(nodeId, { selectable: false, draggable: false })
```

----------------------------------------

TITLE: Registering Custom Nodes via Template Slots (Vue/JS)
DESCRIPTION: This Vue component demonstrates how to define nodes with custom types ('custom', 'special') and render them within a Vue Flow instance by providing corresponding named template slots (`#node-custom`, `#node-special`) that bind props to custom component implementations.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/node.md#_snippet_13

LANGUAGE: vue
CODE:
```
<script setup>
import { ref } from 'vue'
import { VueFlow } from '@vue-flow/core'

import CustomNode from './CustomNode.vue'
import SpecialNode from './SpecialNode.vue'

export const nodes = ref([
  {
    id: '1',
    data: { label: 'Node 1' },
    // this will create the node-type `custom`
    type: 'custom',
    position: { x: 50, y: 50 },
  },
  {
    id: '1',
    data: { label: 'Node 1' },
    // this will create the node-type `special`
    type: 'special',
    position: { x: 150, y: 50 },
  }
])
</script>

<template>
  <VueFlow :nodes="nodes">
    <template #node-custom="customNodeProps">
      <CustomNode v-bind="customNodeProps" />
    </template>
    
    <template #node-special="specialNodeProps">
      <SpecialNode v-bind="specialNodeProps" />
    </template>
  </VueFlow>
</template>
```

----------------------------------------

TITLE: Styling Nodes with Inline Class/Style | Vue Flow | JavaScript
DESCRIPTION: Shows how to include `class` and `style` properties directly within the definition of individual node objects in the `nodes` array. The `class` property takes a string, and the `style` property takes an object, allowing granular control over node appearance.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/theming.md#_snippet_4

LANGUAGE: javascript
CODE:
```
/* Customizing node by assigning class and style properties */
const nodes = ref([
  { 
    id: '1', 
    position: { x: 250, y: 5 },
    data: { label: 'Node 1' },
    
    // Add a class name to the node
    class: 'my-custom-node-class',
    
    // You can pass an object containing CSSProperties or CSS variables
    style: { backgroundColor: 'green', width: '200px', height: '100px' },
  },
])
```

----------------------------------------

TITLE: Simple Custom Node Template Slot Example (Vue/JS)
DESCRIPTION: A focused example showing how to define a node with a custom type ('custom') and associate it with a template slot (`#node-custom`) within the `<VueFlow>` component, binding the slot props to a custom component.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/node.md#_snippet_17

LANGUAGE: vue
CODE:
```
<script setup>
import { VueFlow } from '@vue-flow/core'
import CustomNode from './CustomNode.vue'

const nodes = ref([
  {
    id: '1',
    data: { label: 'Node 1' },
    type: 'custom',
    position: { x: 50, y: 50 },
  }
])
</script>

<template>
  <VueFlow :nodes="nodes">
    <!-- the expected slot name is `node-custom` -->
    <template #node-custom="props">
      <CustomNode v-bind="props" />
    </template>
  </VueFlow>
</template>
```

----------------------------------------

TITLE: Updating Node Data Using v-model Vue
DESCRIPTION: This Vue example demonstrates updating node data when using `v-model` or the `:nodes` prop. It shows how to find a node in the reactive array and mutate its `data` or other properties directly, which Vue Flow observes to update the displayed node.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/node.md#_snippet_9

LANGUAGE: Vue
CODE:
```
<script setup>
import { ref } from 'vue'

const nodes = ref([
  {
    id: '1',
    position: { x: 50, y: 50 },
    data: {
      label: 'Node 1',
      hello: 'world',
    }
  },
])

function onSomeEvent(nodeId) {
  const node = nodes.value.find((node) => node.id === nodeId)

  node.data = {
    ...nodes.value[0].data,
    hello: 'world',
  }
    
  // you can also mutate properties like `selectable` or `draggable`
  node.selectable = false
  node.draggable = false
}
</script>

<template>
  <VueFlow :nodes="nodes">
    <Panel>
      <button type="button" @click="onSomeEvent('1')">Update Node 1</button>
    </Panel>
  </VueFlow>
</template>
```

----------------------------------------

TITLE: Disabling Default State Updates in VueFlow Template (Vue Template)
DESCRIPTION: Demonstrates how to configure the `VueFlow` component to prevent it from automatically applying state changes (like node position updates) that occur through user interaction or other means. Setting the `:apply-default` prop to `false` requires manual handling of state updates.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/vue-flow/state.md#_snippet_3

LANGUAGE: vue
CODE:
```
<template>
    <VueFlow :nodes="nodes" :edges="edges" :apply-default="false" />
</template>
```

----------------------------------------

TITLE: Updating Node with API Vue Flow JavaScript
DESCRIPTION: This snippet shows how to use the `updateNode` function from `useVueFlow` to modify properties of a node. When using `v-model` for nodes, calling `updateNode` will update the internal state *and* synchronize the change back to the bound `nodes` ref in your component.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/controlled-flow.md#_snippet_7

LANGUAGE: javascript
CODE:
```
import { ref } from 'vue';
import { useVueFlow } from '@vue-flow/core';

const nodes = ref([
  {
    id: '1',
    position: { x: 0, y: 0 },
    data: { label: 'Node 1' },
  },
]);

const { updateNode } = useVueFlow();

// using updateNode will only update the internal state, not the nodes state unless you use v-model
updateNode('1', { type: 'new-type' });
```

----------------------------------------

TITLE: Setting Viewport Configuration with VueFlow Props (Vue)
DESCRIPTION: This snippet shows how to configure the initial viewport zoom and limits by passing `default-viewport`, `max-zoom`, and `min-zoom` directly as props to the `<VueFlow>` component in a Vue template. It sets an initial zoom level and defines the allowed zoom range.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/vue-flow/config.md#_snippet_0

LANGUAGE: vue
CODE:
```
<template>
  <VueFlow :default-viewport="{ zoom: 0.5 }" :max-zoom="4" :min-zoom="0.1" />
</template>
```

----------------------------------------

TITLE: Adding Edges via Vue Flow Props
DESCRIPTION: Demonstrates how to add edges to a Vue Flow graph by passing a reactive array of edge objects to the `:edges` prop of the `<VueFlow>` component. Edges require a unique `id`, `source`, and `target` node id.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/edge.md#_snippet_1

LANGUAGE: javascript
CODE:
```
<script setup>
import { ref, onMounted } from 'vue'
import { VueFlow } from '@vue-flow/core'

const nodes = ref([
  {
    id: '1',
    position: { x: 50, y: 50 },
    data: { label: 'Node 1', },
  },
  {
    id: '2',
    position: { x: 50, y: 250 },
    data: { label: 'Node 2', },
  }
]);

const edges = ref([
  {
    id: 'e1->2',
    source: '1',
    target: '2',
  }
]);
</script>

<template>
  <VueFlow :nodes="nodes" :edges="edges" />
</template>
```

LANGUAGE: typescript
CODE:
```
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import type { Node, Edge } from '@vue-flow/core'
import { VueFlow } from '@vue-flow/core'

const nodes = ref<Node[]>([
  {
    id: '1',
    position: { x: 50, y: 50 },
    data: { label: 'Node 1', },
  },
  {
    id: '2',
    position: { x: 50, y: 250 },
    data: { label: 'Node 2', },
  }
]);

const edges = ref<Edge[]>([
  {
    id: 'e1->2',
    source: '1',
    target: '2',
  }
]);
</script>

<template>
  <VueFlow :nodes="nodes" :edges="edges" />
</template>
```

----------------------------------------

TITLE: Creating a Custom Node Component (Vue/JS)
DESCRIPTION: This is a basic example of a custom Vue node component intended for use with Vue Flow. It defines target and source handles using the `Handle` component and accepts a `label` prop to display within the node.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/node.md#_snippet_14

LANGUAGE: vue
CODE:
```
<script setup>
import { Position, Handle } from '@vue-flow/core'

// props were passed from the slot using `v-bind="customNodeProps"`
const props = defineProps(['label'])
</script>

<template>
  <div>
    <Handle type="target" :position="Position.Top" />
    <div>{{ label }}</div>
    <Handle type="source" :position="Position.Bottom" />
  </div>
</template>
```

----------------------------------------

TITLE: Adding Edges via useVueFlow Composable
DESCRIPTION: Illustrates how to add edges programmatically using the `addEdges` action obtained from the `useVueFlow` composable. This method updates the internal state and can be called from components not directly rendering the graph.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/edge.md#_snippet_2

LANGUAGE: vue
CODE:
```
<script setup>
import { VueFlow, useVueFlow } from '@vue-flow/core'

const initialNodes = ref([
  {
    id: '1',
    position: { x: 50, y: 50 },
    data: { label: 'Node 1', },
  },
  {
    id: '2',
    position: { x: 50, y: 250 },
    data: { label: 'Node 2', },
  }
])

const { addEdges } = useVueFlow()

addEdges([
  {
    source: '1',
    target: '2',

    // if a node has multiple handles of the same type,
    // you should specify which handle to use by id
    sourceHandle: null,
    targetHandle: null,
  }
])
</script>

<template>
  <VueFlow :nodes="initialNodes" />
</template>
```

----------------------------------------

TITLE: Updating Edge Data via Ref Mutation (v-model/Props)
DESCRIPTION: Shows how to update an edge when managing the graph state via a reactive ref bound using `v-model` or `:edges`. Find the edge object in the ref's array and mutate its `data` property or other attributes to trigger a reactive update.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/edge.md#_snippet_7

LANGUAGE: vue
CODE:
```
<script setup>
import { ref } from 'vue'

const nodes = ref([
  {
    id: '1',
    position: { x: 50, y: 50 },
    data: {
      label: 'Node 1',
      hello: 'world',
    },
  },
  {
      id: '2',
      position: { x: 50, y: 250 },
      data: { label: 'Node 2', },
  },
])

const edges = ref([
  {
    id: 'e1->2',
    source: '1',
    target: '2',
  },
])

function onSomeEvent(edgeId) {
  const edge = edges.value.find((edge) => edge.id === edgeId)
  // Note: The original source code contained an error here accessing 'elements.value[0].data'.
  // The correct approach for mutation is to update the found 'edge' object.
  if (edge) {
    edge.data = {
      ...edge.data,
      hello: 'world',
    }

    // you can also mutate properties like `selectable` or `animated`
    edge.selectable = !edge.selectable
    edge.animated = !edge.animated
  }
}
</script>

<template>
  <!-- Assuming edges are bound via :edges or v-model binding the edges ref -->
  <!-- Example uses v-model but relies on 'edges' ref internally -->
  <VueFlow :nodes="nodes" :edges="edges" />
</template>
```

----------------------------------------

TITLE: Retrieving Current Flow State Object
DESCRIPTION: Shows the type signature of the `toObject` function, which returns a serializable object containing the current flow's elements (nodes and edges), the pane's position, and its zoom level. Useful for saving or inspecting the flow state.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/utils/instance.md#_snippet_6

LANGUAGE: ts
CODE:
```
toObject = (): {
  elements: FlowElements,
  position: [x, y],
  zoom: scale,
}
```

----------------------------------------

TITLE: Disabling Automatic Changes Vue Flow Vue
DESCRIPTION: This template snippet demonstrates how to disable the default automatic application of changes by passing the `applyDefault` prop with a value of `false` to the `<VueFlow>` component. This allows you to take manual control over when and how changes are applied.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/controlled-flow.md#_snippet_2

LANGUAGE: vue
CODE:
```
<template>
  <VueFlow :nodes="nodes" :edges="edges" :apply-default="false" />
</template>
```

----------------------------------------

TITLE: Removing Nodes Using useVueFlow Action Vue
DESCRIPTION: This Vue snippet demonstrates using the `removeNodes` action from the `useVueFlow` composable to remove one or multiple nodes from the graph state. This method is suitable for removing nodes programmatically from anywhere in the application.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/node.md#_snippet_6

LANGUAGE: Vue
CODE:
```
<script setup>
import { ref } from 'vue'
import { VueFlow, Panel, useVueFlow } from '@vue-flow/core'

const initialNodes = ref([
  {
    id: '1',
    position: { x: 50, y: 50 },
    data: { label: 'Node 1' },
  },
  {
    id: '2',
    position: { x: 150, y: 50 },
    data: { label: 'Node 2' },
  }
])

const { removeNodes } = useVueFlow()

function removeOneNode() {
  removeNodes('1')
}

function removeMultipleNodes() {
  removeNodes(['1', '2'])
}
</script>

<template>
  <VueFlow :nodes="initialNodes">
    <Panel>
      <button type="button" @click="removeOneNode">Remove Node 1</button>
      <button type="button" @click="removeMultipleNodes">Remove Node 1 and 2</button>
    </Panel>
  </VueFlow>
</template>
```

----------------------------------------

TITLE: Defining Valid Vue Flow Edge Configurations (TypeScript)
DESCRIPTION: Shows an example of a ref containing an array of edge objects. Each edge correctly defines id, source, and target properties, illustrating the necessary configuration to avoid the EDGE_INVALID error.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/troubleshooting.md#_snippet_3

LANGUAGE: ts
CODE:
```
// Here's an example of some valid edge configurations
const edges = ref([
  { id: 'e1-3', source: '1', target: '3' },
  { id: 'e1-2', source: '1', target: '2' },
  { id: 'e1-4', source: '1', target: '4' },
])
```

----------------------------------------

TITLE: Defining Valid Vue Flow Node Configurations (TypeScript)
DESCRIPTION: Provides an example of a ref containing an array of node objects. Each node has required properties like id, label, and position, demonstrating correct structure to avoid the NODE_INVALID error.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/troubleshooting.md#_snippet_1

LANGUAGE: ts
CODE:
```
// Here's an example of some valid node configurations
const nodes = ref([
  { id: '1', type: 'input', label: 'Node 1', position: { x: 250, y: 5 } },
  { id: '2', label: 'Node 2', position: { x: 100, y: 100 } },
  { id: '3', type: 'output', label: 'Node 3', position: { x: 400, y: 200 } },
  { id: '4', type: 'special', label: 'Node 4', position: { x: 400, y: 200 } },
])
```

----------------------------------------

TITLE: Fitting Viewport to All Nodes
DESCRIPTION: Provides an example of calling the `fitView` function on the Vue Flow instance to adjust the viewport's zoom and position so that all nodes are visible. Custom options like padding and including hidden nodes can be provided.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/utils/instance.md#_snippet_3

LANGUAGE: ts
CODE:
```
vueFlowInstance.fitView({ padding: 0.25, includeHiddenNodes: true })
```

----------------------------------------

TITLE: Removing Edges via useVueFlow Composable
DESCRIPTION: Demonstrates using the `removeEdges` action from the `useVueFlow` composable to remove edges programmatically. This action can accept a single edge id or an array of ids and directly updates the graph's state.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/edge.md#_snippet_4

LANGUAGE: vue
CODE:
```
<script setup>
import { ref, onMounted } from 'vue'  
import { VueFlow, useVueFlow } from '@vue-flow/core'

const nodes = ref([
  {
    id: '1',
    position: { x: 50, y: 50 },
    data: { label: 'Node 1', },
  },
  {
    id: '2',
    position: { x: 50, y: 250 },
    data: { label: 'Node 2', },
  },
  {
    id: '3',
    position: { x: 250, y: 50 },
    data: { label: 'Node 3', },
  },
  {
    id: '4',
    position: { x: 250, y: 250 },
    data: { label: 'Node 4', },
  },
])

const edges = ref([
  {
    id: 'e1->2',
    source: '1',
    target: '2',
  },
  {
    id: 'e1->3',
    source: '1',
    target: '3',
  },
  {
    id: 'e2->3',
    source: '2',
    target: '3',
  },
  {
    id: 'e2->4',
    source: '2',
    target: '4',
  },
])

const { removeEdges } = useVueFlow()

function removeOneEdge() {
  removeEdges('e1->2')
}

function removeMultipleEdges() {
  removeEdges(['e1->3', 'e2->3'])
}
</script>

<template>
  <VueFlow :nodes="nodes" :edges="edges">
    <Panel>
      <button @click="removeOneEdge">Remove Edge 1</button>
      <button @click="removeMultipleEdges">Remove Edges 2 and 3</button>
    </Panel>
  </VueFlow>
</template>
```

----------------------------------------

TITLE: Fitting Viewport to Specific Bounds
DESCRIPTION: Demonstrates how to use the `fitBounds` function to fit the viewport to a bounding rectangle calculated from a set of nodes or other elements. This is useful for focusing the view on a subset of the flow.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/utils/instance.md#_snippet_4

LANGUAGE: ts
CODE:
```
vueFlowInstance.fitBounds(getRectOfNodes(nodes.value))
```

----------------------------------------

TITLE: Creating Custom Handle with useHandle (Vue)
DESCRIPTION: Provides the implementation of a custom handle component using the `useHandle` composable, showing how to attach pointer down and click handlers to a custom element to enable connection drawing, mimicking the default handle component's behavior.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/composables.md#_snippet_7

LANGUAGE: typescript
CODE:
```
import { NodeId, useHandle, useVueFlow } from '@vue-flow/core'
import type { HandleProps, Position } from '@vue-flow/core'

const props = withDefaults(defineProps<HandleProps>(), {
  type: 'source',
  position: 'top' as Position,
  connectable: true,
})

const nodeId = inject(NodeId, '')

const { id, hooks, connectionStartHandle } = useVueFlow()

const { handlePointerDown, handleClick } = useHandle({
  nodeId,
  handleId: props.id,
  isValidConnection: props.isValidConnection,
  type: props.type,
})

const onMouseDownHandler = (event: MouseEvent) => handlePointerDown(event)

const onClickHandler = (event: MouseEvent) => handleClick(event)
```

LANGUAGE: typescript
CODE:
```
export default {
  name: 'CustomHandle',
}
```

LANGUAGE: html
CODE:
```
<template>
  <div
    :data-handleid="id"
    :data-nodeid="nodeId"
    :data-handlepos="position"
    class="vue-flow__handle nodrag"
    :class="[
      `vue-flow__handle-${position}`,
      `vue-flow__handle-${id}`,
      {
        source: type !== 'target',
        target: type === 'target',
        connectable: connectable,
        connecting:
          connectionStartHandle?.nodeId === nodeId &&
          connectionStartHandle?.handleId === id &&
          connectionStartHandle?.type === type,
      },
    ]"
    @mousedown="onMouseDownHandler"
    @click="onClickHandler"
  >
    <slot :node-id="nodeId" v-bind="props"></slot>
  </div>
</template>
```

----------------------------------------

TITLE: Updating Node Data Using useNode Vue
DESCRIPTION: This Vue snippet, intended for use within a custom node component, shows how to update the current node's data and properties using the `useNode` composable. `useNode` provides direct access to the reactive node object, allowing simple mutation to trigger updates.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/node.md#_snippet_8

LANGUAGE: Vue
CODE:
```
<!-- CustomNode.vue -->
<script setup>
import { useNode } from '@vue-flow/core'

// `useNode` returns us the node object straight from the state
// since the node obj is reactive, we can mutate it to update our nodes' data
const { node } = useNode()

function onSomeEvent() {
  node.data = {
    ...node.data,  
    hello: 'world',
  }
  
  // you can also mutate properties like `selectable` or `draggable`
  node.selectable = false
  node.draggable = false
}
</script>
```

----------------------------------------

TITLE: Implementing Typed Custom Edge Component - Vue/TypeScript
DESCRIPTION: This component shows how to receive typed edge props, including custom data, using TypeScript's `EdgeProps` generic. It demonstrates accessing the typed custom data passed to the edge component.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/edge.md#_snippet_11

LANGUAGE: vue
CODE:
```
<script setup lang="ts">
import type { EdgeProps } from '@vue-flow/core';
import { BezierEdge } from '@vue-flow/core';

import { CustomData } from './edges'

// props were passed from the slot using `v-bind="customEdgeProps"`
const props = defineProps<EdgeProps<CustomData>>();

console.log(props.data.hello) // 'world'
</script>

<script lang="ts">
export default {
  name: 'CustomEdge',
};
</script>

<template>
  <BezierEdge
      :source-x="sourceX"
      :source-y="sourceY"
      :target-x="targetX"
      :target-y="targetY"
      :source-position="sourcePosition"
      :target-position="targetPosition"
  />
</template>
```

----------------------------------------

TITLE: Adding Nodes by Mutating Prop Vue
DESCRIPTION: These Vue examples show how to add a new node to the graph by directly pushing a node object into the reactive array bound to the `:nodes` prop of the `VueFlow` component. It includes both JavaScript and TypeScript versions.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/node.md#_snippet_3

LANGUAGE: Vue
CODE:
```
<script setup>
import { ref, onMounted } from 'vue'
import { VueFlow, Panel } from '@vue-flow/core'

const nodes = ref([
  {
    id: '1',
    position: { x: 50, y: 50 },
    data: { label: 'Node 1', },
  }
]);

function addNode() {
  const id = Date.now().toString()
  
  nodes.value.push({
    id,
    position: { x: 150, y: 50 },
    data: { label: `Node ${id}`, },
  })
}
</script>

<template>
  <VueFlow :nodes="nodes">
    <Panel>
      <button type="button" @click="addNode">Add a node</button>
    </Panel>
  </VueFlow>
</template>
```

LANGUAGE: Vue
CODE:
```
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import type { Node } from '@vue-flow/core'
import { VueFlow, Panel } from '@vue-flow/core'

const nodes = ref<Node[]>([
  {
    id: '1',
    position: { x: 50, y: 50 },
    data: { label: 'Node 1', },
  }
]);

function addNode() {
  const id = Date.now().toString()

  nodes.value.push({
    id,
    position: { x: 150, y: 50 },
    data: { label: `Node ${id}`, },
  })
}
</script>

<template>
  <VueFlow :nodes="nodes">
    <Panel>
      <button type="button" @click="addNode">Add a node</button>
    </Panel>
  </VueFlow>
</template>
```

----------------------------------------

TITLE: Implementing Custom Node with Resizer | Vue
DESCRIPTION: Vue component demonstrating the implementation of a custom node that includes the NodeResizer component. This enables users to resize the node. It also shows how to add handles for connections and display a label property. Requires the @vue-flow/core and @vue-flow/node-resizer libraries, including the resizer's CSS styles.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/packages/node-resizer/README.md#_snippet_2

LANGUAGE: vue
CODE:
```
<script lang="ts" setup>\nimport { Handle, Position } from '@vue-flow/core'\nimport { NodeResizer } from '@vue-flow/node-resizer'\n\n// make sure to include the necessary styles!\nimport '@vue-flow/node-resizer/dist/style.css'\n\ndefineProps(['label'])\n<\/script>\n\n<template>\n  <NodeResizer min-width="100" min-height="30" />\n\n  <Handle type="target" :position="Position.Left" />\n  <div style="padding: 10px">{{ label }}<\/div>\n  <Handle type="source" :position="Position.Right" />\n<\/template>
```

----------------------------------------

TITLE: Updating Edge Data via useVueFlow
DESCRIPTION: Shows two methods using the `useVueFlow` composable to update an edge: using the `updateEdgeData` method or finding the edge object by ID and mutating its properties directly. This allows reactive changes to an edge's data or visual attributes.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/edge.md#_snippet_5

LANGUAGE: typescript
CODE:
```
import  { useVueFlow } from '@vue-flow/core'

const instance = useVueFlow()

// use the `updateEdgeData` method to update the data of an edge
instance.updateEdgeData(edgeId, { hello: 'mona' })

// find the edge in the state by its id
const edge = instance.findEdge(edgeId)

edge.data = {
  ...edge.data,
  hello: 'world',
}

// you can also mutate properties like `selectable` or `animated`
edge.selectable = !edge.selectable
edge.animated = !edge.animated
```

----------------------------------------

TITLE: Setting Viewport Position and Zoom
DESCRIPTION: Illustrates how to directly set the pan position (`x`, `y`) and zoom level (`zoom`) of the Vue Flow viewport using the `setViewport` function. This allows for programmatic control over the current view.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/utils/instance.md#_snippet_5

LANGUAGE: ts
CODE:
```
vueFlowInstance.setViewport({ x: 100, y: 100, zoom: 1.5 })
```

----------------------------------------

TITLE: Creating Custom Edge Component (Vue)
DESCRIPTION: Demonstrates how to create a custom edge component in Vue Flow by wrapping a default edge component. It shows importing default edge components and defining props using `EdgeProps`. The template wraps a standard `BezierEdge`, allowing for custom logic or rendering around it. Requires Vue Flow and Vue 3 Composition API setup.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/utils/edge.md#_snippet_0

LANGUAGE: vue
CODE:
```
<script lang="ts" setup>
// CustomEdge.vue
import { EdgeProps, BezierEdge, SmoothStepEdge, StepEdge, StraightEdge } from '@vue-flow/core'

const props = defineProps<EdgeProps>()

// do some custom logic
</script>
<template>
  <!-- wrap the bezier edge or do something else in the template -->
  <BezierEdge v-bind="props" />
</template>
```

----------------------------------------

TITLE: Updating Node Internals via Component Emit (Vue)
DESCRIPTION: Provides an alternative method to update node internals from within a custom node component. By defining an `updateNodeInternals` emit and invoking it (`emits('updateNodeInternals')`), the component can signal Vue Flow to update its own handle information.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/handle.md#_snippet_9

LANGUAGE: vue
CODE:
```
<script setup>
const emits = defineEmits(['updateNodeInternals'])

const onSomeEvent = () => {
  emits('updateNodeInternals')
}
</script>
```

----------------------------------------

TITLE: Configuring global node connectable state (Vue)
DESCRIPTION: This example shows how to globally disable the ability to connect nodes using the `nodes-connectable` prop. It also illustrates how to override this global setting for an individual node, making only that specific node connectable by setting its `connectable` property to `true`.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/vue-flow/config.md#_snippet_10

LANGUAGE: Vue
CODE:
```
<script setup>
import { ref } from 'vue'
import { VueFlow } from '@vue-flow/core'

const nodesConnectable = ref(false)
  
const nodes = ref([
  { id: '1', position: { x: 250, y: 5 } },
  {
    id: '2',
    // This will overwrite the globally set option of nodes-connectable
    connectable: true,
    position: { x: 100, y: 100 }
  },
])
</script>

<template>
  <VueFlow :nodes="nodes" :nodes-connectable="nodesConnectable" />
</template>
```

----------------------------------------

TITLE: Limiting Connections with a Function (TypeScript)
DESCRIPTION: Illustrates how to use the `connectable` prop with a function to implement custom logic for limiting connections. The example function (`handleConnectable`) restricts a handle to a maximum of 3 connections based on the `connectedEdges` array passed to the function.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/handle.md#_snippet_6

LANGUAGE: ts
CODE:
```
<script lang="ts" setup>
import { Position, Handle, type HandleConnectableFunc } from '@vue-flow/core'
  
const handleConnectable: HandleConnectableFunc = (node, connectedEdges) => {
  // only allow connections if the node has less than 3 connections
  return connectedEdges.length < 3
}
</script>

<template>
  <Handle type="source" :position="Position.Right" :connectable="handleConnectable" />
</template>
```

----------------------------------------

TITLE: Integrating Custom Node in Vue Flow (Vue)
DESCRIPTION: Demonstrates setting up the Vue Flow component and using a template slot (`#node-custom`) to render a custom node component (`CustomNode`). This setup provides the environment where the Node Resizer component can be used within the custom node definition.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/components/node-resizer.md#_snippet_1

LANGUAGE: vue
CODE:
```
<script setup>
import { ref } from 'vue'
import { VueFlow } from '@vue-flow/core'
import initialNodes from './initialNodes'

// some nodes and edges
const nodes = ref(initialNodes)
</script>

<template>
  <VueFlow :nodes="nodes">
    <template #node-custom="nodeProps">
      <CustomNode :data="nodeProps.data"  />
    </template>
  </VueFlow>
</template>
```

----------------------------------------

TITLE: Integrating Custom Node in Vue Flow
DESCRIPTION: Demonstrates how to set up a Vue Flow instance to use a custom node component. It defines an initial node of type `input-field` and maps this type to the `InputFieldNode` component using a template slot.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/node.md#_snippet_24

LANGUAGE: Vue
CODE:
```
<div class="mt-4 bg-[var(--vp-code-block-bg)] rounded-lg h-50">
  <VueFlow :model-value="[{ id: '1', type: 'input-field', label: 'Node 1', position: { x: 50, y: 50 } }]" >
    <template #node-input-field>
      <InputFieldNode />
    </template>
    <Background class="rounded-lg" />
  </VueFlow>
</div>
```

----------------------------------------

TITLE: Updating Node Internals via Store Action (JavaScript)
DESCRIPTION: Shows how to use the `updateNodeInternals` function obtained from `useVueFlow()`. This function is used to notify Vue Flow to re-calculate the positions and sizes of handles for specified nodes, which is necessary when handles are dynamically added or their positions change.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/handle.md#_snippet_8

LANGUAGE: js
CODE:
```
import { useVueFlow } from '@vue-flow/core'

const { updateNodeInternals } = useVueFlow()

const onSomeEvent = () => {
  updateNodeInternals(['1'])
}
```

----------------------------------------

TITLE: Adding Elements to Zoom Pane in Vue Flow (Vue)
DESCRIPTION: This snippet shows how to utilize the '#zoom-pane' slot within Vue Flow. Elements placed inside this slot will be rendered inside the viewport transformation, meaning they will scale and move along with the current zoom level and position of the graph.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/vue-flow/slots.md#_snippet_1

LANGUAGE: vue
CODE:
```
<template>
  <VueFlow>
    <template #zoom-pane>
      <div>Some element inside the zoom pane</div>
    </template>
  </VueFlow>
</template>
```

----------------------------------------

TITLE: Applying Changes Manually Vue Flow JavaScript
DESCRIPTION: This JavaScript snippet shows how to use the `applyNodeChanges` and `applyEdgeChanges` functions from the `useVueFlow` composable. These functions are used to manually apply an array of change objects (obtained from `onNodesChange` or `onEdgesChange`) to the Vue Flow instance when automatic application is disabled.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/controlled-flow.md#_snippet_4

LANGUAGE: javascript
CODE:
```
import { useVueFlow } from '@vue-flow/core';

const { applyNodeChanges, applyEdgeChanges } = useVueFlow();

const onChange = (changes) => {
  // apply changes manually
  applyNodeChanges(changes);
};
```

----------------------------------------

TITLE: Removing Nodes by Filtering Prop Vue
DESCRIPTION: This Vue example shows how to remove nodes from the graph by filtering the reactive array bound to the `:nodes` prop. When the array is updated, Vue Flow reacts to the change and removes the corresponding node from the rendering.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/node.md#_snippet_5

LANGUAGE: Vue
CODE:
```
<script setup>
import { ref } from 'vue'
import { VueFlow, Panel } from '@vue-flow/core'

const nodes = ref([
  {
    id: '1',
    position: { x: 50, y: 50 },
    data: { label: 'Node 1' },
  },
  {
    id: '2',
    position: { x: 150, y: 50 },
    data: { label: 'Node 2' },
  }
])

function removeNode(id) {
  nodes.value = nodes.value.filter((node) => node.id !== id)
}
</script>

<template>
  <VueFlow :nodes="nodes">
    <Panel>
      <button type="button" @click="removeNode('1')">Remove Node 1</button>
      <button type="button" @click="removeNode('2')">Remove Node 2</button>
    </Panel>
  </VueFlow>
</template>
```

----------------------------------------

TITLE: Defining Custom Node and Elements | Vue Flow | Vue
DESCRIPTION: Defines a functional Vue component `CustomNode` using the `h` (hyperscript) render function to create handles and display a label. It also initializes a reactive `elements` array containing data for basic nodes and an edge for a sample Vue Flow diagram.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/theming.md#_snippet_0

LANGUAGE: vue
CODE:
```
<script setup>
import LogosJavascript from '~icons/logos/javascript';
import { ref, h } from 'vue';
import { Handle, Position, VueFlow } from '@vue-flow/core';
import { Background } from '@vue-flow/background';

const CustomNode = (props) => h('div', [
  h(Handle, { connectable: false, type: 'target', position: Position.Top }),
  h('div', props.label),
  h(Handle, { connectable: false, type: 'source', position: Position.Bottom }),
]);

const elements = ref([
  { id: '1', label: 'Node 1', position: { x: 0, y: 0 }, draggable: false, deletable: false, selectable: false, type: 'custom' },
  { id: '2', label: 'Node 2', position: { x: 75, y: 75 }, draggable: false, deletable: false, selectable: false, type: 'custom' },
  { id: 'e1-2', source: '1', target: '2', animated: true, selectable: false, deletable: false },
])
</script>
```

----------------------------------------

TITLE: Calculating Bezier Edge Path (Vue/TypeScript)
DESCRIPTION: Illustrates the usage of the `getBezierPath` utility function from Vue Flow to generate a Bezier curve path string for an edge. It takes edge properties and an optional curvature value. The computed property `edgePathParams` stores the result, and the path string is passed to a `BaseEdge` component for rendering. Requires `BaseEdge`, `getBezierPath`, and `EdgeProps` from `@vue-flow/core`.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/utils/edge.md#_snippet_1

LANGUAGE: vue
CODE:
```
<script lang="ts" setup>
import { computed } from "vue"
import { BaseEdge, getBezierPath, EdgeProps } from '@vue-flow/core'

const props = defineProps<EdgeProps>()

const edgePathParams = computed(() => getBezierPath({ ...props, curvature: 0.5 }))
</script>

<template>
  <BaseEdge :path="edgePathParams[0]" />
</template>
```

----------------------------------------

TITLE: Configuring Autopan Options (Vue Template)
DESCRIPTION: Shows how to configure autopan behavior directly on the `<VueFlow>` component in a Vue template using props. This method sets the `autoPanOnNodeDrag` and `autoPanOnConnect` boolean properties to control automatic panning when dragging nodes or drawing connections. Requires the `vue-flow` library component.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/packages/core/CHANGELOG.md#_snippet_10

LANGUAGE: vue
CODE:
```
<VueFlow
  v-model="elements"
  :autoPanOnNodeDrag="true"
  :autoPanOnConnect="true"
/>
```

----------------------------------------

TITLE: Removing Edges by Mutating Prop Array in Vue Flow
DESCRIPTION: Shows how to remove edges from the graph by filtering the reactive array passed to the `:edges` prop. A function is used to update the ref's value, triggering a re-render of the graph without the removed edge.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/edge.md#_snippet_3

LANGUAGE: vue
CODE:
```
<script setup>
import { ref, onMounted } from 'vue'
import { VueFlow, Panel } from '@vue-flow/core'

const nodes = ref([
  {
    id: '1',
    position: { x: 50, y: 50 },
    data: { label: 'Node 1', },
  },
  {
    id: '2',
    position: { x: 50, y: 250 },
    data: { label: 'Node 2', },
  },
]);

const edges = ref([
  {
    id: 'e1->2',
    source: '1',
    target: '2',
  }
]);

function removeEdge(id) {
  edges.value = edges.value.filter((edge) => edge.id !== id)
}
</script>

<template>
  <VueFlow :nodes="nodes" :edges="edges">
    <Panel>
      <button @click="removeEdge('e1->2')">Remove Edge</button>
    </Panel>
  </VueFlow>
</template>
```

----------------------------------------

TITLE: Removing Nodes with API Vue Flow JavaScript
DESCRIPTION: This snippet shows how to remove a node using the `removeNodes` function obtained from the `useVueFlow` composable. Using API functions like `removeNodes` *will* emit a Vue Flow 'change' event, allowing you to intercept and process the change.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/controlled-flow.md#_snippet_1

LANGUAGE: javascript
CODE:
```
import { ref } from 'vue';
import { useVueFlow } from '@vue-flow/core';

const nodes = ref([
  {
    id: '1',
    position: { x: 0, y: 0 },
    data: { label: 'Node 1' },
  },
]);

// this function *will* emit a change
const { removeNodes } = useVueFlow();

removeNodes('1');
```

----------------------------------------

TITLE: Implementing Node Resizer in Custom Node (Vue)
DESCRIPTION: Shows the implementation of a custom node component incorporating the `NodeResizer` component along with standard `Handle`s. It includes importing the necessary styles and components, defining props, and setting minimum width and height for the resizer.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/components/node-resizer.md#_snippet_2

LANGUAGE: vue
CODE:
```
<script lang="ts" setup>
import { Handle, Position } from '@vue-flow/core'
import { NodeResizer } from '@vue-flow/node-resizer'

// make sure to include the necessary styles!
import '@vue-flow/node-resizer/dist/style.css'

defineProps(['data'])
</script>

<template>
  <NodeResizer min-width="100" min-height="30" />

  <Handle type="target" :position="Position.Left" />
  <Handle type="source" :position="Position.Right" />
</template>
```

----------------------------------------

TITLE: Updating Node with updateNode in TypeScript
DESCRIPTION: This snippet shows how to use the `updateNode` action obtained from `useVueFlow` to modify a node's properties. It supports updating with an object or a function, and an option to replace the node entirely instead of merging properties.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/packages/core/CHANGELOG.md#_snippet_3

LANGUAGE: ts
CODE:
```
const { updateNode } = useVueFlow();

updateNode("1", { position: { x: 100, y: 100 } });

// or using a function to update the node
updateNode("1", (node) => ({ ...node, position: { x: 100, y: 100 } }));

// passing options - `replace` will replace the node instead of merging it
updateNode(
  "1",
  { id: "1", label: "Node 1", position: { x: 100, y: 100 } },
  { replace: true }
);
```

----------------------------------------

TITLE: Configuring global node draggable state (Vue)
DESCRIPTION: This snippet demonstrates how to globally disable node dragging using the `nodes-draggable` prop on the `VueFlow` component. It also shows how to override the global setting to enable dragging for a specific node by setting the `draggable` property on the node object itself.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/vue-flow/config.md#_snippet_9

LANGUAGE: Vue
CODE:
```
<script setup>
import { ref } from 'vue'
import { VueFlow } from '@vue-flow/core'

const nodesDraggable = ref(false)

const nodes = ref([
  { id: '1', position: { x: 250, y: 5 } },
  { 
    id: '2', 
    // This will overwrite the globally set option of nodes-draggable
    draggable: true, 
    position: { x: 100, y: 100 } 
  },
])
</script>

<template>
  <VueFlow :nodes="nodes" :nodes-draggable="nodesDraggable" />
</template>
```

----------------------------------------

TITLE: Custom Node Component with Integrated Node Toolbar (Vue)
DESCRIPTION: Defines a custom Vue Flow node component incorporating the `<NodeToolbar>`. It uses Vue's Composition API and props to dynamically control the toolbar's visibility and position. The snippet shows how to include the toolbar component and basic Handles within the node's template.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/packages/node-toolbar/README.md#_snippet_2

LANGUAGE: vue
CODE:
```
<script lang="ts" setup>
import { Handle, Position } from '@vue-flow/core'
import { NodeToolbar } from '@vue-flow/node-toolbar'

interface NodeData {
  toolbarVisible: boolean
  toolbarPosition: Position
}

interface Props {
  data: NodeData
  label: string
}

defineProps<Props>()
</script>

<template>
  <NodeToolbar :is-visible="data.toolbarVisible" :position="data.toolbarPosition">
    <button>delete</button>
    <button>copy</button>
    <button>expand</button>
  </NodeToolbar>

  <div :style="{ padding: '10px 20px' }">
    {{ label }}
  </div>

  <Handle type="target" :position="Position.Left" />
  <Handle type="source" :position="Position.Right" />
</template>
```

----------------------------------------

TITLE: Using a custom connector function for auto-connect (Vue)
DESCRIPTION: This snippet demonstrates providing a custom `connector` function to the `auto-connect` prop. The function receives connection parameters and can return an edge object to be added or `false` to prevent connection, enabling custom validation or edge property assignment like preventing self-connecting edges and adding labels or animation.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/vue-flow/config.md#_snippet_14

LANGUAGE: Vue
CODE:
```
<script setup>
import { ref } from 'vue'

const nodes = ref([/** ... */])

const edges = ref([/** ... */])

const connector = (params) => {
  if (params.source === params.target) {
    return false
  }
  
  return {
    id: `edge-${params.source}-${params.target}`,
    source: params.source,
    target: params.target,
    label: `Edge ${params.source}-${params.target}`,
    animated: true,
  }
}
</script>

<template>
  <VueFlow :nodes="nodes" :edges="edges" :auto-connect="connector" />
</template>
```

----------------------------------------

TITLE: Defining Initial Vue Flow Elements with Pathfinding Edge (JavaScript)
DESCRIPTION: Defines an array of initial elements (nodes and edges) for a Vue Flow diagram. It demonstrates how to define standard nodes and, more importantly, how to configure an edge (`e12`) to specifically use the custom `'pathfinding'` type by setting the `type` property, enabling the pathfinding logic for that connection.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/packages/pathfinding-edge/README.md#_snippet_2

LANGUAGE: js
CODE:
```
// initial-elements.js
export default [
  {
    id: 'e12',
    source: '1',
    target: '2',
    label: 'Smart Edge',
    style: { stroke: 'red' },
    // assign pathfinding edge type
    type: 'pathfinding'
  },
  {
    id: '1',
    label: 'Node 1',
    position: {
      x: 430,
      y: 0,
    },
  },
  {
    id: '2',
    label: 'Node 2',
    position: {
      x: 230,
      y: 90,
    },
  },
]
```

----------------------------------------

TITLE: Initializing Vue Flow Viewport (Event Listener)
DESCRIPTION: Illustrates how to use an event listener (`@pane-ready`) directly on the `<VueFlow>` component to trigger an action, such as fitting the view, when the pane is ready. This method is suitable for Vue 2 or Vue 3 Options API.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/utils/instance.md#_snippet_1

LANGUAGE: vue
CODE:
```
<script>
import { VueFlow } from '@vue-flow/core'

export default defineComponent({
  components: { VueFlow },
  data() {
    return {
      instance: null,
    }
  },
  methods: {
    onPaneReady(vueFlowInstance) {
      vueFlowInstance.fitView()
      this.instance = vueFlowInstance
    }
  }
})
</script>
<template>
  <VueFlow @pane-ready="onPaneReady" />
</template>
```

----------------------------------------

TITLE: Calculating Smooth Step Path (Vue/TypeScript)
DESCRIPTION: Illustrates the use of the `getSmoothStepPath` utility function from Vue Flow to generate a smooth step path string for an edge. It takes edge properties and options like `borderRadius`. The computed property `edgePathParams` holds the result, which is passed to a `BaseEdge` component. Setting `borderRadius: 0` produces a standard step path. Requires `BaseEdge`, `getSmoothStepPath`, and `EdgeProps` from `@vue-flow/core`.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/utils/edge.md#_snippet_3

LANGUAGE: vue
CODE:
```
<script lang="ts" setup>
import { computed } from "vue"
import { BaseEdge, getSmoothStepPath, EdgeProps } from '@vue-flow/core'

const props = defineProps<EdgeProps>()

const edgePathParams = computed(() => getSmoothStepPath({ ...props, borderRadius: 8 }))
</script>

<template>
  <BaseEdge :path="edgePathParams[0]" />
</template>
```

----------------------------------------

TITLE: Integrating Vue Flow Controls Component (Vue)
DESCRIPTION: Demonstrates how to add the Controls component to a Vue Flow instance. It requires importing the component, the main VueFlow component, and importantly, the controls-specific CSS styles. The component is then placed as a child within the <VueFlow> template.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/components/controls.md#_snippet_1

LANGUAGE: vue
CODE:
```
<script setup>
import { VueFlow } from '@vue-flow/core'
import { Controls } from '@vue-flow/controls'

// import default controls styles
import '@vue-flow/controls/dist/style.css'
</script>

<template>
  <VueFlow>
    <Controls />
  </VueFlow>
</template>
```

----------------------------------------

TITLE: Calculating Simple Bezier Path (Vue/TypeScript)
DESCRIPTION: Demonstrates using the `getSimpleBezierPath` utility function from Vue Flow. This function calculates a simpler Bezier path compared to `getBezierPath`. It takes edge properties and returns the path string and label positions. The computed property `edgePathParams` stores the result, used with a `BaseEdge` component. Requires `BaseEdge`, `getSimpleBezierPath`, and `EdgeProps` from `@vue-flow/core`.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/utils/edge.md#_snippet_2

LANGUAGE: vue
CODE:
```
<script lang="ts" setup>
import { computed } from "vue"
import { BaseEdge, getSimpleBezierPath, EdgeProps } from '@vue-flow/core'

const props = defineProps<EdgeProps>()

const edgePathParams = computed(() => getSimpleBezierPath(props))
</script>

<template>
  <BaseEdge :path="edgePathParams[0]" />
</template>
```

----------------------------------------

TITLE: Defining Global CSS Variables | Vue Flow | CSS
DESCRIPTION: Illustrates how to define custom CSS variables within a `:root` selector to override default theme values globally. These variables (`--vf-node-bg`, `--vf-node-text`, etc.) provide a convenient way to adjust common styling properties across the entire diagram.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/theming.md#_snippet_5

LANGUAGE: css
CODE:
```
/* Global default CSS variable values */
:root {
    --vf-node-bg: #fff;
    --vf-node-text: #222;
    --vf-connection-path: #b1b1b7;
    --vf-handle: #555;
}
```

----------------------------------------

TITLE: Creating a Custom Node Component (Vue/TS)
DESCRIPTION: This custom Vue node component example uses TypeScript to define its props based on `NodeProps`, incorporating custom data and event types defined elsewhere. It logs a property from the custom data to the console.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/node.md#_snippet_16

LANGUAGE: vue
CODE:
```
<script setup lang="ts">
import type { NodeProps } from '@vue-flow/core'  
import { Position } from '@vue-flow/core'

import { CustomData, CustomEvents } from './nodes'

// props were passed from the slot using `v-bind="customNodeProps"`
const props = defineProps<NodeProps<CustomData, CustomEvents>>()
  
console.log(props.data.hello) // 'world'
</script>

<template>
  <div>
    <Handle type="target" :position="Position.Top" />
    <div>{{ label }}</div>
    <Handle type="source" :position="Position.Bottom" />
  </div>
</template>
```

----------------------------------------

TITLE: Disabling Default State Handlers in VueFlow (Vue)
DESCRIPTION: This snippet demonstrates setting the `apply-default` prop to `false` on the `<VueFlow>` component. This disables the default internal state mutation handlers, allowing developers to take full control of state changes, as is necessary for implementing controlled flow patterns.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/vue-flow/config.md#_snippet_8

LANGUAGE: vue
CODE:
```
<template>
  <VueFlow :apply-default="false" />
</template>
```

----------------------------------------

TITLE: Using EdgeLabelRenderer Component in Vue Flow
DESCRIPTION: Provides a detailed example of creating a custom edge component that uses the 'EdgeLabelRenderer' to render HTML elements like buttons directly in the flow's HTML context, outside the SVG. This is essential for adding interactive controls or complex HTML structures to edge labels. Requires defining edge props and using the provided 'getBezierPath' utility.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/packages/core/CHANGELOG.md#_snippet_13

LANGUAGE: vue
CODE:
```
<script lang="ts" setup>
import type { EdgeProps, Position } from "@vue-flow/core";
import { EdgeLabelRenderer, getBezierPath, useVueFlow } from "@vue-flow/core";
import type { CSSProperties } from "vue";

interface CustomEdgeProps<T = any> extends EdgeProps<T> {
  id: string;
  sourceX: number;
  sourceY: number;
  targetX: number;
  targetY: number;
  sourcePosition: Position;
  targetPosition: Position;
  data: T;
  markerEnd: string;
  style: CSSProperties;
}

const props = defineProps<CustomEdgeProps>();

const { removeEdges } = useVueFlow();

const path = $computed(() => getBezierPath(props));
</script>
```

LANGUAGE: vue
CODE:
```
<script lang="ts">
export default {
  inheritAttrs: false,
};
</script>
```

LANGUAGE: vue
CODE:
```
<template>
  <path
    :id="id"
    :style="style"
    class="vue-flow__edge-path"
    :d="path[0]"
    :marker-end="markerEnd"
  />

  <EdgeLabelRenderer>
    <div
      :style="{
        pointerEvents: 'all',
        position: 'absolute',
        transform: `translate(-50%, -50%) translate(${path[1]}px,${path[2]}px)`,
      }"
      class="nodrag nopan"
    >
      <button class="edgebutton" @click="removeEdges([id])">×</button>
    </div>
  </EdgeLabelRenderer>
</template>
```

LANGUAGE: css
CODE:
```
<style>
.edgebutton {
  border-radius: 999px;
  cursor: pointer;
}
.edgebutton:hover {
  box-shadow: 0 0 0 2px pink, 0 0 0 4px #f05f75;
}
</style>
```

----------------------------------------

TITLE: Basic Usage of Vue Flow Controls (Vue)
DESCRIPTION: Shows how to integrate the Controls component into a Vue 3 setup script component. It imports necessary components and styles, sets up reactive elements for nodes/edges, and renders the VueFlow component with the Controls component placed inside.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/packages/controls/README.md#_snippet_1

LANGUAGE: vue
CODE:
```
<script setup>
import { VueFlow } from '@vue-flow/core'
import { Controls } from '@vue-flow/controls'

// import default controls styles
import '@vue-flow/controls/dist/style.css'

import initialElements from './initial-elements'


// some nodes and edges
const elements = ref(initialElements)
</script>

<template>
  <VueFlow v-model="elements" fit-view-on-init class="vue-flow-basic-example">
    <Controls />
  </VueFlow>
</template>
```

----------------------------------------

TITLE: Styling Custom Nodes in Vue Flow CSS
DESCRIPTION: Provides basic CSS styling for a user-defined node type in Vue Flow. Custom node types require explicit styling as they have no default appearance.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/node.md#_snippet_20

LANGUAGE: CSS
CODE:
```
.vue-flow__node-custom {
    background: #9CA8B3;
    color: #fff;
    padding: 10px;
}
```

----------------------------------------

TITLE: Styling Custom Node Class | Vue Flow | CSS
DESCRIPTION: Provides CSS rules specifically targeting the `.vue-flow__node-custom` class, which is applied to custom node types. It demonstrates how to modify background color, text color, border, border-radius, box-shadow, and padding using standard CSS properties.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/theming.md#_snippet_2

LANGUAGE: css
CODE:
```
/* Use a purple theme for our custom node */
.vue-flow__node-custom {
    background: purple;
    color: white;
    border: 1px solid purple;
    border-radius: 4px;
    box-shadow: 0 0 0 1px purple;
    padding: 8px;
}
```

----------------------------------------

TITLE: Using useVueFlow in Options API with ID and Event Handling (Vue/JS)
DESCRIPTION: Explains how to use `useVueFlow` within a Vue component defined using the Options API. It demonstrates providing a unique `id` to `useVueFlow`, accessing state methods (`addEdges`), defining component data and methods, and registering event handlers (`onConnect`) within a lifecycle hook (`beforeMount`) or via regular template event bindings (`@connect`).
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/vue-flow/state.md#_snippet_4

LANGUAGE: vue
CODE:
```
<script>
import { VueFlow, useVueFlow } from '@vue-flow/core'

const { addEdges, onConnect } = useVueFlow({ id: 'options-api' })
export default defineComponent({
  components: { VueFlow },
  data() {
    return {
      nodes: [
        {
          id: '1',
          position: { x: 0, y: 0},
          data: { label: 'Node 1' }
        }
      ],
      edges: [],
    }
  },
  methods: {
    // regular event handler
    handleConnect: (params) => {
      addEdges([params])
    }
  },
  beforeMount() {
    // Register your event handler, can technically be called in any lifecycle phase
    // Skip this if you're using regular event handlers
    onConnect((params) => addEdges([params]))
  }
})
</script>

<template>
  <VueFlow id="options-api" :nodes="nodes" :edges="edges" @connect="handleConnect" />
</template>
```

----------------------------------------

TITLE: Defining Typed Custom Edges with Slots - Vue/TypeScript
DESCRIPTION: This TypeScript example shows how to use generics with the `Edge` type to define custom data structures (`CustomData`) and restrict allowed edge types (`CustomEdgeTypes`), providing type safety for your edge definitions. It also demonstrates how an invalid type results in a type error but defaults to a standard edge.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/edge.md#_snippet_10

LANGUAGE: vue
CODE:
```
<script setup lang="ts">
import { ref } from 'vue'
import type { Edge } from '@vue-flow/core'
import { VueFlow } from '@vue-flow/core'

import CustomEdge from './CustomEdge.vue'
import SpecialEdge from './SpecialEdge.vue'

// You can pass 3 optional generic arguments to the Edge type, allowing you to define:
// 1. The data object type
// 2. The events object type
// 3. The possible edge types

interface CustomData {
    hello: string
}

type CustomEdgeTypes = 'custom' | 'special'

type CustomEdge = Edge<CustomData, any, CustomEdgeTypes>

export const edges = ref<CustomEdge[]>([
    {
      id: 'e1-2',
      source: '1',
      target: '2',
      // this will create the edge-type `custom`
      type: 'custom',
    },
    {
      id: 'e1-3',
      source: '1',
      target: '3',
      // this will create the edge-type `special`
      type: 'special',
    },
    
    {
      id: 'e1-4',
      source: '1',
      target: '4',
      // this will throw a type error, as the type is not defined in the CustomEdgeTypes
      // regardless it would be rendered as a default edge type
      type: 'not-defined',
    }
])
  
const nodes = ref([
  {
    id: '1',
    position: { x: 50, y: 50 },
    data: { label: 'Node 1', },
  },
  {
    id: '2',
    position: { x: 50, y: 250 },
    data: { label: 'Node 2', },
  },
  {
    id: '3',
    position: { x: 250, y: 50 },
    data: { label: 'Node 3', },
  },
  {
    id: '4',
    position: { x: 250, y: 250 },
    data: { label: 'Node 4', },
  },
])  
</script>

<template>
  <VueFlow :nodes="nodes" :edges="edges">
    <template #edge-custom="customEdgeProps">
      <CustomEdge v-bind="customEdgeProps" />
    </template>
    
    <template #edge-special="specialEdgeProps">
      <SpecialEdge v-bind="specialEdgeProps" />
    </template>
  </VueFlow>
</template>
```

----------------------------------------

TITLE: Setting Strict Connection Mode (Vue)
DESCRIPTION: Demonstrates how to configure the `<VueFlow>` component to use `ConnectionMode.Strict`. This mode restricts connections to only be allowed between `source` and `target` type handles, disallowing source-to-source or target-to-target connections.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/handle.md#_snippet_7

LANGUAGE: vue
CODE:
```
<script setup>
import { ConnectionMode, VueFlow } from '@vue-flow/core'
</script>

<template>
  <VueFlow :connection-mode="ConnectionMode.Strict" />
</template>
```

----------------------------------------

TITLE: Initializing Vue Flow State for Injection (Options API Context)
DESCRIPTION: Illustrates how to initialize a Vue Flow state instance using `useVueFlow()` directly in the script body of a component (before setup/methods are processed). This makes the state instance available for injection into child components within the current component tree, useful for avoiding prop drilling.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/vue-flow/state.md#_snippet_1

LANGUAGE: vue
CODE:
```
<script>
// Container.vue
import { useVueFlow  } from '@vue-flow/core'

// initialize a store instance in this context, so it is available when calling inject(VueFlow)
useVueFlow()
</script>
```

----------------------------------------

TITLE: Integrating Vue Flow Minimap Component (Vue)
DESCRIPTION: Demonstrates a basic Vue 3 setup using `<script setup>` showing how to import `VueFlow` and `MiniMap`, include the default styles, bind data using `v-model`, and render the MiniMap component inside the main VueFlow container for a quick start.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/packages/minimap/README.md#_snippet_1

LANGUAGE: vue
CODE:
```
<script setup>
import { VueFlow } from '@vue-flow/core'
import { MiniMap } from '@vue-flow/minimap'

// import default minimap styles
import '@vue-flow/minimap/dist/style.css'

import initialElements from './initial-elements'

// some nodes and edges
const elements = ref(initialElements)
</script>

<template>
  <VueFlow v-model="elements" fit-view-on-init class="vue-flow-basic-example">
    <MiniMap />
  </VueFlow>
</template>
```

----------------------------------------

TITLE: Implementing Pathfinding Edge in Vue Flow (Vue)
DESCRIPTION: Shows how to integrate the `PathFindingEdge` component into a Vue Flow instance. It imports necessary components, sets up initial elements, fetches nodes using `useVueFlow`, and utilizes a template slot (`#edge-pathfinding`) to replace the default edge rendering with the custom pathfinding edge component.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/packages/pathfinding-edge/README.md#_snippet_1

LANGUAGE: vue
CODE:
```
<script setup>
import { VueFlow, useVueFlow } from '@vue-flow/core'
import { PathFindingEdge } from '@vue-flow/pathfinding-edge'
import initialElements from './initial-elements'

const elements = ref(initialElements)

// create a new context so we can fetch nodes
const { getNodes } = useVueFlow()
</script>
<template>
  <div style="height: 300px">
    <VueFlow v-model="elements">
      <template #edge-pathfinding="props">
        <PathFindingEdge v-bind="props" :nodes="getNodes" />
      </template>
    </VueFlow>
  </div>
</template>
```

----------------------------------------

TITLE: Using Vue Flow Background Component (Vue)
DESCRIPTION: A quickstart example demonstrating how to import and use the Background component within a Vue Flow instance. It shows basic template integration alongside the core VueFlow component.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/packages/background/README.md#_snippet_1

LANGUAGE: vue
CODE:
```
<script setup>
import { VueFlow } from '@vue-flow/core'
import { Background } from '@vue-flow/background'
import initialElements from './initial-elements'

// some nodes and edges
const elements = ref(initialElements)
</script>

<template>
  <VueFlow v-model="elements" fit-view-on-init class="vue-flow-basic-example">
    <Background />
  </VueFlow>
</template>
```

----------------------------------------

TITLE: Defining Output Nodes (Vue Flow/TypeScript)
DESCRIPTION: This snippet illustrates how to define an output node in Vue Flow using a ref. Output nodes typically represent conclusion points in a flow and have a single target handle, usually positioned at the top by default.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/node.md#_snippet_12

LANGUAGE: typescript
CODE:
```
import { ref } from 'vue'
import { Position } from '@vue-flow/core'

const nodes = ref([
  {
    id: '1',
    type: 'output',
    targetPosition: Position.Top, // or Bottom, Left, Right,
    data: { label: 'Output Node' },
  }
])
```

----------------------------------------

TITLE: Accessing Current useNodesData Result - TypeScript
DESCRIPTION: This snippet demonstrates the current method (from v1.33.0 onwards) for accessing the result of the `useNodesData` composable. The composable now returns an array of objects, each containing `id`, `type`, and `data` properties, requiring destructuring during iteration.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/packages/core/CHANGELOG.md#_snippet_1

LANGUAGE: typescript
CODE:
```
const nodesData = useNodesData(nodeIds);

// Now
nodesData.forEach(({ id, type, data }) => {
  // ...
});
```

----------------------------------------

TITLE: Retrieving Node Data with useNodesData (TypeScript)
DESCRIPTION: Explains how `useNodesData` can be used in conjunction with `useHandleConnections` to fetch the data property of connected nodes, providing access to the actual data associated with nodes in a flow.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/composables.md#_snippet_4

LANGUAGE: typescript
CODE:
```
import { useNodesData, useHandleConnections } from '@vue-flow/core'

// get all connections where this node is the target (incoming connections)
const connections = useHandleConnections({
  type: 'target',
})

const data = useNodesData(() => connections.value.map((connection) => connection.source))

console.log(data.value) // [{ /* ... *//*]
```

----------------------------------------

TITLE: Updating Nodes Draggable Prop in VueFlow (Vue)
DESCRIPTION: This snippet shows how to dynamically update the `nodes-draggable` prop by binding it to a reactive ref (`nodesDraggable`). A function `toggleNodesDraggable` modifies the ref's value, which automatically updates the component prop, demonstrating reactive configuration changes.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/vue-flow/config.md#_snippet_1

LANGUAGE: vue
CODE:
```
<script setup>
const nodesDraggable = ref(false)

const toggleNodesDraggable = () => {
  // toggle the state
  nodesDraggable.value = !nodesDraggable.value
}
</script>
<template>
  <VueFlow :nodes-draggable="nodesDraggable">...</VueFlow>
</template>
```

----------------------------------------

TITLE: Installing Vue Flow Core with Yarn
DESCRIPTION: Installs the `@vue-flow/core` package using the Yarn package manager. This is another popular tool for managing project dependencies in JavaScript/TypeScript projects. Requires Node.js and Yarn to be installed.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/getting-started.md#_snippet_2

LANGUAGE: Shell
CODE:
```
yarn add @vue-flow/core
```

----------------------------------------

TITLE: Checking if Element is Edge (Vue)
DESCRIPTION: This Vue component demonstrates how to use the `isEdge` utility function from `@vue-flow/core` to identify if an element in the `elements` array is an edge. It includes a button that toggles a CSS class ('light'/'dark') on all elements identified as edges. Requires `@vue-flow/core` and Vue's `ref`. Inputs are the initial `elements` array; the output is the modified `elements` array with updated classes on edges.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/utils/graph.md#_snippet_0

LANGUAGE: javascript
CODE:
```
import { VueFlow, isEdge } from '@vue-flow/core'

const elements = ref([
  { id: '1', position: { x: 250, y: 5 }, },
  { id: '2', position: { x: 100, y: 100 }, },

  { id: 'e1-2', source: '1', target: '2', class: 'light' },
])

const toggleClass = () => {
  elements.value.forEach((el) => {
    if (isEdge(el)) {
      el.class = el.class === 'light' ? 'dark' : 'light'
    }
  })
}
```

LANGUAGE: vue
CODE:
```
<template>
  <VueFlow v-model="elements">
    <button @click="toggleClass">Toggle classes</button>
  </VueFlow>
</template>
```

----------------------------------------

TITLE: Getting Handle Connections with useHandleConnections (TypeScript)
DESCRIPTION: Illustrates how to use `useHandleConnections` to retrieve an array of connections associated with a handle, allowing filtering by type ('source' or 'target') and specifying handle/node IDs. Includes examples of using `onConnect` and `onDisconnect` callbacks.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/composables.md#_snippet_2

LANGUAGE: typescript
CODE:
```
import { type HandleConnection, useHandleConnections } from '@vue-flow/core'

// get all connections where this node is the target (incoming connections)
const targetConnections = useHandleConnections({
  // type is required
  type: 'target',
})

// get all connections where this node is the source (outgoing connections)
const sourceConnections = useHandleConnections({
  type: 'source',
})

const connections = useHandleConnections({
  id: 'handle-1', // you can explicitly pass a handle id if there are multiple handles of the same type
  nodeId: '1', // you can explicitly pass a node id, otherwise it's used from the `NodeId  injection
  type: 'target',
  onConnect: (connections: HandleConnection[]) => {
    // do something with the connections
  },
  onDisconnect: (connections: HandleConnection[]) => {
    // do something with the connections
  },
})
```

----------------------------------------

TITLE: Install Dependencies Bash
DESCRIPTION: This snippet shows how to install the project dependencies using popular package managers. It includes commands for yarn, npm, and pnpm, highlighting the different syntaxes for each tool.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/examples/nuxt3/README.md#_snippet_0

LANGUAGE: bash
CODE:
```
# yarn
yarn install

# npm
npm install

# pnpm
pnpm install --shamefully-hoist
```

----------------------------------------

TITLE: Integrating ControlButton within Controls (Vue)
DESCRIPTION: This snippet shows the standard way to include a custom control button in a Vue Flow diagram. The ControlButton component must be used as a child element within the Controls component, which is in turn placed inside the main VueFlow component. The content inside the ControlButton tag (like the <i> tag here) will be rendered within the button element.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/components/control-button.md#_snippet_0

LANGUAGE: vue
CODE:
```
<template>\n  <VueFlow>\n    <Controls>\n      <ControlButton>\n        <i class=\"fa fa-plus\"></i>\n      </ControlButton>\n    </Controls>\n  </VueFlow>\n</template>
```

----------------------------------------

TITLE: Handling Vue Flow Errors with onError Event (Vue/TypeScript)
DESCRIPTION: Demonstrates how to use the onError event handler provided by useVueFlow or directly on the component to catch and process errors occurring at runtime. It shows how to use isErrorOfType and ErrorCode to specifically handle a NODE_INVALID error. Dependencies are @vue-flow/core.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/troubleshooting.md#_snippet_4

LANGUAGE: ts
CODE:
```
<script lang="ts" setup>
import { isErrorOfType, ErrorCode, useVueFlow, VueFlowError, VueFlow } from '@vue-flow/core'

const { onError } = useVueFlow()

onError(handleError)

function handleError(error: VueFlowError) {
  if (isErrorOfType(error, ErrorCode.NODE_INVALID)) {
    const [nodeId] = error.args
    // handle the error
  }
}
</script>
```

LANGUAGE: vue
CODE:
```
<template>
  <VueFlow @error="handleError" />
</template>
```

----------------------------------------

TITLE: Getting Node Connections with useNodeConnections (TypeScript)
DESCRIPTION: Shows how to use `useNodeConnections` to retrieve all connections for a given node, filtering by handle type ('source' or 'target') or a specific handle ID. Useful for accessing all incoming or outgoing connections for a node.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/composables.md#_snippet_3

LANGUAGE: typescript
CODE:
```
import { type HandleConnection, useNodeConnections } from '@vue-flow/core'

// get all connections where this node is the target (incoming connections)
const targetConnections = useNodeConnections({
  // type is required
  handleType: 'target',
})

// get all connections where this node is the source (outgoing connections)
const sourceConnections = useNodeConnections({
  handleType: 'source',
})

const handleConnections = useNodeConnections({
  handleId: 'handle-1', // you can explicitly pass a handle id if you want to get connections of a specific handle
})

const connections = useNodeConnections({
  nodeId: '1', // you can explicitly pass a node id, otherwise it's used from the `NodeId  injection
  handleType: 'target',
  onConnect: (connections: HandleConnection[]) => {
    // do something with the connections
  },
  onDisconnect: (connections: HandleConnection[]) => {
    // do something with the connections
  },
})
```

----------------------------------------

TITLE: Enforcing State Instance with useVueFlow (TypeScript)
DESCRIPTION: Shows how to provide a unique `id` when calling `useVueFlow` to ensure the composable interacts with a specific instance of the Vue Flow store, useful for managing multiple flows on the same page.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/composables.md#_snippet_1

LANGUAGE: typescript
CODE:
```
import { useVueFlow } from '@vue-flow/core'

const { onInit } = useVueFlow({ id: 'my-flow-instance' })

onInit((instance) => {
  // `instance` is the same type as the return of `useVueFlow` (VueFlowStore)
})
```

----------------------------------------

TITLE: Enabling automatic edge connection (Vue)
DESCRIPTION: This simple snippet shows how to enable the `auto-connect` feature globally by setting the `auto-connect` prop to `true` on the `VueFlow` component template. This allows edges to be automatically created when handles are connected without requiring an explicit `onConnect` listener.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/vue-flow/config.md#_snippet_13

LANGUAGE: Vue
CODE:
```
<template>
  <VueFlow :auto-connect="true" />
</template>
```

----------------------------------------

TITLE: Implementing Scrollable Nodes in Vue Flow Vue
DESCRIPTION: Shows a Vue component structure suitable for use as a custom node in Vue Flow, designed to allow internal scrolling. It applies the default `nowheel` class (specified by `noWheelClassName` on the Vue Flow instance) to the scrollable container (`ul`) to prevent wheel events from triggering pan/zoom on the node.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/node.md#_snippet_21

LANGUAGE: Vue
CODE:
```
<script setup>
import { ref } from 'vue'

const listItems = ref(Array.from({ length: 100 }, (_, i) => i))
</script>

<template>
  <div class="custom-node-container">
    <ul class="nowheel">
      <li v-for="item in listItems" :key="item">Item {{ item }}</li>
    </ul>
  </div>
</template>
```

----------------------------------------

TITLE: Defining Default Nodes (Vue Flow/TypeScript)
DESCRIPTION: This snippet demonstrates how to define a default node type in Vue Flow using a ref. Default nodes typically have both target and source handles and serve as junction points. You can specify the positions of these handles.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/node.md#_snippet_10

LANGUAGE: typescript
CODE:
```
import { ref } from 'vue'
import { Position } from '@vue-flow/core'

const nodes = ref([
  {
    id: '1',
    type: 'default', // You can omit this as it's the fallback type
    targetPosition: Position.Top, // or Bottom, Left, Right,
    sourcePosition: Position.Bottom, // or Top, Left, Right,
    data: { label: 'Default Node' },
  }
])
```

----------------------------------------

TITLE: Defining Nested Vue Flow Nodes (TypeScript)
DESCRIPTION: Demonstrates how to configure nested nodes within Vue Flow by setting the parentNode property on child nodes. This example shows two nodes (2 and 3) parented by node 1 to prevent the NODE_MISSING_PARENT error.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/troubleshooting.md#_snippet_2

LANGUAGE: ts
CODE:
```
// Here's an example of a valid nested node configuration
const nodes = ref([
  { id: '1', type: 'input', label: 'Node 1', position: { x: 250, y: 5 } },
  { id: '2', label: 'Node 2', position: { x: 100, y: 100 }, parentNode: '1' },
  { id: '3', type: 'output', label: 'Node 3', position: { x: 400, y: 200 }, parentNode: '1' },
])
```

----------------------------------------

TITLE: Installing Vue Flow Controls Component (Bash)
DESCRIPTION: Provides the command-line instructions to install the official controls package for Vue Flow using either yarn or npm. This package is a prerequisite for using the Controls component.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/components/controls.md#_snippet_0

LANGUAGE: bash
CODE:
```
yarn add @vue-flow/controls
```

LANGUAGE: bash
CODE:
```
npm install @vue-flow/controls
```

----------------------------------------

TITLE: Creating Custom Vue Flow Node with Non-Draggable Input
DESCRIPTION: Defines a Vue 3 component using the Composition API (`<script setup>`) and a template. The template contains an input field with the class `nodrag`, which prevents the parent node from being dragged when the input is interacted with.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/node.md#_snippet_23

LANGUAGE: Vue
CODE:
```
<script setup>
import { ref } from 'vue'

const inputValue = ref('')
</script>

<template>
  <div class="custom-node-container">
    <input class="nodrag" v-model="inputValue" />
  </div>
</template>
```

----------------------------------------

TITLE: Defining Input Nodes (Vue Flow/TypeScript)
DESCRIPTION: This snippet shows how to define an input node in Vue Flow using a ref. Input nodes typically represent starting points in a flow and have a single source handle, usually positioned at the bottom by default.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/node.md#_snippet_11

LANGUAGE: typescript
CODE:
```
import { ref } from 'vue'
import { Position } from '@vue-flow/core'

const nodes = ref([
  {
    id: '1',
    type: 'input',
    sourcePosition: Position.Bottom, // or Top, Left, Right,
    data: { label: 'Input Node' },
  }
])
```

----------------------------------------

TITLE: Customizing Connection Line in Vue Flow (Vue)
DESCRIPTION: This snippet demonstrates how to use the '#connection-line' slot in Vue Flow to render a custom component for the connection line when a connection is being established. It binds the standard 'connectionLineProps' to the custom component.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/vue-flow/slots.md#_snippet_0

LANGUAGE: vue
CODE:
```
<template>
  <VueFlow>
    <template #connection-line="connectionLineProps">
      <CustomConnectionLine v-bind="connectionLineProps" />
    </template>
  </VueFlow>
</template>
```

----------------------------------------

TITLE: Installing Node Resizer | Bash
DESCRIPTION: Commands to install the @vue-flow/node-resizer package using either yarn or npm. This package is a prerequisite for adding node resizing functionality to your Vue Flow application. Requires node.js and a package manager (yarn or npm) to execute.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/packages/node-resizer/README.md#_snippet_0

LANGUAGE: bash
CODE:
```
# install\n$ yarn add @vue-flow/node-resizer\n\n# or\n$ npm i --save @vue-flow/node-resizer
```

----------------------------------------

TITLE: Adjusting Handle Positions Using CSS (Vue)
DESCRIPTION: Illustrates how to wrap handles in a relatively positioned container (`<div style="position: relative">`) to control their absolute positioning relative to that container, rather than the node's root. This allows for more flexible handle placement.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/handle.md#_snippet_1

LANGUAGE: vue
CODE:
```
<template>
  <div>
    <span>{{ data.label }}</span>

    <div style="position: relative; padding: 10px">
      <Handle type="source" :position="Position.Right" />


      <Handle type="target" :position="Position.Left" />
    </div>
  </div>
</template>
```

----------------------------------------

TITLE: Installing Vue Flow Background Component (bash)
DESCRIPTION: Provides commands to add the @vue-flow/background package to your project using either yarn or npm package managers. This component requires a Node.js environment with one of these package managers installed.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/components/background.md#_snippet_0

LANGUAGE: bash
CODE:
```
yarn add @vue-flow/background
```

LANGUAGE: bash
CODE:
```
npm install @vue-flow/background
```

----------------------------------------

TITLE: Positioning Multiple Handles on the Same Side (Vue)
DESCRIPTION: Provides an example of using inline CSS styles (`top`, `bottom`) on individual `<Handle>` components to position multiple handles on the same side of a node relative to their container, preventing them from overlapping.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/handle.md#_snippet_4

LANGUAGE: vue
CODE:
```
<Handle id="source-a" type="source" :position="Position.Right" style="top: 10px" />
<Handle id="source-b" type="source" :position="Position.Right" style="bottom: 10px; top: auto;" />
```

----------------------------------------

TITLE: Transforming Pixel Coordinates to Flow Coordinates
DESCRIPTION: Shows how to use the `project` function on the Vue Flow instance to convert pixel coordinates from the screen/viewport space into the internal coordinate system of the flow pane. Useful for positioning elements dragged onto the flow.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/utils/instance.md#_snippet_2

LANGUAGE: ts
CODE:
```
vueFlowInstance.project({ x: 100, y: 100 })
```

----------------------------------------

TITLE: Basic Usage of Background Component (Vue)
DESCRIPTION: Demonstrates how to import and use the Background component within a Vue single-file component. The component is imported from '@vue-flow/background' and placed as a child element inside the <VueFlow> component. Requires '@vue-flow/core' and '@vue-flow/background' packages.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/components/background.md#_snippet_1

LANGUAGE: vue
CODE:
```
<script setup>
import { VueFlow } from '@vue-flow/core'
import { Background } from '@vue-flow/background'
</script>

<template>
  <VueFlow>
    <Background />
  </VueFlow>
</template>
```

----------------------------------------

TITLE: Calculating Straight Edge Path (Vue/TypeScript)
DESCRIPTION: Demonstrates using the `getStraightPath` utility function from Vue Flow to calculate a straight line path string between source and target handles. It takes source and target coordinates. The computed property `edgePathParams` stores the resulting path string, which is applied to a `BaseEdge` component. Requires `BaseEdge`, `getStraightPath`, and `EdgeProps` from `@vue-flow/core`.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/utils/edge.md#_snippet_4

LANGUAGE: vue
CODE:
```
<script lang="ts" setup>
import { computed } from "vue"
import { BaseEdge, getStraightPath, EdgeProps } from '@vue-flow/core'

const props = defineProps<EdgeProps>()

const edgePathParams = computed(() => getStraightPath(props))
</script>

<template>
  <BaseEdge :path="edgePathParams[0]" />
</template>
```

----------------------------------------

TITLE: Asserting Node Data Type with useNodesData in TypeScript
DESCRIPTION: This advanced snippet shows how to use a typeguard function with `useNodesData` when retrieving node data for an array of IDs. The typeguard helps TypeScript narrow down the type of the node data, ensuring type safety when working with custom data structures.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/packages/core/CHANGELOG.md#_snippet_8

LANGUAGE: ts
CODE:
```
import type { Node } from "@vue-flow/core";

interface Data {
  foo: string;
  bar: string;
}

type MyNode = Node<CustomNodeData>;

const nodeId = "1";

const data = useNodesData([nodeId], (node): node is MyNode => {
  return "foo" in node.data && "bar" in node.data;
});

console.log(data.value); // '[{ /* foo: string; bar: string */ }]'
```

----------------------------------------

TITLE: Listening to Handle Connections with useHandleConnections in TypeScript
DESCRIPTION: This snippet illustrates how to use the `useHandleConnections` composable to get reactive access to connections associated with a specific handle. It allows specifying the handle type, ID, and node ID, and provides callbacks for when connections are added or removed.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/packages/core/CHANGELOG.md#_snippet_4

LANGUAGE: ts
CODE:
```
const connections = useHandleConnections({
  // type of the handle you are looking for - accepts a `MaybeRefOrGetter<string>`
  type: "source",

  // the id of the handle you are looking for - accepts a `MaybeRefOrGetter<string | undefined> | undefined` [OPTIONAL]
  id: "a",

  // if not provided, the node id from the NodeIdContext is used - accepts a `MaybeRefOrGetter<string | undefined> | undefined`
  nodeId: "1",

  // a cb that is called when a new connection is added
  onConnect: (params) => {
    console.log("onConnect", params);
  },

  // a cb that is called when a connection is removed
  onDisconnect: (params) => {
    console.log("onDisconnect", params);
  },
});

watch(
  connections,
  (next) => {
    console.log("connections", next);
  },
  { immediate: true }
);
```

----------------------------------------

TITLE: Using MiniMapNode in Vue Flow MiniMap (Vue Template)
DESCRIPTION: This code snippet demonstrates how to integrate the custom MiniMapNode component within the MiniMap component in a Vue Flow application. It utilizes a template slot (#node-input) provided by MiniMap to render the MiniMapNode component for nodes of type 'input', binding the necessary node properties passed by the slot to the custom component.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/components/minimap-node.md#_snippet_0

LANGUAGE: vue
CODE:
```
<template>
  <VueFlow>
    <MiniMap>
      <template #node-input="props">
        <MiniMapNode v-bind="props" />
      </template>
    </MiniMap>
  </VueFlow>
</template>
```

----------------------------------------

TITLE: Getting Node ID with useNodeId in TypeScript
DESCRIPTION: This snippet demonstrates how to use the new `useNodeId` composable in Vue Flow to retrieve the ID of the current node within a node component context. It is typically used inside a custom node component to access its own ID.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/packages/core/CHANGELOG.md#_snippet_2

LANGUAGE: ts
CODE:
```
const nodeId = useNodeId();

console.log("nodeId", nodeId); // '1'
```

----------------------------------------

TITLE: Defining node extent constraint (Vue)
DESCRIPTION: This snippet demonstrates how to apply a coordinate extent constraint to a specific node element. By setting the `extent` property on the node object, its movement will be limited to the defined rectangular area, overriding any global `node-extent` setting.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/vue-flow/config.md#_snippet_11

LANGUAGE: Vue
CODE:
```
<script setup>
import { ref } from 'vue'
import { VueFlow } from '@vue-flow/core'

const nodes = ref([
  { id: '1', position: { x: 250, y: 5 } },
  {
    id: '2',
    extent: [[-100, -100], [100, 100]],
    position: { x: 100, y: 100 }
  },
])
</script>

<template>
  <VueFlow :nodes="nodes" />
</template>
```

----------------------------------------

TITLE: Enabling Interactive MiniMap in Vue (Vue)
DESCRIPTION: Shows how to make the MiniMap component interactive, allowing users to pan on drag and zoom on scroll directly on the minimap. This is achieved by adding the `pannable` and `zoomable` boolean props to the `<MiniMap>` component.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/components/minimap.md#_snippet_2

LANGUAGE: vue
CODE:
```
<template>
  <VueFlow>
    <MiniMap pannable zoomable />
  </VueFlow>
</template>
```

----------------------------------------

TITLE: Configuring Autopan Options (TypeScript/JavaScript)
DESCRIPTION: Demonstrates how to configure the autopan behavior for node dragging and connection line drawing using the `useVueFlow` composable or directly via options. This requires the `vue-flow` library as a dependency. The options `autoPanOnNodeDrag` and `autoPanOnConnect` are boolean flags to enable/disable the respective autopan features.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/packages/core/CHANGELOG.md#_snippet_9

LANGUAGE: typescript
CODE:
```
useVueFlow({
  autoPanOnNodeDrag: true,
  autoPanOnConnect: true,
});
```

----------------------------------------

TITLE: Checking if Element is Node (Vue)
DESCRIPTION: This Vue component demonstrates how to use the `isNode` utility function from `@vue-flow/core` to identify if an element in the `elements` array is a node. It includes a button that toggles a CSS class ('light'/'dark') on all elements identified as nodes. Requires `@vue-flow/core` and Vue's `ref`. Inputs are the initial `elements` array; the output is the modified `elements` array with updated classes on nodes.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/utils/graph.md#_snippet_1

LANGUAGE: javascript
CODE:
```
import { VueFlow, isNode } from '@vue-flow/core'

const elements = ref([
  { id: '1', label: 'Node 1', position: { x: 250, y: 5 }, class: 'light' },
  { id: '2', label: 'Node 2', position: { x: 100, y: 100 }, class: 'light' },

  { id: 'e1-2', source: '1', target: '2' },
])

const toggleClass = () => {
  elements.value.forEach((el) => {
    if (isNode(el)) {
      el.class = el.class === 'light' ? 'dark' : 'light'
    }
  })
}
```

LANGUAGE: vue
CODE:
```
<template>
  <VueFlow v-model="elements">
    <button @click="toggleClass">Toggle classes</button>
  </VueFlow>
</template>
```

----------------------------------------

TITLE: Using getBezierPath for Path and Center (After Change) - JavaScript
DESCRIPTION: This snippet illustrates the updated API usage in Vue.js after a breaking change. The `getBezierPath` function now returns an array containing the path string, along with the centerX and centerY coordinates, consolidating the required information.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/packages/core/CHANGELOG.md#_snippet_15

LANGUAGE: js
CODE:
```
import { getBezierPath } from "@vue-flow/core";

// returns the path string and the center positions
const [path, centerX, centerY] = computed(() => getBezierPath(pathParams));
```

----------------------------------------

TITLE: Configuring global edge updatable state (Vue)
DESCRIPTION: This example shows how to globally disable the ability to update edges using the `edges-updatable` prop. It also demonstrates overriding this setting for a specific edge, allowing only that edge to be updatable by setting its individual `updatable` property.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/vue-flow/config.md#_snippet_12

LANGUAGE: Vue
CODE:
```
<script setup>
import { ref } from 'vue'
import { VueFlow } from '@vue-flow/core'

const edgesUpdatable = ref(false)
  
const nodes = ref([
  { id: '1', position: { x: 250, y: 5 } },
  { id: '2', position: { x: 100, y: 100 } },
])
  
const edges = ref([
  { id: 'e1->2', source: '1', target: '2' },
  { 
    id: 'e1->3',
    // Overwrites global edges-updatable config
    updatable: true, 
    source: '1', target: '3', 
  },
])
</script>
<template>
  <VueFlow :nodes="nodes" :edges="edges" :edges-updatable="edgesUpdatable" />
</template>
```

----------------------------------------

TITLE: Getting Node Data for Multiple IDs with useNodesData in TypeScript
DESCRIPTION: This snippet demonstrates using the `useNodesData` composable with an array of node IDs. It returns a ref containing an array with the `data` property for each specified node.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/packages/core/CHANGELOG.md#_snippet_7

LANGUAGE: ts
CODE:
```
const nodeIds = ["1", "2", "3"];

const data = useNodesData(nodeIds);

console.log(data.value); // '[{ /* ... */ }]'
```

----------------------------------------

TITLE: Enabling MiniMap Zoom and Pan in Vue Flow
DESCRIPTION: Shows how to make the MiniMap component interactive, allowing users to zoom and pan the main flow view by interacting with the minimap. This is achieved by setting the boolean 'zoomable' and 'pannable' props to 'true' on the MiniMap component within the Vue Flow template.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/packages/core/CHANGELOG.md#_snippet_12

LANGUAGE: vue
CODE:
```
<template>
  <VueFlow v-model="elements">
    <MiniMap :zoomable="true" :pannable="true" />
  </VueFlow>
</template>
```

----------------------------------------

TITLE: Updating Nodes Draggable State with useVueFlow (Vue)
DESCRIPTION: This snippet illustrates updating the `nodesDraggable` configuration state directly using the object returned by the `useVueFlow` composable. The `toggleNodesDraggable` function directly modifies the reactive `nodesDraggable` property obtained from the composable's state.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/vue-flow/config.md#_snippet_2

LANGUAGE: vue
CODE:
```
<script setup>
const { nodesDraggable } = useVueFlow()

const toggleNodesDraggable = () => {
  nodesDraggable.value = !nodesDraggable.value
}
</script>
```

----------------------------------------

TITLE: Styling Custom Vue Flow Node
DESCRIPTION: Provides CSS styling for the custom node type `input-field`. It applies basic background, rounding, and padding styles to the node wrapper element using utility classes facilitated by `@apply`.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/node.md#_snippet_25

LANGUAGE: CSS
CODE:
```
<style>
.vue-flow__node-input-field {
  @apply bg-primary rounded p-4;
}
</style>
```

----------------------------------------

TITLE: Applying Inline Styles/Classes | Vue Flow | Vue
DESCRIPTION: Demonstrates how to apply CSS classes and inline styles directly to the `<VueFlow>` component instance within a Vue template. The `class` attribute accepts a string, and the `:style` binding accepts a JavaScript object mapping CSS property names to values.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/theming.md#_snippet_3

LANGUAGE: vue
CODE:
```
<VueFlow
  :nodes="nodes"
  :edges="edges"
  class="my-diagram-class"  
  :style="{ background: 'red' }"
/>
```

----------------------------------------

TITLE: Retrieving Typed Node Data with useNodesData (TypeScript)
DESCRIPTION: Demonstrates passing a type guard function as the second argument to `useNodesData` to narrow down the type of the returned node data array, enabling type-safe access to node data properties based on custom types.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/composables.md#_snippet_5

LANGUAGE: typescript
CODE:
```
import { useNodesData, useHandleConnections, type Node } from '@vue-flow/core'

type MyNode = Node<{ foo: string }>

const connections = useHandleConnections({
  type: 'target',
})

const data = useNodesData(() => connections.value.map((connection) => connection.source), (node): node is MyNode => node.type === 'foo')

console.log(data.value) // [{ /* foo: string *//*]
```

----------------------------------------

TITLE: Styling Scrollable Nodes in Vue Flow CSS
DESCRIPTION: Provides the necessary CSS to style a custom scrollable node type (`vue-flow__node-scrollable`) and its internal container. It sets the node dimensions, adds padding, and enables internal scrolling using `overflow: auto`, while limiting the height of the content list.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/node.md#_snippet_22

LANGUAGE: CSS
CODE:
```
.vue-flow__node-scrollable {
  @apply bg-accent rounded px-2;
  overflow: auto;
  width: 150px;
}

.custom-node-container ul {
  max-height: 75px;
}
```

----------------------------------------

TITLE: Getting Node Data for Single ID with useNodesData in TypeScript
DESCRIPTION: This snippet shows the basic usage of the `useNodesData` composable to retrieve the reactive `data` property for a single node specified by its ID. It returns a ref containing an array with the data of the specified node.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/packages/core/CHANGELOG.md#_snippet_6

LANGUAGE: ts
CODE:
```
const nodeId = "1";

const data = useNodesData(nodeId);

console.log(data.value); // '[{ /* ... */ }]'
```

----------------------------------------

TITLE: Installing Vue Flow Core with pnpm
DESCRIPTION: Installs the `@vue-flow/core` package using the pnpm package manager. This provides an alternative to npm for managing project dependencies efficiently. Requires Node.js and pnpm to be installed.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/getting-started.md#_snippet_1

LANGUAGE: Shell
CODE:
```
pnpm add @vue-flow/core
```

----------------------------------------

TITLE: Hiding Handles Using CSS Opacity (Vue)
DESCRIPTION: Shows the correct way to hide a handle visually by applying `opacity: 0` via the `style` prop. This keeps the handle in the DOM for layout calculations while making it invisible, which is crucial for maintaining edge calculations.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/handle.md#_snippet_5

LANGUAGE: vue
CODE:
```
<Handle type="source" :position="Position.Right" style="opacity: 0" />
```

----------------------------------------

TITLE: Getting Current Node ID with useNodeId (TypeScript)
DESCRIPTION: Shows how to use the `useNodeId` composable to retrieve the ID of the node within which the composable is called, intended for use within custom node components.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/composables.md#_snippet_6

LANGUAGE: typescript
CODE:
```
import { useNodeId } from '@vue-flow/core'

const nodeId = useNodeId()

console.log(nodeId.value) // '1'
```

----------------------------------------

TITLE: Installing Vue Flow Node Resizer (Bash)
DESCRIPTION: Provides command-line instructions using `yarn` and `npm` to add the Node Resizer package as a dependency to a project. This package is required for using the resizer component.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/components/node-resizer.md#_snippet_0

LANGUAGE: bash
CODE:
```
yarn add @vue-flow/node-resizer

# or
npm install @vue-flow/node-resizer
```

----------------------------------------

TITLE: Adding Multiple Handles with Unique IDs (Vue)
DESCRIPTION: Shows how to add multiple handles of the same type (source or target) to a node. It emphasizes the requirement for each handle of the same type to have a unique `id` prop for proper identification when creating edges.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/handle.md#_snippet_2

LANGUAGE: vue
CODE:
```
<!-- each of these handles needs a unique id since we're using two `source` type handles -->
<Handle id="source-a" type="source" :position="Position.Right" />
<Handle id="source-b" type="source" :position="Position.Right" />

<!-- each of these handles needs a unique id since we're using two `target` type handles -->
<Handle id="target-a" type="target" :position="Position.Left" />
<Handle id="target-b" type="target" :position="Position.Left" />
```

----------------------------------------

TITLE: Installing Pathfinding Edge Package (Bash)
DESCRIPTION: Provides command-line instructions for adding the `@vue-flow/pathfinding-edge` package to a project using either Yarn or npm. This package is a necessary dependency for using the custom pathfinding edge component in a Vue Flow diagram.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/packages/pathfinding-edge/README.md#_snippet_0

LANGUAGE: bash
CODE:
```
# install
$ yarn add @vue-flow/pathfinding-edge
```

LANGUAGE: bash
CODE:
```
# or
$ npm i --save @vue-flow/pathfinding-edge
```

----------------------------------------

TITLE: Installing Vue Flow Minimap Package (Bash)
DESCRIPTION: Provides command-line instructions for adding the `@vue-flow/minimap` package to your project using either yarn or npm. These steps are required before you can import and use the MiniMap component within your Vue application.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/packages/minimap/README.md#_snippet_0

LANGUAGE: bash
CODE:
```
# install
$ yarn add @vue-flow/minimap

# or
$ npm i --save @vue-flow/minimap
```

----------------------------------------

TITLE: Installing Vue Flow Controls Package (Bash)
DESCRIPTION: Provides commands to install the Vue Flow Controls package using either Yarn or npm package managers. This package is a prerequisite for using the Controls component in your Vue Flow application.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/packages/controls/README.md#_snippet_0

LANGUAGE: bash
CODE:
```
yarn add @vue-flow/controls
```

LANGUAGE: bash
CODE:
```
npm i --save @vue-flow/controls
```

----------------------------------------

TITLE: Installing Vue Flow Node Toolbar Package (Bash)
DESCRIPTION: Provides command-line instructions for adding the `@vue-flow/node-toolbar` package to a project using either the Yarn or npm package manager. This package is a necessary dependency for using the Node Toolbar component in Vue Flow.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/packages/node-toolbar/README.md#_snippet_0

LANGUAGE: bash
CODE:
```
# install
$ yarn add @vue-flow/node-toolbar

# or
$ npm i --save @vue-flow/node-toolbar
```

----------------------------------------

TITLE: Importing Vue Flow Components (After Scope Change) - JavaScript
DESCRIPTION: This snippet demonstrates the new import paths for Vue Flow components following the package scope change. `VueFlow` is now imported from `@vue-flow/core`, while `Background`, `MiniMap`, and `Controls` are imported from `@vue-flow/additional-components`.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/packages/core/CHANGELOG.md#_snippet_17

LANGUAGE: js
CODE:
```
import { VueFlow } from "@vue-flow/core";
import {
  Background,
  MiniMap,
  Controls,
} from "@vue-flow/additional-components";
```

----------------------------------------

TITLE: Basic MiniMap Usage in Vue Flow (Vue)
DESCRIPTION: Demonstrates the basic setup for using the MiniMap component by importing it along with `VueFlow` and placing it as a child within the `<VueFlow>` template. It also shows the required CSS style import.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/components/minimap.md#_snippet_1

LANGUAGE: vue
CODE:
```
<script setup>
import { VueFlow } from '@vue-flow/core'
import { MiniMap } from '@vue-flow/minimap'

// import default minimap styles
import '@vue-flow/minimap/dist/style.css'
</script>

<template>
  <VueFlow>
    <MiniMap />
  </VueFlow>
</template>
```

----------------------------------------

TITLE: Importing Vue Flow Styles - CSS
DESCRIPTION: Shows how to import the necessary CSS styles for the Vue Flow component. It includes the core styles (`style.css`) which are required for basic functionality and layout, and the optional default theme styles (`theme-default.css`) for default visual appearance.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/README.md#_snippet_2

LANGUAGE: css
CODE:
```
/* import the required styles */
@import "@vue-flow/core/dist/style.css";

/* import the default theme (optional) */
@import "@vue-flow/core/dist/theme-default.css";
```

----------------------------------------

TITLE: Installing Vue Flow Background Component (Bash)
DESCRIPTION: Instructions for adding the @vue-flow/background package to your project using either Yarn or npm. This component is a dependency for rendering a background within a Vue Flow canvas.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/packages/background/README.md#_snippet_0

LANGUAGE: bash
CODE:
```
# install
$ yarn add @vue-flow/background

# or
$ npm i --save @vue-flow/background
```

----------------------------------------

TITLE: Logging Vue Flow Events Javascript
DESCRIPTION: This simple JavaScript function demonstrates how to log events emitted by the Vue Flow component. It takes the event `name` and associated `data` as arguments and prints them to the console, useful for debugging or monitoring graph interactions.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/node.md#_snippet_2

LANGUAGE: Javascript
CODE:
```
function logEvent(name, data) {
  console.log(name, data)
}
```

----------------------------------------

TITLE: Updating Nodes Directly Vue Flow JavaScript
DESCRIPTION: This snippet demonstrates updating the `nodes` array directly in Vue.js. This method of changing the nodes array does not trigger a Vue Flow 'change' event because Vue Flow does not track direct array mutations; it only recognizes changes initiated by user interaction or the Vue Flow API.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/controlled-flow.md#_snippet_0

LANGUAGE: javascript
CODE:
```
import { ref } from 'vue';

const nodes = ref([
  {
    id: '1',
    position: { x: 0, y: 0 },
    data: { label: 'Node 1' },
  },
]);

// this function *will not* emit a change
function removeNode() {
  nodes.value = nodes.value.filter((node) => node.id !== '1');
}
```

----------------------------------------

TITLE: Adding Edge on Connect (Vue)
DESCRIPTION: This Vue component demonstrates the *deprecated* `addEdge` utility function from `@vue-flow/core`. It sets up a `VueFlow` instance with initial elements and an `@connect` event handler. The `onConnect` function receives connection parameters and uses `addEdge` to append the new edge to the `elements` array. Requires Vue's `ref` and `@vue-flow/core`. Inputs are the initial `elements` array and connection parameters from the `@connect` event; the output is the `elements` array updated with the new edge.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/utils/graph.md#_snippet_2

LANGUAGE: javascript
CODE:
```
import { ref } from 'vue'
import { VueFlow, addEdge } from '@vue-flow/core'

const elements = ref([
  { id: '1', position: { x: 250, y: 5 } },
  { id: '2', position: { x: 100, y: 100 } },

  { id: 'e1-2', source: '1', target: '2' },
])

const onConnect = (params) => {
  addEdge(params, elements.value)
}
```

LANGUAGE: vue
CODE:
```
<template>
  <VueFlow v-model="elements" @connect="onConnect" />
</template>
```

----------------------------------------

TITLE: Build Production Application Bash
DESCRIPTION: This command compiles and bundles the Nuxt 3 application for production deployment. It optimizes the code for performance and size.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/examples/nuxt3/README.md#_snippet_2

LANGUAGE: bash
CODE:
```
npm run build
```

----------------------------------------

TITLE: Installing Vue Flow MiniMap (Bash)
DESCRIPTION: Commands to install the `@vue-flow/minimap` package into your project using either the Yarn or npm package manager. This is a prerequisite for using the MiniMap component.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/components/minimap.md#_snippet_0

LANGUAGE: bash
CODE:
```
yarn add @vue-flow/minimap
  
# or
npm install @vue-flow/minimap
```

----------------------------------------

TITLE: Providing Initial Elements with v-model in VueFlow (Vue)
DESCRIPTION: This snippet shows the deprecated method of providing initial nodes and edges together in a single array bound to the `v-model` directive. The array contains both node objects (with position) and edge objects (with source/target IDs), representing the full graph state.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/vue-flow/config.md#_snippet_5

LANGUAGE: vue
CODE:
```
<script setup>
import { ref } from 'vue'  
import { VueFlow } from '@vue-flow/core'

const elements = ref([
  { id: '1', type: 'input', label: 'Node 1', position: { x: 250, y: 5 } },
  { id: '2', label: 'Node 2', position: { x: 100, y: 100 }, },
  { id: '3', label: 'Node 3', position: { x: 400, y: 100 } },
  { id: '4', type: 'output', label: 'Node 4', position: { x: 400, y: 200 } },
  { id: 'e1-3', source: '1', target: '3' },
  { id: 'e1-2', source: '1', target: '2', animated: true },
])
</script>

<template>
  <VueFlow v-model="elements" />
</template>
```

----------------------------------------

TITLE: Updating Edge on Edge Update (Vue)
DESCRIPTION: This Vue component demonstrates the *deprecated* `updateEdge` utility function from `@vue-flow/core`. It sets up a `VueFlow` instance with initial elements and an `@edge-update` event handler. The `onEdgeUpdate` function receives the old edge and new connection parameters, then uses `updateEdge` to replace the old edge in the `elements` array with the new one. Requires `@vue-flow/core`. Inputs are the initial `elements` array, the old edge, and new connection parameters from the `@edge-update` event; the output is the `elements` array updated with the modified edge.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/utils/graph.md#_snippet_3

LANGUAGE: javascript
CODE:
```
import { VueFlow, updateEdge } from '@vue-flow/core'

const elements = ref([
  { id: '1', label: 'Node 1', position: { x: 250, y: 5 } },
  { id: '2', label: 'Node 2', position: { x: 100, y: 100 } },

  { id: 'e1-2', source: '1', target: '2' },
])

const onEdgeUpdate = ({ edge, connection }) => {
  elements.value = updateEdge(edge, connection, elements.value)
}
```

LANGUAGE: vue
CODE:
```
<template>
  <VueFlow v-model="elements" @edge-update="onEdgeUpdate" />
</template>
```

----------------------------------------

TITLE: Importing Screenshot Example Component - Vue
DESCRIPTION: This snippet imports the `ScreenshotExample` Vue component from a relative path within the project structure. Using `<script setup>`, the imported component is automatically made available for direct use in the template section of this Vue Single File Component.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/examples/screenshot.md#_snippet_0

LANGUAGE: Vue
CODE:
```
<script setup>\nimport ScreenshotExample from '../../examples/screenshot/ScreenshotExample.vue';\n</script>
```

----------------------------------------

TITLE: Using getBezierPath and getEdgeCenter (Before Change) - JavaScript
DESCRIPTION: This snippet demonstrates the previous API usage in Vue.js before a breaking change. It shows how `getBezierPath` was used only for the path string and required a separate call to `getEdgeCenter` to obtain the edge's center coordinates.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/packages/core/CHANGELOG.md#_snippet_14

LANGUAGE: js
CODE:
```
import { getBezierPath, getEdgeCenter } from "@braks/vue-flow";

// used to return the path string only
const edgePath = computed(() => getBezierPath(pathParams));

// was necessary to get the centerX, centerY of an edge
const centered = computed(() => getEdgeCenter(centerParams));
```

----------------------------------------

TITLE: Running Vue Flow Development Commands - Bash
DESCRIPTION: Provides command-line instructions for setting up and running the development environment for Vue Flow. This includes installing pnpm globally, starting the example applications locally for development, and building all packages in the monorepo.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/README.md#_snippet_3

LANGUAGE: bash
CODE:
```
# install pnpm if you haven't already
$ npm i -g pnpm

# start examples
$ pnpm dev

# build all packages
$ pnpm build
```

----------------------------------------

TITLE: Importing Icons in Vue Setup Script
DESCRIPTION: This snippet imports various icon components from specific paths, likely using an icon library integration like unplugin-icons. These imported components are then used within the Vue template to display icons next to text for enhanced visual presentation of features and links. This requires the appropriate icon library and setup configuration in the project.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/index.md#_snippet_0

LANGUAGE: JavaScript
CODE:
```
import PowerPlug from '~icons/mdi/power-plug';
import Flash from '~icons/mdi/flash';
import Lifebuoy from '~icons/mdi/lifebuoy';
import Puzzle from '~icons/mdi/puzzle';
import Speedometer from '~icons/mdi/speedometer';
import Cogs from '~icons/mdi/cogs';
import CubeOutline from '~icons/mdi/cube-outline';
import Image from '~icons/mdi/image';
import MapMarkerPath from '~icons/mdi/map-marker-path';
import Gamepad from '~icons/mdi/gamepad';
import Wrench from '~icons/mdi/wrench';
import ArrowExpand from '~icons/mdi/arrow-expand';
import LockCheck from '~icons/mdi/lock-check';
import VueJs from '~icons/mdi/vuejs';
import LogosJavascript from '~icons/logos/javascript';
import LogosTypescript from '~icons/logos/typescript-icon';
```

----------------------------------------

TITLE: Preview Production Build Bash
DESCRIPTION: After building the application for production, use this command to locally preview the generated output. This is useful for testing the production build before deploying.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/examples/nuxt3/README.md#_snippet_3

LANGUAGE: bash
CODE:
```
npm run preview
```

----------------------------------------

TITLE: Building for Production (bash)
DESCRIPTION: Builds the Quasar application for production deployment. This command compiles and optimizes the project assets. Requires dependencies to be installed and the Quasar CLI.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/examples/quasar/README.md#_snippet_3

LANGUAGE: bash
CODE:
```
quasar build
```

----------------------------------------

TITLE: Linting Project Files (bash)
DESCRIPTION: Runs linters to check code style and potential errors in the project files. Requires dependencies to be installed and linting scripts configured in package.json.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/examples/quasar/README.md#_snippet_2

LANGUAGE: bash
CODE:
```
yarn lint
```

LANGUAGE: bash
CODE:
```
npm run lint
```

----------------------------------------

TITLE: Starting Development Server (bash)
DESCRIPTION: Starts the Quasar development server with hot-code reloading and error reporting for development purposes. Requires dependencies to be installed and the Quasar CLI.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/examples/quasar/README.md#_snippet_1

LANGUAGE: bash
CODE:
```
quasar dev
```

----------------------------------------

TITLE: Start Development Server Bash
DESCRIPTION: Use this command to start the local development server for the Nuxt 3 application. The server typically runs at http://localhost:3000, allowing for live development.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/examples/nuxt3/README.md#_snippet_1

LANGUAGE: bash
CODE:
```
npm run dev
```

----------------------------------------

TITLE: Accessing Previous useNodesData Result - TypeScript
DESCRIPTION: This snippet demonstrates the previous method (prior to v1.33.0) for accessing the data returned by the `useNodesData` composable in vue-flow. The composable directly returned an array of data objects, which could be iterated over.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/packages/core/CHANGELOG.md#_snippet_0

LANGUAGE: typescript
CODE:
```
const nodesData = useNodesData(nodeIds);

// Previously
nodesData.forEach((data) => {
  // ...
});
```

----------------------------------------

TITLE: Importing Vue Flow Components (Before Scope Change) - JavaScript
DESCRIPTION: This snippet shows the previous method for importing core Vue Flow components such as `VueFlow`, `Background`, `MiniMap`, and `Controls` from the old package scope `@braks/vue-flow` before the change to `@vue-flow`.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/packages/core/CHANGELOG.md#_snippet_16

LANGUAGE: js
CODE:
```
import { VueFlow, Background, MiniMap, Controls } from "@braks/vue-flow";
```

----------------------------------------

TITLE: Importing Vue Flow Components - Old Scope (@braks/vue-flow) - JavaScript
DESCRIPTION: This snippet illustrates how core Vue Flow components and additional components like Background, MiniMap, and Controls were imported from the single `@braks/vue-flow` package before the major version 1.0.0 update. It shows a consolidated import statement for various Vue Flow features.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/packages/pathfinding-edge/CHANGELOG.md#_snippet_2

LANGUAGE: javascript
CODE:
```
import { VueFlow, Background, MiniMap, Controls } from "@braks/vue-flow";
```

----------------------------------------

TITLE: Installing Project Dependencies (bash)
DESCRIPTION: Installs the necessary project dependencies using either Yarn or npm. Requires Node.js and either Yarn or npm installed.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/examples/quasar/README.md#_snippet_0

LANGUAGE: bash
CODE:
```
yarn
```

LANGUAGE: bash
CODE:
```
npm install
```

----------------------------------------

TITLE: Getting Edge Path and Center - Old API (@braks/vue-flow) - JavaScript
DESCRIPTION: This snippet demonstrates how edge path calculation and center position retrieval were done using the old `@braks/vue-flow` package API. It shows separate functions for getting the path string (`getBezierPath`) and the center coordinates (`getEdgeCenter`). Dependencies include `getBezierPath` and `getEdgeCenter` from `@braks/vue-flow` and potentially `computed` from Vue.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/packages/pathfinding-edge/CHANGELOG.md#_snippet_0

LANGUAGE: javascript
CODE:
```
import { getBezierPath, getEdgeCenter } from "@braks/vue-flow";

// used to return the path string only
const edgePath = computed(() => getBezierPath(pathParams));

// was necessary to get the centerX, centerY of an edge
const centered = computed(() => getEdgeCenter(centerParams));
```

----------------------------------------

TITLE: Importing Pinia Example Component in Vue
DESCRIPTION: This script block imports the PiniaExample component, which contains the actual demonstration of using Pinia with Vue Flow. The component is imported from a relative path within the project's example directory and is made available for use in the template.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/examples/pinia.md#_snippet_0

LANGUAGE: Vue
CODE:
```
import PiniaExample from '../../examples/pinia/PiniaExample.vue';
```

----------------------------------------

TITLE: Closing Vue Template Tags (HTML)
DESCRIPTION: This snippet displays the concluding tags of a Vue component template, specifically showing the end tag for an `EdgeLabelRenderer` element and the main `<template>` block. It illustrates the structural closure of the component's view definition.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/guide/getting-started.md#_snippet_10

LANGUAGE: html
CODE:
```
  </EdgeLabelRenderer>
</template>
```

----------------------------------------

TITLE: Embedding Live Demo Component in HTML
DESCRIPTION: This snippet demonstrates how to embed a live, interactive example component (`<Repl>`) within an HTML structure, typically used in documentation. It places the component inside a div with margin styling and passes an 'example' prop to specify the demo content.
SOURCE: https://github.com/bcakmakoglu/vue-flow/blob/master/docs/src/examples/nodes/index.md#_snippet_0

LANGUAGE: HTML
CODE:
```
<div class="mt-6">
  <Repl example="customNode"></Repl>
</div>
```