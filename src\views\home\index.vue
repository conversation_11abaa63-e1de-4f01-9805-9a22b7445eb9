<template>
  <div class="container">
    <!-- Statistics Cards -->
    <vxe-row class="mb-6" :gutter="24">
      <!-- Sales Today -->
      <vxe-col :span="6">
        <vxe-card class="stat-card">
          <div class="flex flex-col">
            <div class="flex justify-between items-center mb-2">
              <h3 class="text-sm font-medium text-gray-500">Sales Today</h3>
              <div class="bg-blue-100 text-blue-600 text-xs px-2 py-1 rounded">Daily</div>
            </div>
            <div class="flex justify-between items-end">
              <div>
                <p class="text-2xl font-bold text-gray-800">2,532</p>
                <div class="flex items-center mt-1">
                  <span class="text-green-500 text-xs font-medium">+26%</span>
                  <span class="text-gray-500 text-xs ml-1">since last month</span>
                </div>
              </div>
              <img src="../../assets/sales-icon.svg" alt="Sales" class="h-12 w-12" />
            </div>
          </div>
        </vxe-card>
      </vxe-col>

      <!-- Visitors -->
      <vxe-col :span="6">
        <vxe-card class="stat-card">
          <div class="flex flex-col">
            <div class="flex justify-between items-center mb-2">
              <h3 class="text-sm font-medium text-gray-500">Visitors</h3>
              <div class="bg-blue-100 text-blue-600 text-xs px-2 py-1 rounded">Daily</div>
            </div>
            <div class="flex justify-between items-end">
              <div>
                <p class="text-2xl font-bold text-gray-800">170,212</p>
                <div class="flex items-center mt-1">
                  <span class="text-green-500 text-xs font-medium">+14%</span>
                  <span class="text-gray-500 text-xs ml-1">since last month</span>
                </div>
              </div>
              <img src="../../assets/visitors-icon.svg" alt="Visitors" class="h-12 w-12" />
            </div>
          </div>
        </vxe-card>
      </vxe-col>

      <!-- Total Earnings -->
      <vxe-col :span="6">
        <vxe-card class="stat-card">
          <div class="flex flex-col">
            <div class="flex justify-between items-center mb-2">
              <h3 class="text-sm font-medium text-gray-500">Total Earnings</h3>
              <div class="bg-blue-100 text-blue-600 text-xs px-2 py-1 rounded">Monthly</div>
            </div>
            <div class="flex justify-between items-end">
              <div>
                <p class="text-2xl font-bold text-gray-800">$ 24,300</p>
                <div class="flex items-center mt-1">
                  <span class="text-red-500 text-xs font-medium">-8%</span>
                  <span class="text-gray-500 text-xs ml-1">since last month</span>
                </div>
              </div>
              <img src="../../assets/earnings-icon.svg" alt="Earnings" class="h-12 w-12" />
            </div>
          </div>
        </vxe-card>
      </vxe-col>

      <!-- Pending Orders -->
      <vxe-col :span="6">
        <vxe-card class="stat-card">
          <div class="flex flex-col">
            <div class="flex justify-between items-center mb-2">
              <h3 class="text-sm font-medium text-gray-500">Pending Orders</h3>
              <div class="bg-blue-100 text-blue-600 text-xs px-2 py-1 rounded">Monthly</div>
            </div>
            <div class="flex justify-between items-end">
              <div>
                <p class="text-2xl font-bold text-gray-800">45</p>
                <div class="flex items-center mt-1">
                  <span class="text-red-500 text-xs font-medium">-2%</span>
                  <span class="text-gray-500 text-xs ml-1">since last month</span>
                </div>
              </div>
              <img src="../../assets/orders-icon.svg" alt="Orders" class="h-12 w-12" />
            </div>
          </div>
        </vxe-card>
      </vxe-col>
    </vxe-row>

    <!-- Charts Row -->
    <vxe-row class="mb-6" :gutter="24">
      <!-- Total Revenue Chart -->
      <vxe-col :span="16">
        <vxe-card class="chart-card">
          <template #header>
            <div class="flex justify-between items-center">
              <h3 class="text-base font-medium text-gray-700">Total Revenue</h3>
              <div class="flex items-center">
                <div class="w-3 h-3 rounded-full bg-blue-500 mr-1"></div>
                <span class="text-xs text-gray-500 mr-4">This year</span>
                <div class="w-3 h-3 rounded-full bg-gray-300 mr-1"></div>
                <span class="text-xs text-gray-500">Last year</span>
              </div>
            </div>
          </template>
          <div ref="revenueChartRef" class="h-64"></div>
        </vxe-card>
      </vxe-col>

      <!-- Weekly Sales Chart -->
      <vxe-col :span="8">
        <vxe-card class="chart-card">
          <template #header>
            <div class="flex justify-between items-center">
              <h3 class="text-base font-medium text-gray-700">Weekly Sales</h3>
            </div>
          </template>
          <div class="flex flex-col items-center">
            <div ref="weeklySalesChartRef" class="h-40 w-40 mb-4"></div>
            <div class="text-center">
              <p class="text-2xl font-bold text-gray-800">+27%</p>
              <p class="text-sm text-gray-500">Weekly Growth</p>
            </div>
            <div class="grid grid-cols-3 gap-4 w-full mt-4">
              <div class="text-center">
                <p class="text-xs text-gray-500">Source</p>
                <p class="text-sm font-medium text-gray-800">Direct</p>
              </div>
              <div class="text-center">
                <p class="text-xs text-gray-500">Revenue</p>
                <p class="text-sm font-medium text-gray-800">$1,200</p>
              </div>
              <div class="text-center">
                <p class="text-xs text-gray-500">Value</p>
                <p class="text-sm font-medium text-gray-800">+15%</p>
              </div>
              <div class="text-center">
                <p class="text-xs text-gray-500">Quantity</p>
                <p class="text-sm font-medium text-gray-800">125</p>
              </div>
              <div class="text-center">
                <p class="text-xs text-gray-500">Target</p>
                <p class="text-sm font-medium text-gray-800">$1,000</p>
              </div>
              <div class="text-center">
                <p class="text-xs text-gray-500">Clients</p>
                <p class="text-sm font-medium text-gray-800">45</p>
              </div>
            </div>
          </div>
        </vxe-card>
      </vxe-col>
    </vxe-row>

    <!-- Mobile/Desktop and Latest Projects -->
    <vxe-row class="mb-6" :gutter="24">
      <!-- Mobile/Desktop Chart -->
      <vxe-col :span="8">
        <vxe-card class="chart-card">
          <template #header>
            <div class="flex justify-between items-center">
              <h3 class="text-base font-medium text-gray-700">Mobile/Desktop</h3>
            </div>
          </template>
          <div ref="deviceChartRef" class="h-64"></div>
        </vxe-card>
      </vxe-col>

      <!-- Latest Projects -->
      <vxe-col :span="16">
        <vxe-card class="chart-card">
          <template #header>
            <div class="flex justify-between items-center">
              <h3 class="text-base font-medium text-gray-700">Latest Projects</h3>
            </div>
          </template>
          <vxe-table :data="latestProjects" :border="false" :stripe="false" :show-header="true">
            <vxe-table-column field="name" title="Name" width="180"></vxe-table-column>
            <vxe-table-column field="startDate" title="Start Date" width="120"></vxe-table-column>
            <vxe-table-column field="endDate" title="End Date" width="120"></vxe-table-column>
            <vxe-table-column field="status" title="State" width="120">
              <template #default="{ row }">
                <div :class="getStatusClass(row.status)" class="px-2 py-1 rounded text-xs font-medium text-center">
                  {{ row.status }}
                </div>
              </template>
            </vxe-table-column>
            <vxe-table-column field="assignee" title="Assignee"></vxe-table-column>
          </vxe-table>
        </vxe-card>
      </vxe-col>
    </vxe-row>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import * as echarts from 'echarts'

// Chart refs
const revenueChartRef = ref(null)
const weeklySalesChartRef = ref(null)
const deviceChartRef = ref(null)

// Latest projects data
const latestProjects = ref([
  {
    name: 'Project Aurora',
    startDate: '01/01/2023',
    endDate: '31/05/2023',
    status: 'Done',
    assignee: 'James Spencer'
  },
  {
    name: 'Project Eagle',
    startDate: '01/04/2023',
    endDate: '30/06/2023',
    status: 'In Progress',
    assignee: 'Tracy Mack'
  },
  {
    name: 'Project Fireball',
    startDate: '01/01/2023',
    endDate: '31/05/2023',
    status: 'Done',
    assignee: 'Sallie Park'
  },
  {
    name: 'Project Omega',
    startDate: '01/01/2023',
    endDate: '31/05/2023',
    status: 'Rejected',
    assignee: 'Caitlin Long'
  },
  {
    name: 'Project Yoda',
    startDate: '01/01/2023',
    endDate: '31/05/2023',
    status: 'Done',
    assignee: 'Raymond Ennis'
  },
  {
    name: 'Project Zulu',
    startDate: '01/01/2023',
    endDate: '31/05/2023',
    status: 'Done',
    assignee: 'Matthew Williams'
  }
])

// Get status class for project status
const getStatusClass = (status) => {
  switch (status) {
    case 'Done':
      return 'bg-green-100 text-green-800'
    case 'In Progress':
      return 'bg-yellow-100 text-yellow-800'
    case 'Rejected':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

// Initialize charts
onMounted(() => {
  // Revenue Chart
  const revenueChart = echarts.init(revenueChartRef.value)
  const revenueOption = {
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: 'This Year',
        type: 'line',
        smooth: true,
        lineStyle: {
          width: 3,
          color: '#3b82f6'
        },
        data: [1000, 1200, 1100, 1300, 1400, 1800, 2000, 1900, 2200, 2100, 2300, 2400]
      },
      {
        name: 'Last Year',
        type: 'line',
        smooth: true,
        lineStyle: {
          width: 2,
          type: 'dashed',
          color: '#d1d5db'
        },
        data: [900, 1000, 1050, 1200, 1300, 1400, 1500, 1600, 1700, 1800, 1900, 2000]
      }
    ]
  }
  revenueChart.setOption(revenueOption)

  // Weekly Sales Chart (Pie Chart)
  const weeklySalesChart = echarts.init(weeklySalesChartRef.value)
  const weeklySalesOption = {
    series: [
      {
        type: 'pie',
        radius: ['70%', '90%'],
        avoidLabelOverlap: false,
        label: {
          show: false
        },
        emphasis: {
          label: {
            show: false
          }
        },
        data: [
          { value: 27, name: 'Growth', itemStyle: { color: '#3b82f6' } },
          { value: 25, name: 'Target', itemStyle: { color: '#f59e0b' } },
          { value: 18, name: 'Current', itemStyle: { color: '#10b981' } },
          { value: 30, name: 'Remaining', itemStyle: { color: '#e5e7eb' } }
        ]
      }
    ]
  }
  weeklySalesChart.setOption(weeklySalesOption)

  // Device Chart (Bar Chart)
  const deviceChart = echarts.init(deviceChartRef.value)
  const deviceOption = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['Mobile', 'Desktop'],
      bottom: 0
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: 'Mobile',
        type: 'bar',
        stack: 'total',
        barWidth: '30%',
        itemStyle: {
          color: '#93c5fd'
        },
        data: [40, 45, 35, 50, 55, 45, 60, 55, 50, 45, 50, 55]
      },
      {
        name: 'Desktop',
        type: 'bar',
        stack: 'total',
        barWidth: '30%',
        itemStyle: {
          color: '#3b82f6'
        },
        data: [60, 55, 65, 50, 45, 55, 40, 45, 50, 55, 50, 45]
      }
    ]
  }
  deviceChart.setOption(deviceOption)

  // Handle window resize
  window.addEventListener('resize', () => {
    revenueChart.resize()
    weeklySalesChart.resize()
    deviceChart.resize()
  })
})
</script>

<style scoped>
.container {
  padding: 1rem;
  max-width: 1280px;
  margin: 0 auto;
}

.stat-card {
  height: 100%;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.chart-card {
  height: 100%;
}
</style>