import axios from 'axios'
import './user'
import './menu'

// Import the custom axios instance
import customAxios from '../plugins/axios'

// Create mock adapter to intercept requests
const setupMock = () => {
  // Set up axios interceptor for mock data
  const originalAdapter = axios.defaults.adapter

  // Function to handle mock requests
  const mockHandler = async config => {
    // Check if the request URL is handled by our mock handlers
    const { url, method, data, params } = config

    // Convert URL to lowercase for case-insensitive matching
    const path = url.toLowerCase()

    // Process mock based on URL pattern
    if (path.includes('/user/login') && method === 'post') {
      const parsedData = typeof data === 'string' ? JSON.parse(data) : data
      return handleMock('userLogin', { data: parsedData })
    }

    if (path.includes('/user/info') && method === 'get') {
      return handleMock('userInfo', { params })
    }

    if (path.includes('/menu/list') && method === 'get') {
      return handleMock('menuList', { params })
    }

    // If no mock handler matches, use the original adapter
    return originalAdapter(config)
  }

  // Apply to default axios
  axios.defaults.adapter = mockHandler

  // Apply to custom axios instance if it exists
  if (customAxios && customAxios.interceptors) {
    // Add a response interceptor to the custom axios instance
    customAxios.interceptors.request.use(async config => {
      // Check if we're in development mode
      if (import.meta.env.DEV) {
        const { url, method, data } = config
        const path = url.toLowerCase()

        // Handle mock responses directly
        if (path.includes('/user/login') && method === 'post') {
          console.log('Intercepting login request with mock data')
        }

        if (path.includes('/user/info') && method === 'get') {
          console.log('Intercepting user info request with mock data')
        }

        if (path.includes('/menu/list') && method === 'get') {
          console.log('Intercepting menu list request with mock data')
        }
      }

      return config
    })
  }
}

// Handle mock response
const handleMock = (type, { data = {}, params = {} }) => {
  let mockData

  switch (type) {
    case 'userLogin':
      mockData = window.__MOCK_USER_LOGIN__(data)
      break
    case 'userInfo':
      mockData = window.__MOCK_USER_INFO__(params)
      break
    case 'menuList':
      mockData = window.__MOCK_MENU_LIST__(params)
      break
    default:
      mockData = { code: 404, message: 'Mock not found', data: null }
  }

  // Return as a Promise with mock response structure
  return Promise.resolve({
    data: mockData,
    status: 200,
    statusText: 'OK',
    headers: {},
    config: {},
    request: {}
  })
}

// Initialize mock
setupMock()