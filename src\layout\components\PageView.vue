<template>
  <div :class="['page-view', {'is-pg': padding, 'is-bg': background}]">
    <slot></slot>
    <vxe-loading :modelValue="loading" :text="loadingText"></vxe-loading>
  </div>
</template>

<script setup>
defineProps({
  loading: Boolean,
  loadingText: String,
  padding: {
    type: Boolean,
    default: true
  },
  background: {
    type: Boolean,
    default: true
  }
})
</script>

<style scoped>
.page-view {
  position: relative;
  height: 100%;
  min-width: 800px;
  overflow: auto;
  border-radius: 0.375rem;
}

.page-view.is-pg {
  padding: 1rem;
}

.page-view.is-bg {
  background-color: white;
}
</style>
