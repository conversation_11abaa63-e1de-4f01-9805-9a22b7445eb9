export interface LoginForm {
  username: string;
  password: string;
  remember?: boolean;
}

export interface UserInfo {
  id: number;
  username: string;
  realName: string;
  avatar?: string;
  roles: string[];
  permissions: string[];
  email?: string;
  phone?: string;
  department?: string;
  position?: string;
  createTime?: string;
  lastLoginTime?: string;
}

export interface LoginResponse {
  code: number;
  message: string;
  data: {
    token: string;
    userInfo: UserInfo;
  };
} 