<template>
  <vxe-form ref="formRef" v-bind="formProps" :data="formData" class="form-container" :readonly="props.formOptions.formType == 'view'">
    <template v-for="(_, name) in $slots" :key="name" #[name]="slotScope">
      <slot :name="name" v-bind="slotScope"></slot>
    </template>

    <template #action>
      <div class="tools-container" :class="[`tools-${formOptions.tools?.position || 'left'}`]">
        <template v-for="(tool, index) in formOptions.tools?.children" :key="index">
          <component :is="componentMap.get(tool.name)" v-bind="tool.props" v-on="getToolEvents(tool)"
            :options="tool.options" :style="{
              width: tool.props.width || '120px',
              marginRight: index === formOptions.tools?.children.length - 1 ? '0' : '8px'
            }" />
        </template>
      </div>
    </template>
  </vxe-form>
</template>

<script lang="tsx" setup>
import { computed, ref, onBeforeMount, onMounted, watch, h, reactive, onBeforeUnmount } from 'vue'
import request from '@/plugins/axios'
import { ExtendedFormProps, ExtendedFormItemProps, ExtendedFormItemRender, ToolItem, FormSize, selectApiConfig, KeyConfig } from '@/types/form'
import { VxeFormInstance, VxeFormEvents, VxeFormProps } from 'vxe-table'
import { VxeTag, VxeButton, VxeSelect, VxeInput, VxeSwitch, VxeButtonGroup, VxeIcon, VxeCheckbox, VxeImage, VxeImageGroup, VxeRadio, VxeTextarea, VxeRadioGroup, VxeText, VxeForm, VxeUI, VxeDatePicker, VxeTreeSelect,VxeUpload } from 'vxe-pc-ui'

interface Props {
  formOptions: ExtendedFormProps
}

const props = withDefaults(defineProps<Props>(), {
  formOptions: () => ({})
})

// console.log('表单只读',props.formOptions.formType)

// 获取字典数据
const dictData = mainStore.getValue('dictData')

const emit = defineEmits<{
  (e: 'submit', data: any): void
}>()

const formRef = ref<VxeFormInstance>()
const formItems = ref<ExtendedFormItemProps[]>([])

let formData = reactive({})

/**
 * 重置表单数据
 */
const resetForm = () => {
  // 重置表单数据
  formRef.value?.reset()

  // 清空componentValues
  componentValues.clear()

  // 如果是编辑模式，重新从初始数据初始化组件值
  if (props.formOptions.formType !== 'add') {
    if (props.formOptions.data) {
      // 优先使用 data 数据
      initComponentValues(props.formOptions.data)
    } else if (props.formOptions.getData) {
      try {
        const initialData = props.formOptions.getData()
        // 重新初始化组件值
        initComponentValues(initialData)
      } catch (error) {
        console.error('重置表单时初始化组件值失败:', error)
      }
    }
  }
}

/**
 * 提交表单数据
 */
const submitForm = async () => {
  const { applyData } = props.formOptions
  if (!applyData) return

  try {
    await applyData(formData)
    emit('submit', formData)
  } catch (error) {
    console.error('提交表单数据失败:', error)
    throw error
  }
}

/**
 * 获取当前表单数据
 * @returns 当前表单的所有数据
 */
const getFormData = () => {
  return formData
}

/**
 * 设置表单数据
 * @param data 表单数据
 * 只更新data中提供的字段，且formData中也存在的字段
 * 支持设置指定字段为null
 */
const setFormData = (data: any) => {
  if (!data) return;

  // 遍历提供的数据对象
  Object.keys(data).forEach(key => {
    // 只更新formData中已存在的字段
    if (key in formData) {
      // 允许设置值为null
      formData[key] = data[key];
    }
  });

  // 更新组件值，确保表单组件重新渲染
  initComponentValues(formData)
}

/**
 * 获取指定字段的值
 * @param field 字段名
 * @returns 字段值
 */
const getFieldValue = (field: string) => {
  return formData[field]
}

/**
 * 设置指定字段的可见性
 * @param field 字段名
 * @param visible 是否可见
 */
const setFieldVisible = (field: string, visible: boolean) => {
  formItems.value = formItems.value.map(item => {
    if (item.field === field) {
      return {
        ...item,
        visible: visible
      }
    }
    return item
  })
}

// 表单尺寸状态
const formSize = ref<FormSize>({
  original: undefined,//原始尺寸
  collapse: undefined,//折叠尺寸
  collapseStatus: false,//折叠状态
  itemHeight: 0,//单个对象得动态高度
  originalCoefficient: undefined,//原始尺寸系数
  collapseCoefficient: undefined,//折叠尺寸系数
  changeCoefficient: undefined,//折叠状态改变系数
  originalRatio: undefined//原始比值系数
})

// 折叠表单面板
const collapseForm = () => {
  const itemSpans = formItems.value.map(item => item.span)
  const groups = convertArrayToMoreArray(itemSpans)

  if (groups.length > 1) {
    const firstGroupLength = groups[0].length
    formItems.value = formItems.value.map((item, index) => ({
      ...item,
      visible: index === 0 ? true : (index < firstGroupLength || formSize.value.collapseStatus)
    }))

    formSize.value.collapseStatus = !formSize.value.collapseStatus
  }


  // 如果存在 panelSize 回调函数，则调用它并传递当前的 formSize 状态
  if (props.formOptions.panelSize) {
    const height = formSize.value.original?.height
    const width = formSize.value.original?.width

    console.log("formSize.value", formSize.value.original)
    // 根据折叠状态更新相应的尺寸
    if (formSize.value.collapseStatus) {
      // 获取表单项得高度 默认为 56.4
      const itemHeight = props.formOptions.formSize?.itemHeight || 56.4
      // 获取表单项之外得高度
      if (props.formOptions.tools?.position) {
        // 减去最顶层元素得高度
        let formHeight = height - (groups.length - 1) * itemHeight
        formSize.value.collapse = { width: width, height: formHeight }
      }
    } else {
      // 折叠表单得 高与宽
      formSize.value.collapse = { width: width, height: height }
    }

    // 计算原始比值系数
    formSize.value.originalRatio = {
      width: formSize.value.originalCoefficient?.width / formSize.value.original?.width,
      height: formSize.value.originalCoefficient?.height / formSize.value.original?.height
    }

    // 计算折叠尺寸系数
    formSize.value.collapseCoefficient = {
      width: formSize.value.collapse?.width * formSize.value.originalRatio?.width,
      height: formSize.value.collapse?.height * formSize.value.originalRatio?.height,
    }

    // 计算折叠状态改变系数
    formSize.value.changeCoefficient = {
      height: formSize.value.collapseCoefficient.height - formSize.value.originalCoefficient.height,
      width: formSize.value.collapseCoefficient.width - formSize.value.originalCoefficient.width
    }

    props.formOptions.panelSize(formSize.value)
  }
}


//将数组转换为二维数组,每行最大不超过24 否则就换行
const convertArrayToMoreArray = (array: any[]) => {
  let result = []
  let currentGroup = []
  let currentSum = 0

  for (let i = 0; i < array.length; i++) {
    const currentValue = array[i]

    // 如果当前组为空，直接添加当前元素
    if (currentGroup.length === 0) {
      currentGroup.push(currentValue)
      currentSum = currentValue
      continue
    }

    // 判断添加当前元素后是否会超过24
    if (currentSum + currentValue <= 24) {
      currentGroup.push(currentValue)
      currentSum += currentValue
    } else {
      // 如果会超过24，将当前组添加到结果中，并开始新的一组
      result.push([...currentGroup])
      currentGroup = [currentValue]
      currentSum = currentValue
    }
  }

  // 不要忘记将最后一组添加到结果中
  if (currentGroup.length > 0) {
    result.push(currentGroup)
  }

  return result
}

const bmFormRef = {
  formRef,
  resetForm,
  submitForm,
  getFormData,
  getFieldValue,
  collapseForm,
  setFieldVisible
}

// 添加组件映射
const componentMap = new Map<string, any>([
  ['VxeTag', VxeTag],
  ['VxeButton', VxeButton],
  ['VxeSelect', VxeSelect],
  ['VxeInput', VxeInput],
  ['VxeSwitch', VxeSwitch],
  ['VxeButtonGroup', VxeButtonGroup],
  ['VxeIcon', VxeIcon],
  ['VxeCheckbox', VxeCheckbox],
  ['VxeImage', VxeImage],
  ['VxeImageGroup', VxeImageGroup],
  ['VxeRadio', VxeRadio],
  ['VxeTextarea', VxeTextarea],
  ['VxeRadioGroup', VxeRadioGroup],
  ['VxeText', VxeText],
  ['VxeDatePicker', VxeDatePicker],
  ['VxeTreeSelect', VxeTreeSelect],
  ['VxeUpload', VxeUpload],
  ['VxeForm', VxeForm]
])

// 组件值存储
const componentValues = reactive(new Map())

/**
 * 处理多个子组件的渲染
 */
const handleItemRenders = (item: ExtendedFormItemProps) => {
  if (!item.itemRenders?.length) return item

  // 处理 visibleEvent
  if (item.visibleEvent) {
    item.visible = item.visibleEvent(formData)
  }

  return {
    ...item,
    slots: {
      default: () => {
        const totalComponents = item.itemRenders?.length

        return h('div',
          {
            style: {
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              width: '100%'
            }
          },
          item.itemRenders?.map((render: ExtendedFormItemRender, index: number) => {
            const { name, props = {}, events = {}, dict, selectApi } = render
            const componentId = `${item.field}!${index}`

            let options = render.options

            //如果同时存在 options
            if (dict) {
              //如果字典属性存在，则进行字典渲染
              options = dictData[dict]
            }

            let componentWidth = props.width
            if (!componentWidth) {
              componentWidth = `calc(${100 / totalComponents}% - ${(totalComponents - 1) * 8 / totalComponents}px)`
            } else if (typeof componentWidth === 'string' && componentWidth.includes('%')) {
              componentWidth = props.width
            } else {
              componentWidth = `${props.width}px`
            }

            return h('div',
              {
                style: {
                  width: componentWidth,
                  minWidth: 0,
                }
              },
              h(componentMap.get(name), {
                ...props,
                style: {
                  width: '100%',
                  ...props.style
                },
                options,
                modelValue: componentValues.get(componentId),
                onClick: (e: Event) => {
                  if (events.click) {
                    events.click(formData, bmFormRef,e)
                  }
                },
                onChange: (value: any) => {
                  // console.log("触发onChange",componentId,value)
                  // console.log(value, params)
                  const newValue = value?.value ?? value
                  componentValues.set(componentId, newValue)
                  // 获取所有组件的值
                  const allValues = item.itemRenders?.map((_, i) => {
                    return componentValues.get(`${item.field}!${i}`) || 'null'
                  })
                  // 更新表单数据
                  formData[item.field] = allValues?.join(',')

                  if (events.change) {
                    if (options && options.length > 0) {
                      const selectedOption = options.find((opt: any) =>
                        opt.value === newValue || opt.name === newValue
                      )
                      events.change({ value: allValues }, formData, bmFormRef)
                    } else {
                      events.change({ value: allValues }, formData, bmFormRef)
                    }
                  }

                }
              })
            )
          })
        )
      }
    }
  }
}

/**
 * 处理单个组件的渲染
 */
const handleItemRender = (item: ExtendedFormItemProps) => {
  if (!item.itemRender) return item

  // 处理 visibleEvent
  if (item.visibleEvent) {
    item.visible = item.visibleEvent(formData)
  }

  return {
    ...item,
    slots: {
      default: () => {
        const { name, props = {}, events = {}, dict, selectApi } = item.itemRender
        const componentId = item.field

        let options = item.itemRender.options

        //如果同时存在 options
        if (dict) {
          //如果字典属性存在，则进行字典渲染
          options = dictData[dict]
        }

        // 特殊处理 VxeForm 组件
        if (name === 'VxeForm') {
          return h('div',
            {
              style: {
                width: '100%',
                minWidth: 0,
              }
            },
            h(componentMap.get(name), {
              ...props,
              style: {
                width: '100%',
                ...props.style
              },
              // VxeForm 特有的属性
              data: componentValues.get(componentId) || {},
              items: props.items || [],
              rules: props.rules,
              span: props.span,
              titleWidth: props.titleWidth,
              titleAlign: props.titleAlign,
              titleOverflow: props.titleOverflow,
              titleColon: props.titleColon,
              onChange: (value: any) => {
                componentValues.set(componentId, value)
                formData[item.field] = value

                if (events.change) {
                  events.change(value, formData, bmFormRef)
                }
              },
              onSubmit: (params: any) => {
                if (events.submit) {
                  events.submit(params, formData, bmFormRef)
                }
              },
              onReset: (params: any) => {
                if (events.reset) {
                  events.reset(params, formData, bmFormRef)
                }
              }
            })
          )
        }

        // 其他组件的原有逻辑
        return h('div',
          {
            style: {
              width: '100%',
              minWidth: 0,
            }
          },
          h(componentMap.get(name), {
            ...props,
            style: {
              width: '100%',
              ...props.style
            },
            options,
            modelValue: componentValues.get(componentId),
            onClick: (e: Event) => {
              if (events.click) {
                events.click(formData, bmFormRef,e)
              }
            },
            onChange: (value: any) => {
              let newValue = null;
              // 这里需要根据不同的常用组件 进行单独逻辑处理
              if (name == 'VxeRadioGroup') {
                newValue = value.label
              } else {
                newValue = value?.value ?? value
              }
              componentValues.set(componentId, newValue)
              formData[item.field] = newValue

              if (events.change) {
                if (options && options.length > 0) {
                  const selectedOption = options.find((opt: any) =>
                    opt.value === newValue || opt.name === newValue
                  )
                  events.change(newValue, formData, bmFormRef)
                } else {
                  events.change(newValue, formData, bmFormRef)
                }
              }
            }
          })
        )
      }
    }
  }
}

/**
 * 从API获取选项数据
 */
const fetchOptionsFromApi = async (selectApi: selectApiConfig) => {
  try {
    const response = await request.post(selectApi.url, selectApi.body)
    const data = Array.isArray(response) ? response : (response.data || [])

    // 递归处理数据的函数
    const mapDataRecursively = (items: any[]): any[] => {
      return items.map((item: any) => {
        const result = {
          label: item[selectApi.config.keyFieldName],
          value: item[selectApi.config.valueFieldName],
          children: []
        }

        // 如果存在children字段并且是数组，递归处理
        if (selectApi.config.childrenFieldName &&
            item[selectApi.config.childrenFieldName] &&
            Array.isArray(item[selectApi.config.childrenFieldName]) &&
            item[selectApi.config.childrenFieldName].length > 0) {
          result.children = mapDataRecursively(item[selectApi.config.childrenFieldName])
        }
        return result
      })
    }

    // 使用递归函数处理数据
    const mappedData = mapDataRecursively(data)

    // console.log('递归处理后的数据', mappedData)

    return mappedData
  } catch (error) {
    console.error('Failed to fetch options from API:', error)
    return []
  }
}

/**
 * 初始化组件值
 */
const initComponentValues = (data: any) => {
  formItems.value.forEach(item => {
    const fieldValue = data[item.field]
    if (fieldValue !== undefined) {
      if (item.itemRenders?.length) {
        // 处理多个组件的情况
        const values = String(fieldValue).split(',')
        item.itemRenders.forEach((_, index) => {
          const componentId = `${item.field}!${index}`
          componentValues.set(componentId, values[index] || null)
        })
      } else if (item.itemRender) {
        // 处理单个组件的情况
        const componentId = item.field
        // console.log(componentId,fieldValue)
        componentValues.set(componentId, fieldValue)
      }
    }
  })
}

/**
 * 获取表单数据
 */
const fetchFormData = async () => {
  // 优先检查是否有 data
  if (props.formOptions.data) {
    formData = reactive(props.formOptions.data)
    // 初始化组件值
    initComponentValues(props.formOptions.data)
    return
  }

  // 如果没有 data，再检查 getData
  const { getData } = props.formOptions
  if (!getData) return

  try {
    // 检查 getData 是否为函数
    if (typeof getData === 'function') {
      const result = await getData()
      if (result && result.data) {
        formData = reactive(result.data)
        // 初始化组件值
        initComponentValues(result.data)
      }
    } else if (getData && typeof getData === 'object' && 'url' in getData) {
      // 如果 getData 是一个对象，且包含 url 属性，则尝试使用 request 发送请求
      try {
        const getDataObj = getData as any
        const apiMethod = (getDataObj.method || 'post').toLowerCase()
        const response = await request[apiMethod](
          getDataObj.url,
          getDataObj.body || {},
          { headers: getDataObj.header || {} }
        )
        if (response && response.data) {
          formData = reactive(response.data)
          // 初始化组件值
          initComponentValues(response.data)
        }
      } catch (apiError) {
        console.error('API请求失败:', apiError)
      }
    }
  } catch (error) {
    console.error('获取表单数据失败:', error)
  }
}

/**
 * 初始化表单项的选项数据
 */
const initFormItemsOptions = async () => {
  if (!props.formOptions.items) return

  const processedItems = await Promise.all(
    props.formOptions.items.map(async (item) => {
      // 处理多个子组件渲染
      if (item.itemRenders?.length) {
        // 修改这部分代码，使用Promise.all等待所有selectApi请求完成
        const updatedItemRenders = await Promise.all(
          item.itemRenders.map(async (itemRender) => {
            if (itemRender.selectApi) {
              const options = await fetchOptionsFromApi(itemRender.selectApi)
              return { ...itemRender, options }
            }
            return itemRender
          })
        )

        item.itemRenders = updatedItemRenders
        return handleItemRenders(item)
      }

      // 处理单个子组件渲染
      if (item.itemRender) {
        const { options = [], dict, selectApi } = item.itemRender

        // 处理选项数据优先级：options > dict > selectApi
        if (options && options.length > 0) {
          return handleItemRender(item)
        } else if (dict) {
          const dictOptions = dictData[dict]
          return handleItemRender({
            ...item,
            itemRender: {
              ...item.itemRender,
              options: dictOptions
            }
          })
        } else if (selectApi) {

          const apiOptions = await fetchOptionsFromApi(selectApi)
          return handleItemRender({
            ...item,
            itemRender: {
              ...item.itemRender,
              options: apiOptions
            }
          })
        }

        return handleItemRender(item)
      }

      return item
    })
  )

  formItems.value = processedItems
}

// 修改 watch 监听逻辑
watch(
  () => formData,
  (newVal) => {
    // 只更新需要根据条件显示/隐藏的表单项
    formItems.value.forEach((item) => {
      if (item.visibleEvent) {
        const newVisibility = item.visibleEvent(newVal)
        if (item.visible !== newVisibility) {
          // 使用类型断言确保类型兼容
          item.visible = newVisibility as typeof item.visible
        }
      }
    })
  },
  { deep: true }
)

// 在组件挂载前初始化数据
onBeforeMount(async () => {
  // 只有非add模式才需要获取表单数据
  if (props.formOptions.formType !== 'add') {
    await fetchFormData()
  }

  await initFormItemsOptions()

  //初次渲染进行值更新
  if (props.formOptions.formType !== 'add') {
    if (props.formOptions.data) {
      // 优先使用 data 数据
      formData = reactive(props.formOptions.data)
      // 初始化组件值
      initComponentValues(props.formOptions.data)
    } else if (typeof props.formOptions.getData === 'function') {
      // 只有在 data 为空的情况下才使用 getData() 函数
      try {
        const data = props.formOptions.getData()
        formData = reactive(data)
        // 初始化组件值
        initComponentValues(data)
      } catch (error) {
        console.error('初始化表单数据失败:', error)
      }
    }
  }
})

onMounted(() => {

  // //监听getData返回的值变化及时更新
  // if (props.formOptions.formType !== 'add') {
  //   if (props.formOptions.data) {
  //     // 优先使用 data 数据
  //     formData = reactive(props.formOptions.data)
  //     // 初始化组件值
  //     initComponentValues(props.formOptions.data)
  //   } else if (typeof props.formOptions.getData === 'function') {
  //     // 只有在 data 为空的情况下才使用 getData() 函数
  //     try {
  //       const data = props.formOptions.getData()
  //       formData = reactive(data)
  //       // 初始化组件值
  //       initComponentValues(data)
  //     } catch (error) {
  //       console.error('初始化表单数据失败:', error)
  //     }
  //   }
  // }

  observePanelSize()

  // 同步外部传入的表单尺寸配置
  if (props.formOptions.formSize) {
    formSize.value.original = props.formOptions.formSize.original
    formSize.value.collapse = props.formOptions.formSize.collapse
    formSize.value.collapseStatus = props.formOptions.formSize.collapseStatus ?? false
    formSize.value.itemHeight = props.formOptions.formSize.itemHeight
    formSize.value.originalCoefficient = props.formOptions.formSize.originalCoefficient
    formSize.value.collapseCoefficient = props.formOptions.formSize.collapseCoefficient
    formSize.value.changeCoefficient = props.formOptions.formSize.changeCoefficient
  }
})

/**
 * 计算表单属性
 */
const formProps = computed<VxeFormProps>(() => {
  let items = [...formItems.value]
  // 当 tools.position 存在时,添加工具栏项
  if (props.formOptions.tools?.position) {
    items.push({
      align: props.formOptions.tools.position,
      span: 24,
      slots: { default: 'action' }
    })
  }

  return {
    ...props.formOptions,
    items
  }
})

// 添加工具事件处理方法
const getToolEvents = (tool: ToolItem) => {
  const events: Record<string, any> = {}

  if (tool.events) {
    Object.entries(tool.events).forEach(([eventName, handler]) => {
      events[eventName] = (...args: any[]) => {
        handler(formData, bmFormRef, ...args)
      }
    })
  }

  return events
}

// 监听面板尺寸变化
const observePanelSize = () => {
  // 获取 form-container 元素
  const formContainer = formRef.value?.$el as HTMLElement
  if (!formContainer || !props.formOptions.panelSize) return

  const resizeObserver = new ResizeObserver((entries) => {
    for (const entry of entries) {
      const { width, height } = entry.contentRect

      const itemSpans = formItems.value.map(item => item.span)
      const groups = convertArrayToMoreArray(itemSpans)

      // console.log("groups",groups)
      // 根据折叠状态更新相应的尺寸
      if (formSize.value.collapseStatus) {
        // 获取表单项得高度 默认为 56.4
        const itemHeight = props.formOptions.formSize?.itemHeight || 56.4
        // 获取表单项之外得高度
        if (props.formOptions.tools?.position) {
          // 减去最顶层元素得高度
          let formHeight = height - (groups.length - 1) * itemHeight
          formSize.value.collapse = { width: width, height: formHeight }
        }
      } else {
        // 原始表单得 高与宽
        formSize.value.original = { width: width, height: height }
        // 折叠表单得 高与宽
        formSize.value.collapse = { width: width, height: height }
      }

      // 调用回调函数，传递当前尺寸状态
      // props.formOptions.panelSize?.(formSize.value)
    }
  })

  // 开始观察容器元素
  resizeObserver.observe(formContainer)

  // 组件卸载时清理
  onBeforeUnmount(() => {
    resizeObserver.disconnect()
  })
}

// 监听 panelSize 的变化
watch(
  () => props.formOptions.panelSize,
  (newValue) => {
    if (newValue) {
      observePanelSize()
    }
  }
)



// 暴露表单实例和方法
defineExpose({
  formRef,
  resetForm,
  submitForm,
  getFormData,
  setFormData,
  getFieldValue,
  collapseForm,
  formSize,
  setFieldVisible
})
</script>

<style scoped>
.form-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.tools-container {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  width: 100%;
}

.tools-left {
  justify-content: flex-start;
}

.tools-center {
  justify-content: center;
}

.tools-right {
  justify-content: flex-end;
}
</style>