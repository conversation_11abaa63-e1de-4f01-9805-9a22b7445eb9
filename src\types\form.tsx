import { VxeFormItemProps, VxeFormItemPropTypes, VxeFormProps, VxeFormPropTypes } from 'vxe-table'
import type { IResultData } from '@/api/entity/index'

/**
 * 扩展的表单属性接口
 * @template D - 数据类型泛型
 */
export interface ExtendedFormProps<D = any> extends VxeFormProps<D> {
    /** 表单项配置数组 */
    items?: ExtendedFormItemProps<D>[]
    /** 获取数据的配置 */
    getData: () => Promise<IResultData<D>> | D
    /** 提交数据的函数 */
    applyData: (data: D) => Promise<void> | void
    /** 表单类型 */
    formType: FormType
    /** 工具栏配置 */
    tools?: ToolsConfig
    /** 监听form表单得面板尺寸 , 这里得面板高和宽 仅保留系数*/
    panelSize?: (formSize: FormSize) => void
    /** 表单尺寸 */
    formSize?: FormSize
}

/**
 * 数据请求配置接口
 */
export interface FormSize {
    /** 原始尺寸 */
    original?: { width: number, height: number }
    /** 原始尺寸系数 */
    originalCoefficient?: { width: number, height: number }
    /** 折叠尺寸 */
    collapse?: { width: number, height: number }
    /** 折叠尺寸系数 */
    collapseCoefficient?: { width: number, height: number }
    /** 折叠状态 */
    collapseStatus?: boolean
    /** 单个对象得动态高度 */
    itemHeight?: number
    /** 折叠状态改变系数 正数代表增加,负数代表减少 */
    changeCoefficient?: { width: number, height: number }
    /** 原始比值系数 */
    originalRatio?: { width: number, height: number }
}

/**
 * 表单类型
 * - add: 新增
 * - edit: 编辑
 * - view: 查看
 */
export type FormType = 'add' | 'edit' | 'view'

/**
 * 数据请求配置接口
 */
export interface DataProps {
    /** 请求URL */
    url: string
    /** 请求方法 */
    method: string
    /** 请求体 */
    body: object
    /** 请求头 */
    header: object
}

/**
 * 扩展的表单项属性接口
 * @template D - 数据类型泛型
 */
export interface ExtendedFormItemProps<D = any> extends VxeFormItemProps<D> {
    /**
     * 单个项渲染配置
     */
    itemRender?: ExtendedFormItemRender<D>
    /**
     * 多个项渲染配置数组
     */
    itemRenders?: ExtendedFormItemRender<D>[]
    /** 是否显示的函数类型事件 */
    visibleEvent?: (data: D) => Boolean
}

/**
 * 扩展的表单项渲染配置接口
 * @template D - 数据类型泛型
 * @template P - 属性类型泛型
 */
export interface ExtendedFormItemRender<D = any, P = Record<string, any>> extends VxeFormItemPropTypes.ItemRender<D, P> {
    /** 字典键名 */
    dict?: string
    /** 下拉选择器数据接口 */
    selectApi?: selectApiConfig
    /** 选项配置数组 */
    options?: Array<{ label: string, value: string | number }>
}

/**
 * selectApi 接口对象
 */
export interface selectApiConfig {
    url?: string // 工具栏位置
    body?: Record<string,any> // 接口得请求JSON对象
    config?: KeyConfig // 字典的key和value字段名称
}

/**
 * 字段名称配置
 */
export interface KeyConfig {
    keyFieldName: string // 字典的key字段名称
    valueFieldName: string // 字典的value字段名称
    childrenFieldName?: string // 字典的children字段名称
}

/**
 * 工具栏配置接口
 */
export interface ToolsConfig {
    position?: 'left' | 'right' | 'center' // 工具栏位置
    children?: ToolItem[] // 工具项数组
}

/**
 * 工具项接口
 */
export interface ToolItem {
    name: string // 组件名称 
    props?: Record<string, any> // 组件属性
    options?: Array<{ label: string, value: string | number }> // 选项配置(针对Select等)
    events?: Record<string, (...args: any[]) => void> // 组件事件
}
