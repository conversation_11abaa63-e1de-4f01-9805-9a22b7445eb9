{"name": "vite-project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "pre": "vite build --mode pre", "testing": "vite build --mode testing", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@tailwindcss/vite": "^4.1.7", "@vue-flow/background": "^1.3.2", "@vue-flow/controls": "^1.1.2", "@vue-flow/core": "^1.44.0", "@vue-flow/minimap": "^1.5.3", "@vxe-ui/plugin-export-xlsx": "^4.2.1", "@vxe-ui/plugin-menu": "^4.0.9", "@vxe-ui/plugin-render-wangeditor": "^4.0.2", "axios": "^1.9.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "grid-layout-plus": "^1.1.0", "mitt": "^3.0.1", "pinia": "^3.0.2", "tailwindcss": "^4.1.7", "vite-plugin-compression2": "^1.3.3", "vue": "^3.5.14", "vue-router": "^4.5.1", "vxe-pc-ui": "4.6.11", "vxe-table": "4.13.31", "xe-utils": "^3.7.4"}, "devDependencies": {"@types/node": "^22.9.0", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "@vitejs/plugin-vue": "^5.2.4", "@vitejs/plugin-vue-jsx": "^4.1.2", "less": "^4.2.0", "typescript": "^5.8.3", "unplugin-auto-import": "^19.2.0", "unplugin-vue-components": "^28.5.0", "vite": "^6.3.5", "vite-plugin-lazy-import": "^1.0.7", "vue-tsc": "^2.2.10"}}