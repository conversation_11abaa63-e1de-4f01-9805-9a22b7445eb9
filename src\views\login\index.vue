<template>
  <div class="login-container">
    <vxe-card class="login-card" title="VXE Admin System" width="400">
      <template #title>
        <div class="text-center">
          <h2 class="text-3xl font-extrabold text-gray-900">
            VXE Admin System
          </h2>
          <p class="mt-2 text-sm text-gray-600">
            请输入账号和密码登录系统
          </p>
        </div>
      </template>
      
      <!-- Login Form -->
      <vxe-form @submit="handleLogin">
        <!-- Username -->
        <vxe-form-item title="用户名">
          <vxe-input
            v-model="loginForm.username"
            placeholder="请输入用户名"
            name="username"
          />
        </vxe-form-item>
        
        <!-- Password -->
        <vxe-form-item title="密码">
          <vxe-input 
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            name="password"
          />
        </vxe-form-item>
        
        <!-- Remember me -->
        <vxe-form-item>
          <div class="flex items-center justify-between">
            <vxe-checkbox v-model="loginForm.remember">记住我</vxe-checkbox>
            
            <vxe-link status="primary">忘记密码?</vxe-link>
          </div>
        </vxe-form-item>
        
        <!-- Submit Button -->
        <vxe-form-item>
          <vxe-button status="primary" :loading="loading" type="submit" content="登录" />
        </vxe-form-item>
      </vxe-form>
      
      <!-- Demo Credentials -->
      <template #footer>
        <div class="text-center text-sm text-gray-600">
          <p>测试账号: admin / 123456</p>
        </div>
      </template>
    </vxe-card>
  </div>
</template>

<script setup>
import { reactive, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '../../stores/user'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()
const loading = ref(false)

// Login form data
const loginForm = reactive({
  username: 'admin',
  password: '123456',
  remember: true
})

// Handle login
const handleLogin = async () => {
  loading.value = true
  
  try {
    await userStore.login(loginForm)
    
    // Redirect to dashboard or stored redirect path
    const redirectPath = route.query.redirect || '/dashboard'
    router.push(redirectPath)
  } catch (error) {
    alert(error.message || '登录失败，请检查用户名和密码！')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f3f4f6;
  padding: 3rem 1rem;
}

.login-card {
  max-width: 400px;
  margin: 0 auto;
}
</style> 