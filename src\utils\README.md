# Mock Data Generator

This utility helps generate dynamic mock data for development and testing purposes.

## Installation

Make sure `dayjs` is installed as a dependency:

```bash
npm install dayjs
# or
yarn add dayjs
```

## Basic Usage

```typescript
import { mockGenerator, FieldConfig } from './mockGenerator';

// Define the fields you want to generate
const fields: FieldConfig[] = [
  { name: 'id', type: 'id' },
  { name: 'username', type: 'username' },
  { name: 'email', type: 'email' },
  { name: 'createTime', type: 'datetime' }
];

// Generate 10 mock records
const mockData = mockGenerator.generateMockData(fields, 10);
console.log(mockData);

// Generate a response object (similar to API response)
const mockResponse = mockGenerator.generateMockResponse(fields, 10);
console.log(mockResponse);
```

## Supported Field Types

The mock generator supports the following field types:

| Type | Description | Example Value |
|------|-------------|---------------|
| id | Auto-incrementing ID | 1, 2, 3, ... |
| name | Full name | "John Doe" |
| username | Username | "admin" |
| realName | Full name (alias) | "Jane Smith" |
| avatar | Avatar URL | "https://avatars.githubusercontent.com/u/499550" |
| email | Email address | "<EMAIL>" |
| phone | Phone number | "13800138000" |
| department | Department name | "Technology" |
| position | Job position | "System Administrator" |
| date | Date string | "2023-01-01" |
| datetime | Datetime string | "2023-01-01 12:34:56" |
| roles | Array of role names | ["admin", "user"] |
| permissions | Array of permission names | ["view", "edit", "delete"] |
| boolean | Boolean value | true or false |
| number | Random number | 42 |
| paragraph | Multi-sentence text | "Lorem ipsum dolor sit amet. Consectetur adipiscing elit." |
| sentence | Single-sentence text | "Lorem ipsum dolor sit amet." |
| custom | Custom function | Anything returned by the function |

## Customization Options

You can customize field generation using the `options` and `custom` properties:

```typescript
const fields: FieldConfig[] = [
  // Custom ID value
  { name: 'id', type: 'id', custom: () => 1000 },
  
  // Number with min/max range
  { name: 'priority', type: 'number', options: { min: 1, max: 5 } },
  
  // Date with custom format
  { name: 'createdAt', type: 'date', options: { format: 'MM/DD/YYYY' } },
  
  // Custom field generation
  { name: 'tags', type: 'custom', custom: () => ['important', 'urgent'] }
];
```

## Example Implementation

Check out `src/mock/mockExample.ts` for complete implementation examples. 