<template>
  <div class="bm-flow-demo">
    <!-- 页面标题 -->
    <div class="demo-header">
      <h1>BmFlow 组件使用示例 - 右键对话框功能</h1>
      <p>这是一个基于 Vue Flow 封装的流程图组件，支持通过 JSON 配置快速创建交互式流程图。<br>
      <strong>新功能：</strong>右键点击节点可弹出配置对话框，支持自定义表单内容，点击画布或其他节点关闭对话框。</p>
    </div>

    <!-- 操作工具栏 -->
    <div class="demo-toolbar">
      <vxe-button @click="addNode" status="primary">
        <vxe-icon name="plus"></vxe-icon>
        添加节点
      </vxe-button>
      <vxe-button @click="fitView" status="info">
        <vxe-icon name="fullscreen"></vxe-icon>
        适应视图
      </vxe-button>
      <vxe-button @click="resetFlow" status="warning">
        <vxe-icon name="refresh"></vxe-icon>
        重置流程
      </vxe-button>
      <vxe-button @click="exportData" status="success">
        <vxe-icon name="download"></vxe-icon>
        导出数据
      </vxe-button>
    </div>

    <!-- BmFlow 组件 -->
    <div class="demo-content">
      <BmFlow
        ref="bmFlowRef"
        :config="flowConfig"
        :events="flowEvents"
        class="demo-flow"
      />
    </div>

    <!-- 事件日志 -->
    <div class="demo-logs">
      <h3>事件日志</h3>
      <div class="logs-container">
        <div
          v-for="(log, index) in eventLogs"
          :key="index"
          class="log-item"
          :class="log.type"
        >
          <span class="log-time">{{ log.time }}</span>
          <span class="log-event">{{ log.event }}</span>
          <span class="log-data">{{ log.data }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import BmFlow from '@/components/BmFlow.vue'
import type { BmFlowConfig, BmFlowEvents, BmFlowInstance } from '@/types/bm-flow'

// 组件引用
const bmFlowRef = ref<BmFlowInstance>()

// 事件日志
const eventLogs = ref<Array<{
  time: string
  event: string
  data: string
  type: string
}>>([])

// 添加日志
const addLog = (event: string, data: any, type: string = 'info') => {
  const now = new Date()
  const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`

  eventLogs.value.unshift({
    time,
    event,
    data: typeof data === 'object' ? JSON.stringify(data, null, 2) : String(data),
    type
  })

  // 限制日志数量
  if (eventLogs.value.length > 50) {
    eventLogs.value = eventLogs.value.slice(0, 50)
  }
}

// 流程图配置
const flowConfig = reactive<BmFlowConfig>({
  nodes: [
    {
      id: '1',
      type: 'input',
      position: { x: 100, y: 100 },
      data: {
        label: '开始节点',
        description: '流程的起始点'
      }
    },
    {
      id: '2',
      type: 'bmForm',
      position: { x: 350, y: 50 },
      data: {
        label: '用户信息表单',
        description: '收集用户基本信息',
        formConfig: {
          formType: 'add',
          items: [
            {
              field: 'username',
              title: '用户名',
              span: 12,
              itemRender: {
                name: 'VxeInput',
                props: {
                  placeholder: '请输入用户名',
                  clearable: true
                }
              }
            },
            {
              field: 'email',
              title: '邮箱',
              span: 12,
              itemRender: {
                name: 'VxeInput',
                props: {
                  placeholder: '请输入邮箱地址',
                  clearable: true
                }
              }
            },
            {
              field: 'phone',
              title: '手机号',
              span: 12,
              itemRender: {
                name: 'VxeInput',
                props: {
                  placeholder: '请输入手机号',
                  clearable: true
                }
              }
            },
            {
              field: 'department',
              title: '部门',
              span: 12,
              itemRender: {
                name: 'VxeSelect',
                props: {
                  placeholder: '请选择部门',
                  clearable: true,
                  options: [
                    { label: '技术部', value: 'tech' },
                    { label: '产品部', value: 'product' },
                    { label: '运营部', value: 'operation' }
                  ]
                }
              }
            }
          ],
          getData: () => ({
            username: '',
            email: '',
            phone: '',
            department: ''
          }),
          applyData: (data: any) => {
            addLog('表单提交', data, 'success')
            console.log('用户信息表单提交:', data)
          }
        }
      }
    },
    {
      id: '3',
      type: 'default',
      position: { x: 100, y: 300 },
      data: {
        label: '审批节点',
        description: '管理员审批用户信息'
      }
    },
    {
      id: '4',
      type: 'bmForm',
      position: { x: 350, y: 350 },
      data: {
        label: '审批意见',
        description: '填写审批意见和结果',
        formConfig: {
          formType: 'edit',
          items: [
            {
              field: 'result',
              title: '审批结果',
              span: 24,
              itemRender: {
                name: 'VxeRadioGroup',
                props: {
                  options: [
                    { label: '通过', value: 'approved' },
                    { label: '拒绝', value: 'rejected' },
                    { label: '待补充', value: 'pending' }
                  ]
                }
              }
            },
            {
              field: 'comment',
              title: '审批意见',
              span: 24,
              itemRender: {
                name: 'VxeTextarea',
                props: {
                  placeholder: '请填写审批意见',
                  rows: 3,
                  maxlength: 200,
                  showWordCount: true
                }
              }
            }
          ],
          getData: () => ({
            result: '',
            comment: ''
          }),
          applyData: (data: any) => {
            addLog('审批提交', data, 'warning')
            console.log('审批意见提交:', data)
          }
        }
      }
    },
    {
      id: '5',
      type: 'output',
      position: { x: 650, y: 200 },
      data: {
        label: '结束节点',
        description: '流程结束'
      }
    }
  ],
  edges: [
    {
      id: 'e1-2',
      source: '1',
      target: '2',
      type: 'default',
      label: '提交申请'
    },
    {
      id: 'e1-3',
      source: '1',
      target: '3',
      type: 'smoothstep',
      label: '转审批'
    },
    {
      id: 'e3-4',
      source: '3',
      target: '4',
      type: 'default',
      label: '填写意见'
    },
    {
      id: 'e2-5',
      source: '2',
      target: '5',
      type: 'step',
      label: '完成'
    },
    {
      id: 'e4-5',
      source: '4',
      target: '5',
      type: 'step',
      label: '审批完成'
    }
  ],
  options: {
    showBackground: true,
    backgroundType: 'dots',
    backgroundColor: '#f0f0f0',
    showControls: true,
    showMinimap: true,
    fitView: true,
    zoomOnScroll: true,
    nodesDraggable: true,
    nodesConnectable: true,
    elementsSelectable: true,
    minZoom: 0.1,
    maxZoom: 2
  },
  toolbar: {
    show: true,
    items: [
      {
        name: 'save',
        title: '保存',
        icon: 'save',
        onClick: (instance: any) => {
          const data = instance.toObject()
          addLog('保存流程', `节点数: ${data.nodes.length}, 边数: ${data.edges.length}`, 'success')
        }
      },
      {
        name: 'clear',
        title: '清空',
        icon: 'delete',
        onClick: () => {
          if (confirm('确定要清空所有节点吗？')) {
            flowConfig.nodes = []
            flowConfig.edges = []
            addLog('清空流程', '所有节点和边已清空', 'warning')
          }
        }
      }
    ]
  },
  rightClickDialog: {
    enabled: true,
    width: '450px',
    height: 'auto',
    title: '节点配置',
    formConfig: {
      formType: 'edit',
      items: [
        {
          field: 'label',
          title: '节点标签',
          span: 24,
          itemRender: {
            name: 'VxeInput',
            props: {
              placeholder: '请输入节点标签',
              clearable: true
            }
          }
        },
        {
          field: 'description',
          title: '节点描述',
          span: 24,
          itemRender: {
            name: 'VxeTextarea',
            props: {
              placeholder: '请输入节点描述',
              rows: 3,
              maxlength: 200,
              showWordCount: true
            }
          }
        },
        {
          field: 'priority',
          title: '优先级',
          span: 12,
          itemRender: {
            name: 'VxeSelect',
            props: {
              placeholder: '请选择优先级',
              clearable: true,
              options: [
                { label: '高', value: 'high' },
                { label: '中', value: 'medium' },
                { label: '低', value: 'low' }
              ]
            }
          }
        },
        {
          field: 'status',
          title: '状态',
          span: 12,
          itemRender: {
            name: 'VxeSelect',
            props: {
              placeholder: '请选择状态',
              clearable: true,
              options: [
                { label: '待处理', value: 'pending' },
                { label: '进行中', value: 'processing' },
                { label: '已完成', value: 'completed' }
              ]
            }
          }
        }
      ],
      getData: () => ({
        label: '',
        description: '',
        priority: '',
        status: ''
      }),
      applyData: (data: any) => {
        addLog('右键对话框提交', data, 'info')
        console.log('节点配置提交:', data)
      }
    }
  }
})

// 事件处理配置
const flowEvents: BmFlowEvents = {
  onNodeClick: (_event: any, node: any) => {
    addLog('节点点击', `节点ID: ${node.id}, 标签: ${node.data.label}`, 'info')
  },
  onNodeDoubleClick: (_event: any, node: any) => {
    addLog('节点双击', `节点ID: ${node.id}, 标签: ${node.data.label}`, 'info')
  },
  onNodeContextMenu: (_event: any, node: any) => {
    addLog('节点右键', `节点ID: ${node.id}, 标签: ${node.data.label}, 已显示配置对话框`, 'warning')
  },
  onEdgeClick: (_event: any, edge: any) => {
    addLog('边点击', `边ID: ${edge.id}, 标签: ${edge.label || '无标签'}`, 'info')
  },
  onConnect: (connection: any) => {
    addLog('新连接', `从 ${connection.source} 到 ${connection.target}`, 'success')
    // 自动添加新的边
    const newEdge = {
      id: `e${connection.source}-${connection.target}`,
      source: connection.source,
      target: connection.target,
      type: 'default',
      label: '新连接'
    }
    flowConfig.edges?.push(newEdge)
  },
  onElementsRemove: (elements: any) => {
    addLog('删除元素', `删除了 ${elements.length} 个元素`, 'warning')
  },
  onPaneClick: (_event: any) => {
    addLog('画布点击', '点击了空白区域', 'info')
  }
}

// 工具栏方法
const addNode = () => {
  const nodeId = `node-${Date.now()}`
  const newNode = {
    id: nodeId,
    type: 'default',
    position: {
      x: Math.random() * 400 + 100,
      y: Math.random() * 300 + 100
    },
    data: {
      label: `新节点 ${nodeId}`,
      description: '动态添加的节点'
    }
  }

  flowConfig.nodes?.push(newNode)
  addLog('添加节点', `节点ID: ${nodeId}`, 'success')
}

const fitView = () => {
  bmFlowRef.value?.fitView()
  addLog('适应视图', '视图已适应画布', 'info')
}

const resetFlow = () => {
  if (confirm('确定要重置流程图吗？这将恢复到初始状态。')) {
    // 重置到初始配置
    flowConfig.nodes = [
      {
        id: '1',
        type: 'input',
        position: { x: 100, y: 100 },
        data: {
          label: '开始节点',
          description: '流程的起始点'
        }
      },
      {
        id: '5',
        type: 'output',
        position: { x: 650, y: 200 },
        data: {
          label: '结束节点',
          description: '流程结束'
        }
      }
    ]
    flowConfig.edges = []
    addLog('重置流程', '流程图已重置到初始状态', 'warning')
  }
}

const exportData = () => {
  const data = {
    nodes: flowConfig.nodes,
    edges: flowConfig.edges,
    timestamp: new Date().toISOString()
  }

  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `flow-config-${Date.now()}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)

  addLog('导出数据', `导出了 ${data.nodes?.length || 0} 个节点和 ${data.edges?.length || 0} 条边`, 'success')
}
</script>

<style scoped>
.bm-flow-demo {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f5f5f5;
  padding: 16px;
  gap: 16px;
}

.demo-header {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.demo-header h1 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.demo-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

.demo-toolbar {
  display: flex;
  gap: 12px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.demo-content {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  min-height: 500px;
}

.demo-flow {
  width: 100%;
  height: 100%;
}

.demo-logs {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 16px;
  max-height: 200px;
  overflow: hidden;
}

.demo-logs h3 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.logs-container {
  max-height: 150px;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 8px;
  background: #fafafa;
}

.log-item {
  display: flex;
  gap: 12px;
  padding: 4px 0;
  border-bottom: 1px solid #f0f0f0;
  font-size: 12px;
  font-family: 'Courier New', monospace;
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  color: #999;
  min-width: 60px;
}

.log-event {
  color: #333;
  font-weight: 600;
  min-width: 80px;
}

.log-data {
  color: #666;
  flex: 1;
  word-break: break-all;
}

.log-item.info .log-event {
  color: #409eff;
}

.log-item.success .log-event {
  color: #67c23a;
}

.log-item.warning .log-event {
  color: #e6a23c;
}

.log-item.error .log-event {
  color: #f56c6c;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .bm-flow-demo {
    padding: 8px;
    gap: 8px;
  }

  .demo-toolbar {
    flex-wrap: wrap;
    padding: 12px;
  }

  .demo-content {
    min-height: 400px;
  }

  .demo-logs {
    max-height: 150px;
  }

  .logs-container {
    max-height: 100px;
  }

  .log-item {
    flex-direction: column;
    gap: 4px;
  }

  .log-time,
  .log-event {
    min-width: auto;
  }
}
</style>