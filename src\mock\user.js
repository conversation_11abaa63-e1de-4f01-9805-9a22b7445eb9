// Mock user login
window.__MOCK_USER_LOGIN__ = (data) => {
  const { username, password } = data
  
  // Check login credentials
  if (username === 'admin' && password === '123456') {
    return {
      code: 200,
      message: 'Login successful',
      data: {
        token: 'mock-token-admin-2023',
        userInfo: {
          id: 1,
          username: 'admin',
          realName: 'Administrator',
          avatar: 'https://avatars.githubusercontent.com/u/499550',
          roles: ['admin'],
          permissions: ['*'],
          email: '<EMAIL>',
          phone: '***********',
          department: 'Technology',
          position: 'System Administrator',
          createTime: '2023-01-01 00:00:00',
          lastLoginTime: '2023-09-01 12:34:56'
        }
      }
    }
  } else if (username === 'user' && password === '123456') {
    return {
      code: 200,
      message: 'Login successful',
      data: {
        token: 'mock-token-user-2023',
        userInfo: {
          id: 2,
          username: 'user',
          realName: 'Regular User',
          avatar: 'https://avatars.githubusercontent.com/u/499551',
          roles: ['user'],
          permissions: ['view'],
          email: '<EMAIL>',
          phone: '***********',
          department: 'Business',
          position: 'Staff',
          createTime: '2023-02-01 00:00:00',
          lastLoginTime: '2023-09-02 10:20:30'
        }
      }
    }
  }
  
  // Invalid credentials
  return {
    code: 401,
    message: 'Invalid username or password',
    data: null
  }
}

// Mock user info
window.__MOCK_USER_INFO__ = () => {
  // In a real application, this would use the token to determine which user info to return
  // For simplicity, we'll always return the admin user
  return {
    code: 200,
    message: 'Success',
    data: {
      id: 1,
      username: 'admin',
      realName: 'Administrator',
      avatar: 'https://avatars.githubusercontent.com/u/499550',
      roles: ['admin'],
      permissions: ['*'],
      email: '<EMAIL>',
      phone: '***********',
      department: 'Technology',
      position: 'System Administrator',
      createTime: '2023-01-01 00:00:00',
      lastLoginTime: '2023-09-01 12:34:56'
    }
  }
} 