# BmFlow 组件使用说明

BmFlow 是一个基于 Vue Flow 封装的流程图组件，支持通过 JSON 配置快速创建交互式流程图，并且可以在节点中嵌入 BmForm 表单组件。

## 特性

- 📊 **JSON 配置驱动** - 通过简单的 JSON 配置即可创建复杂的流程图
- 🎨 **自定义节点** - 支持多种预定义节点类型，包括可嵌入表单的 bmForm 节点
- 🔗 **灵活连接** - 支持多种边类型和连接方式
- 🛠️ **工具栏支持** - 内置工具栏，支持自定义工具按钮
- 📱 **响应式设计** - 支持缩放、平移、拖拽等交互操作
- 🎯 **事件系统** - 完整的事件回调支持

## 安装依赖

```bash
npm install @vue-flow/core @vue-flow/background @vue-flow/controls @vue-flow/minimap
```

## 基本用法

### 1. 导入组件

```vue
<script setup lang="ts">
import BmFlow from '@/components/BmFlow.vue'
import type { BmFlowConfig } from '@/types/bm-flow'
</script>
```

### 2. 创建配置

```typescript
const flowConfig: BmFlowConfig = {
  nodes: [
    {
      id: '1',
      type: 'input',
      position: { x: 100, y: 100 },
      data: {
        label: '开始节点'
      }
    },
    {
      id: '2',
      type: 'bmForm',
      position: { x: 300, y: 100 },
      data: {
        label: '表单节点',
        formConfig: {
          formType: 'add',
          items: [
            {
              field: 'name',
              title: '姓名',
              span: 12,
              itemRender: {
                name: 'VxeInput',
                props: { placeholder: '请输入姓名' }
              }
            }
          ],
          getData: () => ({}),
          applyData: (data) => console.log(data)
        }
      }
    }
  ],
  edges: [
    {
      id: 'e1-2',
      source: '1',
      target: '2',
      type: 'default'
    }
  ],
  options: {
    showBackground: true,
    showControls: true,
    fitView: true
  }
}
```

### 3. 使用组件

```vue
<template>
  <BmFlow 
    :config="flowConfig" 
    :events="flowEvents"
    style="width: 100%; height: 600px;"
  />
</template>
```

## 节点类型

### 1. 基础节点类型

- **input** - 输入节点，通常作为流程的起点
- **output** - 输出节点，通常作为流程的终点
- **default** - 默认节点，可以有多个输入和输出连接点

### 2. 自定义节点类型

- **bmForm** - 表单节点，可以在节点中嵌入 BmForm 组件

#### bmForm 节点配置示例

```typescript
{
  id: 'form-node-1',
  type: 'bmForm',
  position: { x: 300, y: 100 },
  data: {
    label: '用户信息表单',
    formConfig: {
      formType: 'add',
      items: [
        {
          field: 'username',
          title: '用户名',
          span: 12,
          itemRender: {
            name: 'VxeInput',
            props: { placeholder: '请输入用户名' }
          }
        },
        {
          field: 'email',
          title: '邮箱',
          span: 12,
          itemRender: {
            name: 'VxeInput',
            props: { placeholder: '请输入邮箱' }
          }
        }
      ],
      getData: () => ({ username: '', email: '' }),
      applyData: (data) => {
        console.log('表单提交:', data)
      }
    }
  }
}
```

## 边类型

- **default** - 默认贝塞尔曲线
- **step** - 阶梯线
- **smoothstep** - 平滑阶梯线
- **straight** - 直线

## 工具栏配置

```typescript
toolbar: {
  show: true,
  position: 'top', // 'top' | 'bottom' | 'left' | 'right'
  tools: [
    {
      name: 'fitView',
      label: '适应视图',
      type: 'button',
      onClick: (flowInstance) => {
        flowInstance.fitView()
      }
    },
    {
      name: 'separator',
      type: 'separator'
    },
    {
      name: 'addNode',
      label: '添加节点',
      type: 'button',
      onClick: (flowInstance) => {
        // 添加节点逻辑
      }
    }
  ]
}
```

## 事件处理

```typescript
const flowEvents: BmFlowEvents = {
  onNodeClick: (event, node) => {
    console.log('节点点击:', node)
  },
  onNodeDoubleClick: (event, node) => {
    console.log('节点双击:', node)
  },
  onEdgeClick: (event, edge) => {
    console.log('边点击:', edge)
  },
  onConnect: (connection) => {
    console.log('新连接:', connection)
    // 可以在这里添加新的边到配置中
  }
}
```

## 组件实例方法

通过 ref 可以访问组件实例方法：

```vue
<script setup>
const bmFlowRef = ref()

// 添加节点
const addNode = () => {
  bmFlowRef.value.addNode({
    id: 'new-node',
    type: 'default',
    position: { x: 100, y: 100 },
    data: { label: '新节点' }
  })
}

// 适应视图
const fitView = () => {
  bmFlowRef.value.fitView()
}
</script>

<template>
  <BmFlow ref="bmFlowRef" :config="flowConfig" />
</template>
```

## 完整示例

参考 `src/views/BmFlowDemo.vue` 文件查看完整的使用示例。

## 注意事项

1. 确保每个节点和边都有唯一的 ID
2. bmForm 节点需要提供完整的 formConfig 配置
3. 组件需要明确的宽高才能正常显示
4. 建议在容器组件中使用，确保有足够的显示空间

## 类型定义

所有相关的类型定义都在 `src/types/bm-flow.ts` 文件中，包括：

- `BmFlowConfig` - 主配置接口
- `BmFlowNode` - 节点配置接口
- `BmFlowEdge` - 边配置接口
- `BmFlowOptions` - 选项配置接口
- `BmFlowEvents` - 事件接口
- `BmFlowInstance` - 实例方法接口
