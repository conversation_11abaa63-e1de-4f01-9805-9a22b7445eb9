<template>
  <vxe-layout-container class="layout-container" :size="appStore.componentsSize">
    <vxe-layout-aside class="page-aside" :width="240" :collapsed="appStore.collapseAside">
      <AsideView />
    </vxe-layout-aside>
    <vxe-layout-container vertical>
      <vxe-layout-header class="layout-header">
        <HeaderView />
        <TopView />
      </vxe-layout-header>
      <vxe-layout-body :key="appStore.pageKey" class="page-body">
        <RouterView />
      </vxe-layout-body>
      <vxe-layout-footer class="layout-footer">
        <FooterView />
      </vxe-layout-footer>
    </vxe-layout-container>
  </vxe-layout-container>
</template>

<script setup>
import { useAppStore } from '@/stores/app'
import HeaderView from './components/HeaderView.vue'
import AsideView from './components/AsideView.vue'
import TopView from './components/TopView.vue'
import FooterView from './components/FooterView.vue'

const appStore = useAppStore()
</script>

<style scoped>
.layout-container {
  min-height: 100vh;
}

.page-aside {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  background-color: white;
  border-right: 1px solid #e5e7eb;
}

.layout-header {
  border-bottom: 1px solid #e5e7eb;
}

.page-body {
  padding: 1rem;
  background-color: #f9fafb;
}

.layout-footer {
  border-top: 1px solid #e5e7eb;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  text-align: center;
  color: #6b7280;
  font-size: 0.875rem;
}
</style>
