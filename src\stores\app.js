import { defineStore } from 'pinia'

/**
 * App store for managing application-wide settings
 */
export const useAppStore = defineStore('app', {
  state: () => ({
    // Layout settings
    collapseAside: false,
    theme: localStorage.getItem('theme') || 'light',
    primaryColor: localStorage.getItem('primaryColor') || '#409eff',
    componentsSize: localStorage.getItem('componentsSize') || '',
    language: localStorage.getItem('language') || 'zh-CN',
    
    // Page reload key
    pageKey: 0
  }),
  
  actions: {
    /**
     * Toggle sidebar collapse state
     */
    toggleCollapseAside() {
      this.collapseAside = !this.collapseAside
    },
    
    /**
     * Set theme (light/dark)
     * @param {string} theme - Theme name
     */
    setTheme(theme) {
      this.theme = theme
      localStorage.setItem('theme', theme)
      
      // Apply theme to document
      document.documentElement.setAttribute('data-vxe-ui-theme', theme)
    },
    
    /**
     * Set primary color
     * @param {string} color - Color hex code
     */
    setPrimaryColor(color) {
      this.primaryColor = color
      localStorage.setItem('primaryColor', color)
      
      // Apply primary color to CSS variables
      document.documentElement.style.setProperty('--el-color-primary', color)
    },
    
    /**
     * Set components size
     * @param {string} size - Component size (medium, small, mini)
     */
    setComponentsSize(size) {
      this.componentsSize = size
      localStorage.setItem('componentsSize', size)
    },
    
    /**
     * Set language
     * @param {string} lang - Language code
     */
    setLanguage(lang) {
      this.language = lang
      localStorage.setItem('language', lang)
    },
    
    /**
     * Reload current page
     */
    reloadPage() {
      this.pageKey = Date.now()
    }
  }
})
