import { PropType } from 'vue'
import { Layout , ResponsiveLayout } from 'grid-layout-plus'

export const gridProps = {
  autoSize: {
    type: Boolean,
    default: true
  },
  colNum: {
    type: Number,
    default: 12
  },
  rowHeight: {
    type: Number,
    default: 150
  },
  maxRows: {
    type: Number,
    default: Infinity
  },
  margin: {
    type: Array as PropType<number[]>,
    default: () => [10, 10]
  },
  isDraggable: {
    type: Boolean,
    default: true
  },
  isResizable: {
    type: Boolean,
    default: true
  },
  isMirrored: {
    type: Boolean,
    default: false
  },
  isBounded: {
    type: Boolean,
    default: false
  },
  useCssTransforms: {
    type: Boolean,
    default: true
  },
  verticalCompact: {
    type: Boolean,
    default: true
  },
  restoreOnDrag: {
    type: Boolean,
    default: false
  },
  layout: {
    type: Array as PropType<Layout>,
    required: true
  },
  responsive: {
    type: Boolean,
    default: false
  },
  responsiveLayouts: {
    type: Object as PropType<Partial<ResponsiveLayout>>,
    default: () => ({})
  },
  transformScale: {
    type: Number,
    default: 1
  },
  useStyleCursor: {
    type: Boolean,
    default: true
  }
}

export const gridEmits = [
  'layout-before-mount',
  'layout-mounted',
  'layout-updated',
  'layout-ready',
  'update:layout',
  'breakpoint-changed'
]
