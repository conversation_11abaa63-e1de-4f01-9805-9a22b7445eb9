<template>
  <vxe-modal
    v-model="dialogOptions.visible"
    :title="dialogOptions.title"
    :width="formattedWidth"
    :height="formattedHeight"
    :show-close="true"
    :show-footer="true"
    :show-cancel-button="true"
    :show-confirm-button="true"
    :cancel-button-text="'取消'"
    :confirm-button-text="'确定'"
    @confirm="handleConfirm"
    @cancel="handleCancel"
    destroy-on-close
    resize
    show-zoom
  >
    <div class="bm-dialog-container">
      <bm-grid-layout
        ref="gridLayoutRef"
        :layout="dialogOptions.layout || []"
        :col-num="12"
        :row-height="30"
        :is-draggable="false"
        :is-resizable="false"
        :vertical-compact="false"
        :margin="[10, 10]"
        :use-css-transforms="true"
        :auto-size="true"
        @size-change="handleSizeChange"
      >
        <bm-grid-item
          v-for="item in dialogOptions.layout"
          :key="item.i"
          :x="item.x"
          :y="item.y"
          :w="item.w"
          :h="item.h"
          :i="item.i"
          class="grid-item"
        >
          <div class="grid-item-content">
            <!-- 根据配置渲染表单或表格 -->
            <template v-if="dialogOptions.dialogOptions && dialogOptions.dialogOptions.get(item.i)">
              <bm-form
                v-if="isFormComponent(item.i)"
                :ref="el => setFormRef(el, item.i)"
                :form-options="dialogOptions.dialogOptions.get(item.i) as any"
              />
              <bm-grid
                v-else
                :ref="el => setGridRef(el, item.i)"
                :grid-options="dialogOptions.dialogOptions.get(item.i) as any"
              />
            </template>
          </div>
        </bm-grid-item>
      </bm-grid-layout>

      <!-- 工具栏 -->
      <div v-if="dialogOptions.tools && dialogOptions.tools.length > 0" class="tools-container">
        <template v-for="(tool, toolIndex) in dialogOptions.tools" :key="toolIndex">
          <div
            class="tool-item"
            :class="[`tool-${tool.position || 'left'}`]"
            :style="{
              width: tool.width || 'auto',
              marginLeft: tool.margin?.left || '0',
              marginRight: tool.margin?.right || '0',
              marginTop: tool.margin?.top || '0',
              marginBottom: tool.margin?.bottom || '0'
            }"
          >
            <component
              :is="componentMap.get(tool.name)"
              v-bind="tool.props"
              v-on="getToolEvents(tool)"
            />
          </div>
        </template>
      </div>
    </div>
  </vxe-modal>
</template>

<script lang="ts" setup>
import { ref, computed, reactive } from 'vue'
import { VxeModal, VxeIcon, VxeTag, VxeButton, VxeSelect, VxeInput, VxeSwitch, VxeButtonGroup, VxeCheckbox, VxeImage, VxeImageGroup, VxeRadio, VxeTextarea, VxeRadioGroup, VxeText, VxeDatePicker, VxeTreeSelect, VxeUpload } from 'vxe-pc-ui'
import BmForm from './BmForm.vue'
import BmGrid from './BmGrid.vue'
import BmGridLayout from './gridlayout/BmGridLayout.vue'
import BmGridItem from './gridlayout/BmGridItem.vue'
import { DialogProps, ToolItem } from '@/types/dialog'

// 扩展 DialogProps 接口，添加 visible 属性
interface ExtendedDialogProps extends Omit<DialogProps, 'width' | 'height'> {
  visible: boolean
  width?: string | number
  height?: string | number
  dialogOptions?: Map<string, any>
  tools?: ToolItem[]
}

interface Props {
  dialogOptions: ExtendedDialogProps
}

const props = withDefaults(defineProps<Props>(), {
  dialogOptions: () => ({
    visible: false,
    title: '',
    layout: [],
    dialogOptions: new Map()
  })
})

const emit = defineEmits<{
  (e: 'confirm', data: any): void
  (e: 'cancel'): void
  (e: 'update:dialogOptions', options: Props['dialogOptions']): void
}>()

// 引用
const gridLayoutRef = ref()
const formRefs = reactive(new Map())
const gridRefs = reactive(new Map())

// 格式化宽度，支持像素值和百分比
const formattedWidth = computed(() => {
  const width = props.dialogOptions.width
  if (!width) return '800px' // 默认宽度

  // 如果已经是字符串且包含单位（px 或 %），直接返回
  if (typeof width === 'string' && (width.includes('px') || width.includes('%'))) {
    return width
  }

  // 如果是数字或不带单位的字符串，添加 px 单位
  return `${width}px`
})

// 格式化高度，支持像素值和百分比
const formattedHeight = computed(() => {
  const height = props.dialogOptions.height
  if (!height) return '600px' // 默认高度

  // 如果已经是字符串且包含单位（px 或 %），直接返回
  if (typeof height === 'string' && (height.includes('px') || height.includes('%'))) {
    return height
  }

  // 如果是数字或不带单位的字符串，添加 px 单位
  return `${height}px`
})

// 判断是否为表单组件
const isFormComponent = (id: string) => {
  const dialogOptions = props.dialogOptions.dialogOptions
  if (!dialogOptions) return false

  const component = dialogOptions.get(id)
  if (!component) return false

  // 通过检查特定属性来判断是表单还是表格
  return 'items' in component && 'formType' in component
}

// 设置表单引用
const setFormRef = (el: any, id: string) => {
  if (el) {
    formRefs.set(id, el)
  }
}

// 设置表格引用
const setGridRef = (el: any, id: string) => {
  if (el) {
    gridRefs.set(id, el)
  }
}

// 处理布局大小变化
const handleSizeChange = (size: any) => {
  console.log('Layout size changed:', size)
}

// 确认按钮处理
const handleConfirm = async () => {
  try {
    // 收集所有表单数据
    const formData = {}

    for (const [_, formRef] of formRefs.entries()) {
      if (formRef && formRef.getFormData) {
        const data = formRef.getFormData()
        Object.assign(formData, data)
      }
    }

    // 收集所有表格数据
    const gridData = {}

    for (const [id, gridRef] of gridRefs.entries()) {
      if (gridRef && gridRef.getTableInstance) {
        const tableInstance = gridRef.getTableInstance()
        if (tableInstance) {
          const data = tableInstance.getTableData().fullData
          gridData[`grid_${id}`] = data
        }
      }
    }

    emit('confirm', { forms: formData, grids: gridData })

    // 关闭对话框
    const newOptions = { ...props.dialogOptions, visible: false }
    emit('update:dialogOptions', newOptions)
  } catch (error) {
    console.error('确认时发生错误:', error)
  }
}

// 取消按钮处理
const handleCancel = () => {
  emit('cancel')
  // 关闭对话框
  const newOptions = { ...props.dialogOptions, visible: false }
  emit('update:dialogOptions', newOptions)
}

// 设置对话框可见性
const setVisible = (visible: boolean) => {
  const newOptions = { ...props.dialogOptions, visible }
  emit('update:dialogOptions', newOptions)
}

// 设置对话框宽度
const setWidth = (width: string | number) => {
  const newOptions = { ...props.dialogOptions, width }
  emit('update:dialogOptions', newOptions)
}

// 设置对话框高度
const setHeight = (height: string | number) => {
  const newOptions = { ...props.dialogOptions, height }
  emit('update:dialogOptions', newOptions)
}

// 同时设置对话框宽度和高度
const setSize = (width: string | number, height: string | number) => {
  const newOptions = { ...props.dialogOptions, width, height }
  emit('update:dialogOptions', newOptions)
}

// 获取所有表单数据
const getAllFormData = () => {
  const allData = {}

  for (const [_, formRef] of formRefs.entries()) {
    if (formRef && formRef.getFormData) {
      const data = formRef.getFormData()
      Object.assign(allData, data)
    }
  }

  return allData
}

// 获取所有表格数据
const getAllGridData = () => {
  const allData = {}

  for (const [id, gridRef] of gridRefs.entries()) {
    if (gridRef && gridRef.getTableInstance) {
      const tableInstance = gridRef.getTableInstance()
      if (tableInstance) {
        allData[`grid_${id}`] = tableInstance.getTableData().fullData
      }
    }
  }

  return allData
}

// 组件映射
const componentMap = new Map<string, any>([
  ['VxeTag', VxeTag],
  ['VxeButton', VxeButton],
  ['VxeSelect', VxeSelect],
  ['VxeInput', VxeInput],
  ['VxeSwitch', VxeSwitch],
  ['VxeButtonGroup', VxeButtonGroup],
  ['VxeIcon', VxeIcon],
  ['VxeCheckbox', VxeCheckbox],
  ['VxeImage', VxeImage],
  ['VxeImageGroup', VxeImageGroup],
  ['VxeRadio', VxeRadio],
  ['VxeTextarea', VxeTextarea],
  ['VxeRadioGroup', VxeRadioGroup],
  ['VxeText', VxeText],
  ['VxeDatePicker', VxeDatePicker],
  ['VxeTreeSelect', VxeTreeSelect],
  ['VxeUpload', VxeUpload]
])

// 获取工具事件
const getToolEvents = (tool: ToolItem) => {
  const events: Record<string, any> = {}

  if (tool.events) {
    Object.entries(tool.events).forEach(([eventName, handler]) => {
      if (handler) {
        events[eventName] = (...args: any[]) => {
          handler(getAllFormData(), {
            formRefs,
            gridRefs,
            setVisible
          }, ...args)
        }
      }
    })
  }

  return events
}

// 暴露方法
defineExpose({
  setVisible,
  setWidth,
  setHeight,
  setSize,
  getAllFormData,
  getAllGridData,
  formRefs,
  gridRefs,
  // 添加获取当前宽度和高度的方法
  getWidth: () => formattedWidth.value,
  getHeight: () => formattedHeight.value
})
</script>

<style scoped>
.bm-dialog-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.grid-item {
  overflow: hidden;
  background-color: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #ebeef5;
}

.grid-item-content {
  width: 100%;
  height: 100%;
  padding: 10px;
  overflow: auto;
}

.tools-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 16px;
  padding: 0 10px 10px;
}

.tool-item {
  display: flex;
}

.tool-left {
  margin-right: auto;
}

.tool-center {
  margin-left: auto;
  margin-right: auto;
}

.tool-right {
  margin-left: auto;
}
</style>