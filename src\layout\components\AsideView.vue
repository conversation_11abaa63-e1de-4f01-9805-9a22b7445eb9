<template>
  <div class="aside-view">
    <!-- Logo -->
    <div class="aside-logo">
      <img class="logo-img" src="@/assets/logo.svg" alt="Logo" />
      <vxe-link v-if="!appStore.collapseAside" href="/" class="logo-title">
        Vxe 后台管理系统模板
      </vxe-link>
    </div>

    <!-- Menu -->
    <div class="aside-menu">
      <VxeMenu
        v-model="currRouteName"
        :options="menuOptions"
        collapse-fixed
        @click="handleMenuClick"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { useRoute, useRouter, onBeforeRouteUpdate } from 'vue-router'
import XEUtils from 'xe-utils'
import { useAppStore } from '@/stores/app'
import { useUserStore } from '@/stores/user'
import { useMenuStore } from '@/stores/menu'
import { routeToMenuName } from '@/utils'

const route = useRoute()
const router = useRouter()
const appStore = useAppStore()
const userStore = useUserStore()
const menuStore = useMenuStore()

const currRouteName = ref('')

// Menu options
const menuOptions = computed(() => {
  return [
    // 首页
    {
      name: 'dashboard',
      title: '首页',
      icon: 'home',
      iconClass: 'text-blue-500',
      path: '/dashboard'
    },
    // 示例菜单
    {
      name: 'example',
      title: '示例菜单',
      icon: 'edit',
      iconClass: 'text-gray-500',
      expanded: true,
      children: [
        {
          name: 'example1-list',
          title: '列表1',
          path: '/example1/list'
        },
        {
          name: 'example2-list',
          title: '列表2',
          path: '/example2/list'
        },
        {
          name: 'example3-list',
          title: '列表3',
          path: '/example3/list'
        },
        {
          name: 'example4-list',
          title: '列表4',
          path: '/example4/list'
        }
      ]
    },
    // 系统管理
    {
      name: 'system',
      title: '系统管理',
      icon: 'setting',
      iconClass: 'text-gray-500',
      expanded: true,
      children: [
        {
          name: 'User',
          title: '用户管理',
          path: '/system/user'
        },
        {
          name: 'Role',
          title: '角色管理',
          path: '/system/role'
        },
        {
          name: 'Route',
          title: '路由管理',
          path: '/system/route'
        },
        {
          name: 'Dict',
          title: '字典管理',
          path: '/system/dict'
        },
      ]
    },
    // 关于我们
    {
      name: 'about',
      title: '关于我们',
      icon: 'message',
      iconClass: 'text-gray-500',
      path: '/about'
    }
  ]
})

// Update selected menu based on current route
const updateSelectMenu = () => {
  currRouteName.value = routeToMenuName(route)
}

// Handle menu click
const handleMenuClick = ({ menu }) => {
  if (menu.path) {
    router.push(menu.path)
  }
}

// Update menu selection when route changes
onBeforeRouteUpdate(() => {
  setTimeout(() => updateSelectMenu())
})

watch(route, () => {
  updateSelectMenu()
})

// Initialize
updateSelectMenu()
</script>

<style scoped>
.aside-view {
  background-color: white;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.aside-logo {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.logo-img {
  width: 2rem;
  height: 2rem;
}

.logo-title {
  margin-left: 0.5rem;
  font-weight: bold;
  font-size: 1.125rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.aside-menu {
  flex: 1;
  margin-top: 0.5rem;
  overflow-y: auto;
  overflow-x: hidden;
}

/* Custom scrollbar */
.aside-menu::-webkit-scrollbar {
  width: 0.375rem;
}

.aside-menu::-webkit-scrollbar-track {
  background-color: #f3f4f6;
}

.aside-menu::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
  border-radius: 0.25rem;
}

.aside-menu::-webkit-scrollbar-thumb:hover {
  background-color: #9ca3af;
}
</style>
