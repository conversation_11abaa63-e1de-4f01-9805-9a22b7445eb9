// 请求响应参数（不包含data）
export interface IResult {
    code: string
    msg: string
  }
  
  // 请求响应参数（包含data）
  export interface IResultData<T> {
    code: string;
    msg: string;
    data: T;
  }
  
  export interface IPage<T = any> {
    pageNumber: number
    pageSize: number
    totalPage: number
    totalRow: number
    records: T[]
    param?: { [key: string]: any } | string
  }
  
  export interface IPageQuery {
    pageNumber: number
    pageSize: number
  }
  