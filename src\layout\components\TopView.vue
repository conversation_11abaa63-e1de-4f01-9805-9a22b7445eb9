<template>
  <div class="page-top">
    <!-- Tabs navigation -->
    <div class="page-nav">
      <vxe-tabs
        v-model="selectTab"
        type="round-card"
        :options="tabList"
        :show-close="tabList.length > 1"
        @tab-click="tabClickEvent"
        @tab-close="tabCloseEvent"
      >
        <template #extra>
          <vxe-pulldown
            :options="tabOptions"
            trigger="click"
            show-popup-shadow
            transfer
            @option-click="tabOptionClickEvent"
          >
            <template #default>
              <vxe-button mode="text" icon="vxe-icon-ellipsis-v"></vxe-button>
            </template>
          </vxe-pulldown>
        </template>
      </vxe-tabs>
    </div>

    <!-- Breadcrumb navigation -->
    <div class="page-breadcrumb">
      <div class="page-breadcrumb-left">
        <vxe-button mode="text" icon="vxe-icon-arrows-left" class="back-btn" @click="backEvent"></vxe-button>
        <vxe-button mode="text" icon="vxe-icon-refresh" class="refresh-btn" @click="refreshEvent"></vxe-button>
        <vxe-breadcrumb>
          <vxe-breadcrumb-item v-for="(item, index) in navList" :key="index">
            {{ item.title }}
          </vxe-breadcrumb-item>
        </vxe-breadcrumb>
      </div>
      <div class="page-breadcrumb-right">
        <slot name="breadcrumb-right"></slot>
      </div>
    </div>
  </div>
</template>

<script setup>
import XEUtils from 'xe-utils'
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAppStore } from '@/stores/app'
import { useUserStore } from '@/stores/user'
import { routeToMenuName } from '@/utils'

const route = useRoute()
const router = useRouter()
const appStore = useAppStore()
const userStore = useUserStore()

// Mock data for tabs
const mockTabs = [
  {
    name: 'dashboard',
    title: '首页',
    path: '/dashboard',
    routeName: 'Dashboard',
    query: {},
    params: {}
  }
]

// Mock data for menu maps
const mockMenuMaps = {
  dashboard: { title: '首页' },
  'example1-list': { title: '列表1' },
  'example2-list': { title: '列表2' },
  'User': { title: '用户管理' },
  'Role': { title: '角色管理' }
}

// Navigation breadcrumb list
const navList = computed(() => {
  const routeName = routeToMenuName(route)
  // In a real app, this would use userStore.menuAllTreeList
  // For now, we'll return a simple breadcrumb based on the current route
  const parts = route.path.split('/').filter(Boolean)
  return parts.map((part, index) => {
    const path = '/' + parts.slice(0, index + 1).join('/')
    return {
      title: mockMenuMaps[routeName]?.title || part,
      path
    }
  })
})

// Tab list
const tabList = computed(() => {
  // In a real app, this would use userStore.userTabs
  // For now, we'll use mock data
  return mockTabs.map(item => {
    const menuItem = mockMenuMaps[item.name]
    return {
      title: menuItem ? menuItem.title : item.name,
      name: item.name,
      path: item.path,
      routeLink: {
        name: item.routeName,
        query: item.query,
        params: item.params
      }
    }
  })
})

// Selected tab
const selectTab = ref('dashboard')

// Tab options
const tabOptions = ref([
  { label: '关闭其他页签', value: 'closeOther' },
  { label: '关闭左侧页签', value: 'closeLeft' },
  { label: '关闭右侧页签', value: 'closeRight' },
  { label: '重新加载', value: 'refresh' }
])

// Navigation events
const backEvent = () => {
  router.back()
}

const refreshEvent = () => {
  appStore.reloadPage()
}

// Tab events
const tabClickEvent = ({ name }) => {
  const item = tabList.value.find(item => item.name === name)
  if (item && item.path !== route.fullPath) {
    router.push(item.path)
  }
}

const tabCloseEvent = ({ name }) => {
  // In a real app, this would call userStore.removeUserTab
  // For now, we'll just handle the UI part
  const index = mockTabs.findIndex(tab => tab.name === name)
  if (index > -1) {
    mockTabs.splice(index, 1)

    // Navigate to the first tab if we closed the active one
    if (name === selectTab.value && mockTabs.length > 0) {
      selectTab.value = mockTabs[0].name
      router.push(mockTabs[0].path)
    }
  }
}

const tabOptionClickEvent = ({ option }) => {
  switch (option.value) {
    case 'closeOther':
      // Keep only the active tab
      const activeTab = mockTabs.find(tab => tab.name === selectTab.value)
      if (activeTab) {
        mockTabs.length = 0
        mockTabs.push(activeTab)
      }
      break
    case 'closeLeft':
      // Close tabs to the left of the active tab
      const leftIndex = mockTabs.findIndex(tab => tab.name === selectTab.value)
      if (leftIndex > 0) {
        mockTabs.splice(0, leftIndex)
      }
      break
    case 'closeRight':
      // Close tabs to the right of the active tab
      const rightIndex = mockTabs.findIndex(tab => tab.name === selectTab.value)
      if (rightIndex > -1 && rightIndex < mockTabs.length - 1) {
        mockTabs.splice(rightIndex + 1)
      }
      break
    case 'refresh':
      refreshEvent()
      break
  }
}
</script>

<style scoped>
.page-top {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  line-height: 2.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.page-nav {
  padding-top: 0.375rem;
  padding-right: 1rem;
}

.page-breadcrumb {
  display: flex;
}

.page-breadcrumb-left {
  flex-grow: 1;
}

.page-breadcrumb-right {
  padding-right: 1rem;
}

.back-btn,
.refresh-btn {
  vertical-align: middle;
  font-size: 1rem;
}

.back-btn {
  margin-left: 0.5rem;
}

.refresh-btn {
  margin-right: 0.75rem;
}

:deep(.vxe-tabs--round-card .vxe-tabs-header--item-wrapper) {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
</style>
