# 基础组件

## Icon 图标组件

### 基础用法
<vxe-icon name="print"></vxe-icon>

### 尺寸
<vxe-icon name="home" size="medium"></vxe-icon>

### 状态
<vxe-icon name="question-circle-fill" status="primary"></vxe-icon>

### 动画效果
<vxe-icon name="refresh" status="success" roll></vxe-icon>


## Text 文本组件

### 尺寸大小
<vxe-text size="medium">中等尺寸</vxe-text>

### 状态颜色
<vxe-text status="primary">主要颜色</vxe-text>

### 图标
<vxe-text icon="vxe-icon-warning-circle-fill" status="primary">文本</vxe-text>

### 点击复制内容
<vxe-text content="点击图标复制内容123" click-to-copy></vxe-text>


## Link链接

### 尺寸大小
<vxe-link size="medium">中等尺寸</vxe-link>

### 状态颜色
<vxe-link status="primary" content="主要颜色"></vxe-link>

### 图标
<vxe-link href="https://vxeui.com" icon="vxe-icon-file" status="primary">链接</vxe-link>

### 下划线
<vxe-link href="https://vxeui.com" status="primary">有下划线</vxe-link>

### 超链接
<vxe-link href="https://vxeui.com">当前页面跳转</vxe-link>

### 路由模式
<vxe-link :router-link="{name: 'StartInstall'}">点击跳转</vxe-link>

### 权限码
<vxe-link href="https://vxeui.com" target="_blank" permission-code="xxx">链接</vxe-link>


## Tag标签

### 尺寸大小
<vxe-tag size="medium">中等尺寸</vxe-tag>

### 状态
<vxe-tag status="primary" content="主要颜色"></vxe-tag>

### 图标
<vxe-tag status="primary" content="主要图标颜色" icon="vxe-icon-info-circle"></vxe-tag>


## Button按钮 

### 尺寸大小
<vxe-button mode="text" size="medium">中等尺寸</vxe-button>

### 状态
<vxe-button mode="text" status="primary" content="主要颜色"></vxe-button>

### 圆角边框
<vxe-button content="主要颜色" status="primary" round></vxe-button>

### 图标
<vxe-button mode="text" status="primary" icon="vxe-icon-delete"></vxe-button>

### 圆形
<vxe-button status="primary" icon="vxe-icon-save" circle></vxe-button>

### 前缀、后缀提示
<vxe-button :prefix-tooltip="{content: '提示内容111'}"></vxe-button>

### 加载中
<vxe-button mode="text" status="primary" content="主要颜色" loading></vxe-button>

### 按钮组
<vxe-button-group mode="text">
        <vxe-button content="文本按钮1"></vxe-button>
        <vxe-button content="文本按钮2"></vxe-button>
        <vxe-button content="文本按钮3"></vxe-button>
</vxe-button-group>

### 排版方式
<vxe-button-group>
        <vxe-button content="文本按钮1"></vxe-button>
        <vxe-button content="文本按钮2"></vxe-button>
        <vxe-button content="文本按钮3"></vxe-button>
</vxe-button-group>

### 下拉按钮

#### 下拉按钮
<vxe-button :options="downBtns" @dropdown-click="dropdownClickEvent">下拉按钮</vxe-button>

<script lang="ts" setup>
import { ref } from 'vue'
import { VxeUI, VxeButtonPropTypes, VxeButtonEvents } from 'vxe-pc-ui'

const downBtns = ref<VxeButtonPropTypes.Options>([
  { name: '1', content: '下拉按钮1' },
  { name: '2', content: '下拉按钮2' },
  { name: '3', content: '下拉按钮3' }
])

const dropdownClickEvent: VxeButtonEvents.DropdownClick = (params) => {
  VxeUI.modal.message({
    content: `点击了 ${params.name}`
  })
}
</script>


#### 状态颜色
<vxe-button status="primary" :options="downBtns">下拉按钮</vxe-button>

<script lang="ts" setup>
import { ref } from 'vue'
import { VxeButtonPropTypes } from 'vxe-pc-ui'

const downBtns = ref<VxeButtonPropTypes.Options>([
  { name: '1', content: '下拉按钮1', status: 'warning' },
  { name: '2', content: '下拉按钮2', status: 'error' },
  { name: '3', content: '下拉按钮3' }
])
</script>

#### 触发方式
<vxe-button trigger="hover" :options="downBtns">鼠标移触发</vxe-button>

<script lang="ts" setup>
import { ref } from 'vue'
import { VxeButtonPropTypes } from 'vxe-pc-ui'

const downBtns = ref<VxeButtonPropTypes.Options>([
  { name: '1', content: '下拉按钮1' },
  { name: '2', content: '下拉按钮2' },
  { name: '3', content: '下拉按钮3' }
])
</script>

#### 固定方向
<vxe-button :options="downBtns" placement="top">下拉按钮</vxe-button>

<script lang="ts" setup>
import { ref } from 'vue'
import { VxeButtonPropTypes } from 'vxe-pc-ui'

const downBtns = ref<VxeButtonPropTypes.Options>([
  { name: '1', content: '下拉按钮1' },
  { name: '2', content: '下拉按钮2' },
  { name: '3', content: '下拉按钮3' }
])
</script>

#### 下拉面板挂载 Body
<vxe-button :options="downBtns" transfer>下拉按钮</vxe-button>

<script lang="ts" setup>
import { ref } from 'vue'
import { VxeButtonPropTypes } from 'vxe-pc-ui'

const downBtns = ref<VxeButtonPropTypes.Options>([
  { name: '1', content: '下拉按钮1' },
  { name: '2', content: '下拉按钮2' },
  { name: '3', content: '下拉按钮3' }
])
</script>

##### 自定义插槽模板
<template>
  <div>
    <vxe-button @dropdown-click="dropdownClickEvent">
      <template #default>下拉按钮</template>
      <template #dropdowns>
        <vxe-button name="1" mode="text" content="下拉按钮1"></vxe-button>
        <vxe-button name="2" mode="text" content="下拉按钮2"></vxe-button>
        <vxe-button name="3" mode="text" content="下拉按钮3"></vxe-button>
      </template>
    </vxe-button>
  </div>
</template>

<script lang="ts" setup>
import { VxeUI, VxeButtonEvents } from 'vxe-pc-ui'

const dropdownClickEvent: VxeButtonEvents.DropdownClick = (params) => {
  VxeUI.modal.message({
    content: `点击了 ${params.name}`
  })
}
</script>

#### 权限码
<vxe-button permission-code="xxx" :options="downBtns">下拉按钮</vxe-button>

<script lang="ts" setup>
import { ref } from 'vue'
import { VxeButtonPropTypes } from 'vxe-pc-ui'

const downBtns = ref<VxeButtonPropTypes.Options>([
  { name: '1', content: '下拉按钮1', permissionCode: 'xxx' },
  { name: '2', content: '下拉按钮2', permissionCode: 'xxx' },
  { name: '3', content: '下拉按钮3', permissionCode: 'xxx' }
])
</script>

### 禁用
<vxe-button status="primary" content="主要禁用颜色" disabled></vxe-button>

### 权限码
<vxe-button mode="text" permission-code="xxx">文本按钮</vxe-button>

# 容器组件

## Layout页面布局

### 基础布局
<vxe-layout-container vertical>
  <vxe-layout-header class="bg1">
    <div style="height: 50px">头部</div>
  </vxe-layout-header>
  <vxe-layout-body class="bg3">
    <div style="height: 400px">内容</div>
    <div style="height: 400px">内容</div>
  </vxe-layout-body>
  <vxe-layout-footer class="bg4">
    <div style="height: 30px">底部</div>
  </vxe-layout-footer>
</vxe-layout-container>

### 固定头部和尾部
 <vxe-layout-container vertical>
  <vxe-layout-header fixed class="bg1">
    <div style="height: 50px">头部</div>
  </vxe-layout-header>
  <vxe-layout-body class="bg3">
    <div style="height: 400px">内容</div>
    <div style="height: 400px">内容</div>
  </vxe-layout-body>
  <vxe-layout-footer fixed class="bg4">
    <div style="height: 30px">底部</div>
  </vxe-layout-footer>
</vxe-layout-container>

### 侧边栏，内容区滚动条
<vxe-layout-container>
  <vxe-layout-aside class="bg2" :loading="loading">
    <vxe-button mode="text" @click="openLoading">点击加载中</vxe-button>
    <div style="height: 400px">菜单</div>
    <div style="height: 400px">菜单</div>
  </vxe-layout-aside>
  <vxe-layout-container vertical>
    <vxe-layout-header fixed class="bg1">
      <div style="height: 50px">头部</div>
    </vxe-layout-header>
    <vxe-layout-body class="bg3">
      <div style="height: 400px">内容</div>
      <div style="height: 400px">内容</div>
    </vxe-layout-body>
    <vxe-layout-footer class="bg4">
      <div style="height: 30px">底部</div>
    </vxe-layout-footer>
  </vxe-layout-container>
</vxe-layout-container>

### 头部固定，中间内容区滚动
<vxe-layout-container vertical>
  <vxe-layout-header class="bg1">
    <div style="height: 50px">头部</div>
  </vxe-layout-header>
  <vxe-layout-container>
    <vxe-layout-aside class="bg2">
      <div style="height: 400px">菜单</div>
      <div style="height: 400px">菜单</div>
    </vxe-layout-aside>
	  <vxe-layout-container vertical>
	    <vxe-layout-body class="bg3">
	      <div style="height: 400px">内容</div>
	      <div style="height: 400px">内容</div>
	    </vxe-layout-body>
	    <vxe-layout-footer class="bg4">
	      <div style="height: 30px">底部</div>
	    </vxe-layout-footer>
	  </vxe-layout-container>
	</vxe-layout-container>
</vxe-layout-container>

### 左侧与内容区滚动
<vxe-layout-container vertical>
  <vxe-layout-header class="bg1">
    <div style="height: 50px">头部</div>
  </vxe-layout-header>
  <vxe-layout-container>
    <vxe-layout-aside class="bg2">
      <div style="height: 400px">菜单</div>
      <div style="height: 400px">菜单</div>
    </vxe-layout-aside>
    <vxe-layout-container vertical>
      <vxe-layout-body class="bg3" :loading="loading">
        <vxe-button mode="text" @click="openLoading">点击加载中</vxe-button>
        <div style="height: 400px">内容</div>
        <div style="height: 400px">内容</div>
      </vxe-layout-body>
    </vxe-layout-container>
  </vxe-layout-container>
  <vxe-layout-footer class="bg4">
    <div style="height: 30px">底部</div>
  </vxe-layout-footer>
</vxe-layout-container>

## Row 行与列

### 占比
<vxe-row gutter="10">
  <vxe-col>
    <div class="mybg">内容1</div>
  </vxe-col>
  <vxe-col>
    <div class="mybg">内容2</div>
  </vxe-col>
</vxe-row>

### 垂直布局
<vxe-row gutter="10" vertical>
  <vxe-col span="8">
    <div class="mybg">占比8</div>
  </vxe-col>
  <vxe-col span="12">
    <div class="mybg">占比12</div>
  </vxe-col>
</vxe-row>

### 自动换行
<vxe-row :gutter="[10, 10]" wrap>
  <vxe-col span="8">
    <div class="mybg">占比8</div>
  </vxe-col>
  <vxe-col span="8">
    <div class="mybg">占比8</div>
  </vxe-col>
  <vxe-col span="8">
    <div class="mybg">占比8</div>
  </vxe-col>
  <vxe-col span="8">
    <div class="mybg">占比8</div>
  </vxe-col>
</vxe-row>

### 间距
<vxe-row gutter="10">
  <vxe-col span="8">
    <div class="mybg">占比8</div>
  </vxe-col>
  <vxe-col span="8">
    <div class="mybg">占比8</div>
  </vxe-col>
</vxe-row>

### 固定宽度
<vxe-row gutter="10">
  <vxe-col width="100">
    <div class="mybg">宽度100</div>
  </vxe-col>
</vxe-row>

### 铺满宽度
<vxe-row gutter="10">
  <vxe-col fill>
    <div class="mybg">铺满剩余宽度</div>
  </vxe-col>
</vxe-row>

### 对齐方式
<vxe-row gutter="10">
  <vxe-col span="8" align="left">
    <div class="mybg">居左</div>
  </vxe-col>
</vxe-row>

### 溢出隐藏
<vxe-row gutter="10">
  <vxe-col span="4" ellipsis>
    <span>超出隐藏超出隐藏超出隐藏超出隐藏超出隐藏超出隐藏超出隐藏超出隐藏</span>
  </vxe-col>
</vxe-row>

## Card 卡片

### 基础
<vxe-card title="标题">
  <div>内容内容内容1</div>
  <div>内容内容内容2</div>
</vxe-card>

### 宽度
<vxe-card title="标题" :width="300">
  <div>内容1</div>
  <div>内容2</div>
</vxe-card>

### 高度
<vxe-card title="标题" :height="200" :width="300">
  <div>内容1</div>
  <div>内容2</div>
  <div>内容3</div>
  <div>内容4</div>
  <div>内容5</div>
  <div>内容6</div>
  <div>内容8</div>
  <div>内容9</div>
  <div>内容10</div>
</vxe-card>

### 加载中
<vxe-card title="标题" :loading="loading" :height="200" :width="300"></vxe-card>

### 显示/隐藏边框
<vxe-card title="标题" :border="false">
	<vxe-image src="https://vxeui.com/resource/img/fj843.jpg" width="300"></vxe-image>
</vxe-card>

### 显示/隐藏边距
<vxe-card title="标题" :padding="false">
  <vxe-image src="https://vxeui.com/resource/img/fj567.jpeg" width="300"></vxe-image>
</vxe-card>

### 边框阴影
<vxe-card title="标题" shadow>
  <vxe-image src="https://vxeui.com/resource/img/fj573.jpeg" width="300"></vxe-image>
</vxe-card>

### 自定义模板
<template>
  <div>
    <p>
      <vxe-card title="标题" :height="200">
        <template #title>
          <div class="bg1">标题</div>
        </template>
        <template #extra>
          <div class="bg3">右上角</div>
        </template>
        <template #left>
          <div class="bg3">内容左侧区1</div>
          <div class="bg3">内容左侧区2</div>
          <div class="bg3">内容左侧区3</div>
          <div class="bg3">内容左侧区4</div>
          <div class="bg3">内容左侧区5</div>
          <div class="bg3">内容左侧区6</div>
        </template>
        <template #default>
          <div class="bg3">内容区1</div>
          <div class="bg3">内容区2</div>
          <div class="bg3">内容区3</div>
          <div class="bg3">内容区4</div>
          <div class="bg3">内容区5</div>
          <div class="bg3">内容区6</div>
          <div class="bg3">内容区7</div>
        </template>
        <template #right>
          <div class="bg3">内容右侧区1</div>
          <div class="bg3">内容右侧区2</div>
          <div class="bg3">内容右侧区3</div>
          <div class="bg3">内容右侧区4</div>
          <div class="bg3">内容右侧区5</div>
          <div class="bg3">内容右侧区6</div>
          <div class="bg3">内容右侧区7</div>
          <div class="bg3">内容右侧区8</div>
        </template>
        <template #footer>
          <div class="bg4">底部区</div>
        </template>
      </vxe-card>
    </p>
  </div>
</template>

<style lang="scss" scoped>
.page-wrapper {
  position: relative;
  height: 100px;
  margin: 20px 0;
}
.bg1 {
  background-color: #a6c9ed;
}
.bg2 {
  background-color: #c0dcf7;
}
.bg3 {
  background-color: #dfedfb;
}
.bg4 {
  background-color: #a6c9ed;
}
</style>

### 实现可刷新
<template>
  <div>
    <p>
      <vxe-card title="标题" :loading="loading" :height="200" :width="300">
        <template #extra>
          <vxe-button mode="text" icon="vxe-icon-refresh" @click="loadData()"></vxe-button>
        </template>

        <template #default>
          <div>内容1</div>
          <div>内容2</div>
          <div>内容3</div>
        </template>
      </vxe-card>
    </p>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'

const loading = ref(false)

const loadData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
  }, 500)
}

loadData()
</script>


## Carousel 轮播图

### 基础
<vxe-carousel>
  <vxe-carousel-item name="1" url="https://vxeui.com/resource/img/fj577.jpg"></vxe-carousel-item>
  <vxe-carousel-item name="2" url="https://vxeui.com/resource/img/fj581.jpeg"></vxe-carousel-item>
</vxe-carousel>

### 高度
<vxe-carousel height="400">
  <vxe-carousel-item name="1" url="https://vxeui.com/resource/img/fj577.jpg"></vxe-carousel-item>
  <vxe-carousel-item name="2" url="https://vxeui.com/resource/img/fj581.jpeg"></vxe-carousel-item>
</vxe-carousel>

### 纵向排版
<vxe-carousel height="400" auto-play vertical>
  <vxe-carousel-item name="1" url="https://vxeui.com/resource/img/fj577.jpg"></vxe-carousel-item>
  <vxe-carousel-item name="2" url="https://vxeui.com/resource/img/fj581.jpeg"></vxe-carousel-item>
</vxe-carousel>

### 配置式
<vxe-carousel :options="imgList" height="400" auto-play></vxe-carousel>

<script lang="ts" setup>
import { ref } from 'vue'
import { VxeCarouselPropTypes } from 'vxe-pc-ui'

const imgList = ref<VxeCarouselPropTypes.Options>([
  { name: '1', url: 'https://vxeui.com/resource/img/fj577.jpg' },
  { name: '2', url: 'https://vxeui.com/resource/img/fj581.jpeg' },
  { name: '3', url: 'https://vxeui.com/resource/img/fj573.jpeg' }
])
</script>

### 自定义模板
<template>
  <div>
    <vxe-carousel height="400">
      <vxe-carousel-item name="1">
        <img class="carousel-img" src="https://vxeui.com/resource/img/fj577.jpg">
      </vxe-carousel-item>
      <vxe-carousel-item name="2">
        <img class="carousel-img" src="https://vxeui.com/resource/img/fj581.jpeg">
      </vxe-carousel-item>
      <vxe-carousel-item name="3">
        <img class="carousel-img" src="https://vxeui.com/resource/img/fj573.jpeg">
      </vxe-carousel-item>
    </vxe-carousel>
  </div>
</template>

<style lang="scss" scoped>
.carousel-img {
  width: 100%;
}
</style>

### 自动切换
<vxe-carousel height="400" auto-play :interval="5000">
  <vxe-carousel-item name="1" url="https://vxeui.com/resource/img/fj577.jpg"></vxe-carousel-item>
  <vxe-carousel-item name="2" url="https://vxeui.com/resource/img/fj581.jpeg"></vxe-carousel-item>
</vxe-carousel>

### 指示器
<vxe-carousel height="400" auto-play show-indicators>
  <vxe-carousel-item name="1">
    <img class="carousel-img" src="https://vxeui.com/resource/img/fj577.jpg">
  </vxe-carousel-item>
  <vxe-carousel-item name="2">
    <img class="carousel-img" src="https://vxeui.com/resource/img/fj581.jpeg">
  </vxe-carousel-item>
</vxe-carousel>

## Collapse 折叠面板

### 基础
<vxe-collapse>
  <vxe-collapse-pane title="标题1" name="1">内容1</vxe-collapse-pane>
  <vxe-collapse-pane title="标题2" name="2">内容2</vxe-collapse-pane>
</vxe-collapse>

### 图标
<vxe-collapse>
  <vxe-collapse-pane title="标题1" name="1" icon="vxe-icon-home">内容1</vxe-collapse-pane>
  <vxe-collapse-pane title="标题2" name="2">内容2</vxe-collapse-pane>
</vxe-collapse>

### 配置式
<vxe-collapse :options="itemList"></vxe-collapse>

<script lang="ts" setup>
import { ref } from 'vue'
import { VxeCollapsePropTypes } from 'vxe-pc-ui'

const itemList = ref<VxeCollapsePropTypes.Options>([
  { name: '1', title: '标题1', slots: { default: 'default1' } },
  { name: '2', title: '标题2', slots: { default: 'default2' } },
  { name: '3', title: '标题3', slots: { default: 'default3' } }
])
</script>

### 自定义标题模板
<template>
  <div>
    <vxe-collapse>
      <vxe-collapse-pane name="1">
        <template #title>
          <div>标题1</div>
        </template>

        <template #default>
          <div>内容1</div>
        </template>
      </vxe-collapse-pane>
      <vxe-collapse-pane name="2">
        <template #title>
          <div>标题2</div>
        </template>

        <template #default>
          <div>内容2</div>
        </template>
      </vxe-collapse-pane>
      <vxe-collapse-pane name="3">
        <template #title>
          <div>标题3</div>
        </template>

        <template #default>
          <div>内容3</div>
        </template>
      </vxe-collapse-pane>
      <vxe-collapse-pane name="4">
        <template #title>
          <div>标题4</div>
        </template>

        <template #default>
          <div>内容4</div>
        </template>
      </vxe-collapse-pane>
      <vxe-collapse-pane name="5">
        <template #title>
          <div>标题5</div>
        </template>

        <template #default>
          <div>内容5</div>
        </template>
      </vxe-collapse-pane>
    </vxe-collapse>
  </div>
</template>

### 自定义内容模板
<template>
  <div>
    <vxe-collapse>
      <vxe-collapse-pane title="标题1" name="1">
        <template #default>
          <div>内容1</div>
        </template>
      </vxe-collapse-pane>
      <vxe-collapse-pane title="标题2" name="2">
        <template #default>
          <div>内容2</div>
        </template>
      </vxe-collapse-pane>
      <vxe-collapse-pane title="标题3" name="3">
        <template #default>
          <div>内容3</div>
        </template>
      </vxe-collapse-pane>
      <vxe-collapse-pane title="标题4" name="4">
        <template #default>
          <div>内容4</div>
        </template>
      </vxe-collapse-pane>
      <vxe-collapse-pane title="标题5" name="5">
        <template #default>
          <div>内容5</div>
        </template>
      </vxe-collapse-pane>
    </vxe-collapse>
  </div>
</template>

## Split 分割面板

### 基础

#### 基础
<vxe-split>
  <vxe-split-pane>
    <div style="background-color: #f3e1e1;">左侧</div>
  </vxe-split-pane>
</vxe-split>

#### 高度
<vxe-split height="300">
  <vxe-split-pane>
    <div style="height: 600px;background-color: #f3e1e1;">左侧</div>
  </vxe-split-pane>
</vxe-split>

#### 边框
<vxe-split height="300" border>
  <vxe-split-pane>
    <div style="height: 100%;background-color: #f3e1e1;">左侧</div>
  </vxe-split-pane>
</vxe-split>

#### 边距
<vxe-split height="300" border padding>
  <vxe-split-pane>
    <div style="height: 100%;background-color: #f3e1e1;">左侧</div>
  </vxe-split-pane>
  <vxe-split-pane>
    <div style="height: 100%;background-color: #d8d8f9;">右侧</div>
  </vxe-split-pane>
</vxe-split>

#### 垂直布局
<vxe-split-pane>
    <div style="height: 200px;background-color: #f3e1e1;">顶部</div>
  </vxe-split-pane>
  <vxe-split-pane>
    <div style="height: 400px;background-color: #d8d8f9;">底部</div>
  </vxe-split-pane>
</vxe-split>

#### 折叠按钮
<vxe-split height="300" border>
  <vxe-split-pane width="100">
    <div style="height: 100%;background-color: #f3e1e1;">左侧</div>
  </vxe-split-pane>
  <vxe-split-pane show-action>
    <div style="height: 600px;background-color: #d8d8f9;">右侧</div>
  </vxe-split-pane>
</vxe-split>

#### 组合布局
<vxe-split height="300" border>
  <vxe-split-pane>
    <vxe-split border vertical>
      <vxe-split-pane height="100">
        <div style="height: 100%;background-color: #f3e1e1;">顶部</div>
      </vxe-split-pane>
      <vxe-split-pane>
        <div style="height: 100%;background-color: #d8d8f9;">底部</div>
      </vxe-split-pane>
    </vxe-split>
  </vxe-split-pane>
  <vxe-split-pane width="200">
    <div style="height: 100%;background-color: #f3d0ec;">右侧</div>
  </vxe-split-pane>
</vxe-split>

### 配置式

#### 基础
<vxe-split v-bind="splitOptions">
  <template #leftContent>
    <div style="background-color: #f3e1e1;">左</div>
  </template>
</vxe-split>

<script lang="ts" setup>
import { reactive } from 'vue'
import { VxeSplitProps } from 'vxe-pc-ui'

const splitOptions = reactive<VxeSplitProps>({
  items: [
    { width: 100, slots: { default: 'leftContent' } }
  ]
})
</script>

#### 高度
<vxe-split v-bind="splitOptions">
  <template #leftContent>
    <div style="height: 600px;background-color: #f3e1e1;">左侧</div>
  </template>
</vxe-split>

<script lang="ts" setup>
import { reactive } from 'vue'
import { VxeSplitProps } from 'vxe-pc-ui'

const splitOptions = reactive<VxeSplitProps>({
  height: 300,
  items: [
    { slots: { default: 'leftContent' } },
  ]
})
</script>

#### 边框
<vxe-split v-bind="splitOptions">
  <template #leftContent>
    <div style="height: 100%;background-color: #f3e1e1;">左侧</div>
  </template>
  <template #rightContent>
    <div style="height: 100%;background-color: #d8d8f9;">右侧</div>
  </template>
</vxe-split>

<script lang="ts" setup>
import { reactive } from 'vue'
import { VxeSplitProps } from 'vxe-pc-ui'

const splitOptions = reactive<VxeSplitProps>({
  height: 300,
  border: true,
  items: [
    { slots: { default: 'leftContent' } },
    { slots: { default: 'rightContent' } }
  ]
})
</script>

#### 边距
<vxe-split v-bind="splitOptions">
  <template #leftContent>
    <div style="height: 100%;background-color: #f3e1e1;">左侧</div>
  </template>
  <template #rightContent>
    <div style="height: 100%;background-color: #d8d8f9;">右侧</div>
  </template>
</vxe-split>

const splitOptions = reactive<VxeSplitProps>({
  height: 300,
  border: true,
  padding: true,
  items: [
    { slots: { default: 'leftContent' } },
    { slots: { default: 'rightContent' } }
  ]
})

#### 垂直布局
<vxe-split v-bind="splitOptions">
  <template #topContent>
    <div style="height: 200px;background-color: #f3e1e1;">顶部</div>
  </template>
  <template #bottomContent>
    <div style="height: 400px;background-color: #d8d8f9;">底部</div>
  </template>
</vxe-split>

<script lang="ts" setup>
import { reactive } from 'vue'
import { VxeSplitProps } from 'vxe-pc-ui'

const splitOptions = reactive<VxeSplitProps>({
  height: 300,
  border: true,
  vertical: true,
  items: [
    { slots: { default: 'topContent' } },
    { slots: { default: 'bottomContent' } }
  ]
})
</script>

#### 折叠按钮
<vxe-split v-bind="splitOptions">
  <template #leftContent>
    <div style="height: 100%;background-color: #f3e1e1;">左侧</div>
  </template>
  <template #rightContent>
    <div style="height: 600px;background-color: #d8d8f9;">右侧</div>
  </template>
</vxe-split>
    
<script lang="ts" setup>
import { reactive } from 'vue'
import { VxeSplitProps } from 'vxe-pc-ui'

const splitOptions = reactive<VxeSplitProps>({
  height: 300,
  border: true,
  items: [
    { width: 100, slots: { default: 'leftContent' } },
    { showAction: true, slots: { default: 'rightContent' } }
  ]
})
</script>

#### 组合布局
<vxe-split v-bind="splitOptions1">
  <template #leftContent>
    <vxe-split v-bind="splitOptions2">
      <template #topContent>
        <div style="height: 100%;background-color: #f3e1e1;">顶部</div>
      </template>
      <template #bottomContent>
        <div style="height: 100%;background-color: #d8d8f9;">底部</div>
      </template>
    </vxe-split>
  </template>
  <template #rightContent>
    <div style="height: 100%;background-color: #f3d0ec;">右侧</div>
  </template>
</vxe-split>

<script lang="ts" setup>
import { reactive } from 'vue'
import { VxeSplitProps } from 'vxe-pc-ui'

const splitOptions1 = reactive<VxeSplitProps>({
  height: 300,
  border: true,
  items: [
    { slots: { default: 'leftContent' } },
    { width: 200, slots: { default: 'rightContent' } }
  ]
})

const splitOptions2 = reactive<VxeSplitProps>({
  border: true,
  vertical: true,
  items: [
    { width: 100, slots: { default: 'topContent' } },
    { slots: { default: 'bottomContent' } }
  ]
})
</script>

## Tabs 标签

### 基础
<vxe-tabs>
  <vxe-tab-pane title="标题1" name="1">内容1</vxe-tab-pane>
  <vxe-tab-pane title="标题2" name="2">内容2</vxe-tab-pane>
</vxe-tabs>

### 卡片风格
<vxe-tabs type="card">
  <vxe-tab-pane title="标题1" name="1">内容1</vxe-tab-pane>
  <vxe-tab-pane title="标题2" name="2">内容2</vxe-tab-pane>
</vxe-tabs>

### 边框背景的卡片
<vxe-tabs type="border-card">
  <vxe-tab-pane title="标题1" name="1">内容1</vxe-tab-pane>
  <vxe-tab-pane title="标题2" name="2">内容2</vxe-tab-pane>
</vxe-tabs>

### 设置高度
<vxe-tabs type="border-card" :height="140" padding>
  <vxe-tab-pane title="标题1" name="1">
    <div>内容1</div>
    <div>内容1</div>
    <div>内容1</div>
    <div>内容1</div>
    <div>内容1</div>
    <div>内容1</div>
  </vxe-tab-pane>
</vxe-tabs>

### 圆角边框背景的卡片
<vxe-tabs v-model="selectTab" type="round-card" :height="140">
  <vxe-tab-pane title="标题1" name="1">
    <div>内容1</div>
    <div>内容1</div>
    <div>内容1</div>
    <div>内容1</div>
    <div>内容1</div>
    <div>内容1</div>
  </vxe-tab-pane>
</vxe-tabs>

### 配置式
<vxe-tabs :height="140" :options="tabList">
  <template #default1>
    <div>内容1</div>
    <div>内容1</div>
  </template>
</vxe-tabs>

<script lang="ts" setup>
import { ref } from 'vue'
import { VxeTabsPropTypes } from 'vxe-pc-ui'

const tabList = ref<VxeTabsPropTypes.Options>([
  { name: '1', title: '标题1', slots: { default: 'default1' } }
])
</script>

### 页签图标
<vxe-tabs :height="140" :options="tabList">
  <template #default1>
    <div>内容1</div>
    <div>内容1</div>
    <div>内容1</div>
    <div>内容1</div>
    <div>内容1</div>
    <div>内容1</div>
  </template>
</vxe-tabs>

<script lang="ts" setup>
import { ref } from 'vue'
import { VxeTabsPropTypes } from 'vxe-pc-ui'

const tabList = ref<VxeTabsPropTypes.Options>([
  { name: '1', title: '标题1', icon: 'vxe-icon-home', slots: { default: 'default1' } }
])
</script>

### 页签触发方式
<vxe-tabs ref="tabsRef" v-model="selectTab" :height="140" :options="tabList" trigger="manual">
  <template #default1>
    <div>内容1</div>
    <div>内容1</div>
  </template>
  <template #default2>
    <div>内容2</div>
    <div>内容2</div>
  </template>
</vxe-tabs>

<vxe-button status="primary" :disabled="selectTab === '1'" @click="prevEvent">上一步</vxe-button>
<vxe-button status="primary" :disabled="selectTab === '4'" @click="nextEvent">下一步</vxe-button>

<script lang="ts" setup>
import { ref } from 'vue'
import { VxeTabsPropTypes, VxeTabsInstance } from 'vxe-pc-ui'

const tabsRef = ref<VxeTabsInstance>()

const selectTab = ref('1')

const tabList = ref<VxeTabsPropTypes.Options>([
  { name: '1', title: '标题1', slots: { default: 'default1' } },
])

const prevEvent = () => {
  const $tabs = tabsRef.value
  if ($tabs) {
    $tabs.prev()
  }
}

const nextEvent = () => {
  const $tabs = tabsRef.value
  if ($tabs) {
    $tabs.next()
  }
}
</script>

### 预加载页签
<vxe-tabs v-model="selectTab" :height="140" padding>
  <vxe-tab-pane title="标题1" name="1">
        <div>内容1</div>
        <div>内容1</div>
        <div>内容1</div>
        <div>内容1</div>
        <div>内容1</div>
        <div>内容1</div>
  </vxe-tab-pane>
  <vxe-tab-pane title="标题2" name="2" preload>
    <div>无需激活页签，默认也会被预加载出来</div>
    <div>内容2</div>
    <div>内容2</div>
    <div>内容2</div>
    <div>内容2</div>
    <div>内容2</div>
  </vxe-tab-pane>
</vxe-tabs>

<script lang="ts" setup>
import { ref } from 'vue'

const selectTab = ref('1')
</script>

### 页签超出可滚动
<vxe-tabs v-model="selectTab" :options="tabList"></vxe-tabs>

<script lang="ts" setup>
import { ref } from 'vue'
import { VxeTabsPropTypes } from 'vxe-pc-ui'

const selectTab = ref('1')

const tabList = ref<VxeTabsPropTypes.Options>([
  { name: '1', title: '标题1标题1标题1' },
  { name: '2', title: '标题2' },
  { name: '3', title: '标题3标题3标题3' },
  { name: '4', title: '标题4标题4' },
  { name: '5', title: '标题5标题5标题5' },
  { name: '6', title: '标题6标题6标题6标题6标题6标题6' },
  { name: '7', title: '标题7标题7标题7' },
  { name: '8', title: '标题8标题8' },
  { name: '9', title: '标题9标题9标题9' },
  { name: '10', title: '标题10标题10标题10标题10' },
  { name: '11', title: '标题11标题11标题11' },
  { name: '12', title: '标题12标' },
  { name: '13', title: '标题13标题13标题13' },
  { name: '14', title: '标题14标题14' },
  { name: '15', title: '标题15标题15标题15' },
  { name: '16', title: '标题16标题16标题16标题16' }
])
</script>

### 右侧操作按钮
<vxe-tabs v-model="selectTab" :options="tabList">
  <template #extra>
    <vxe-pulldown :options="tabOptions" trigger="click" show-popup-shadow transfer
      @option-click="tabOptionClickEvent">
      <template #default>
        <vxe-button mode="text" icon="vxe-icon-ellipsis-v"></vxe-button>
      </template>
    </vxe-pulldown>
  </template>
</vxe-tabs>

<script lang="ts" setup>
import XEUtils from 'xe-utils'
import { ref } from 'vue'
import { VxeUI, VxeTabsPropTypes } from 'vxe-pc-ui'

const selectTab = ref('1')

const tabList = ref<VxeTabsPropTypes.Options>([
  { name: '1', title: '标题1标题1标题1' },
  { name: '2', title: '标题2' },
  { name: '3', title: '标题3标题3标题3' },
  { name: '4', title: '标题4标题4' },
  { name: '5', title: '标题5标题5标题5' },
  { name: '6', title: '标题6标题6标题6标题6标题6标题6' },
  { name: '7', title: '标题7标题7标题7' },
  { name: '8', title: '标题8标题8' },
  { name: '9', title: '标题9标题9标题9' },
  { name: '10', title: '标题10标题10标题10标题10' },
  { name: '11', title: '标题11标题11标题11' },
  { name: '12', title: '标题12标' },
  { name: '13', title: '标题13标题13标题13' },
  { name: '14', title: '标题14标题14' },
  { name: '15', title: '标题15标题15标题15' },
  { name: '16', title: '标题16标题16标题16标题16' }
])

const tabOptions = ref([
  { label: '关闭其他页签', value: 'closeOther' },
  { label: '关闭左侧页签', value: 'closeLeft' },
  { label: '关闭右侧页签', value: 'closeRight' },
  { label: '刷新页面', value: 'refresh' }
])

const tabOptionClickEvent = ({ option }) => {
  const index = XEUtils.findIndexOf(tabList.value, item => item.name === selectTab.value)
  switch (option.value) {
    case 'closeOther':
      tabList.value = tabList.value.filter(item => item.name === selectTab.value)
      break
    case 'closeLeft':
      tabList.value = tabList.value.slice(index)
      break
    case 'closeRight':
      tabList.value = tabList.value.slice(0, index + 1)
      break
    case 'refresh':
      VxeUI.modal.message({
        content: '刷新页面',
        status: 'success'
      })
      break
  }
}
</script>

### 拦截页签切换
<vxe-tabs
  v-model="selectTab"
  type="round-card"
  :height="140"
  :options="tabList"
  :before-change-method="beforeChangeMethod"
  @tab-change="tabChangeEvent"
  @tab-change-fail="tabChangeFailEvent">
  <template #default1>
    <div>内容1</div>
    <div>内容1</div>
    <div>内容1</div>
    <div>内容1</div>
    <div>内容1</div>
    <div>内容1</div>
  </template>
  <template #default2>
    <div>内容2</div>
    <div>内容2</div>
    <div>内容2</div>
    <div>内容2</div>
    <div>内容2</div>
    <div>内容2</div>
  </template>
</vxe-tabs>

<script lang="ts" setup>
import { ref } from 'vue'
import { VxeUI, VxeTabsPropTypes, VxeTabsEvents } from 'vxe-pc-ui'

const selectTab = ref<VxeTabsPropTypes.ModelValue>('1')

const tabList = ref<VxeTabsPropTypes.Options>([
  { name: '1', title: '标题1', slots: { default: 'default1' } },
  { name: '2', title: '标题2', slots: { default: 'default2' } }
])

const beforeChangeMethod: VxeTabsPropTypes.BeforeChangeMethod = () => {
  // 支持同步或异步
  return VxeUI.modal.confirm({
    content: '请确认是否关闭？'
  }).then(type => {
    if (type === 'confirm') {
      return true
    }
    return false
  })
}

const tabChangeEvent: VxeTabsEvents.TabChange = ({ name }) => {
  VxeUI.modal.message({
    content: `已切换到新页签 name=${name}`,
    status: 'success'
  })
}

const tabChangeFailEvent: VxeTabsEvents.TabChangeFail = ({ name }) => {
  VxeUI.modal.message({
    content: `阻止切换 ${name}`,
    status: 'warning'
  })
}
</script>

### 可关闭
<vxe-tabs
  v-model="selectTab"
  type="round-card"
  :height="140"
  :options="tabList"
  :close-config="closeConfig"
  @tab-close="tabCloseEvent">
  <template #default1>
    <div>内容1</div>
    <div>内容1</div>
    <div>内容1</div>
    <div>内容1</div>
    <div>内容1</div>
    <div>内容1</div>
  </template>
</vxe-tabs>

<script lang="ts" setup>
import { ref } from 'vue'
import { VxeTabsPropTypes, VxeTabsEvents } from 'vxe-pc-ui'

const selectTab = ref<VxeTabsPropTypes.ModelValue>('1')

const tabList = ref<VxeTabsPropTypes.Options>([
  { name: '1', title: '标题1', slots: { default: 'default1' } }
])

const closeConfig = ref<VxeTabsPropTypes.CloseConfig>({
  enabled: true
})

const tabCloseEvent: VxeTabsEvents.TabClose = ({ name, nextName }) => {
  tabList.value = tabList.value.filter(item => item.name !== name)
  selectTab.value = nextName
}
</script>

### 拦截页签关闭
<vxe-tabs
  v-model="selectTab"
  type="round-card"
  :height="140"
  :options="tabList"
  :close-config="closeConfig"
  @tab-close="tabCloseEvent"
  @tab-close-fail="tabCloseFailEvent">
  <template #default1>
    <div>内容1</div>
    <div>内容1</div>
    <div>内容1</div>
    <div>内容1</div>
    <div>内容1</div>
    <div>内容1</div>
  </template>
</vxe-tabs>

<script lang="ts" setup>
import { ref } from 'vue'
import { VxeUI, VxeTabsPropTypes, VxeTabsEvents } from 'vxe-pc-ui'

const selectTab = ref<VxeTabsPropTypes.ModelValue>('1')

const tabList = ref<VxeTabsPropTypes.Options>([
  { name: '1', title: '标题1', slots: { default: 'default1' } }
])

const closeConfig = ref<VxeTabsPropTypes.CloseConfig>({
  enabled: true,
  beforeMethod () {
    // 支持同步或异步
    return VxeUI.modal.confirm({
      content: '请确认是否关闭？'
    }).then(type => {
      if (type === 'confirm') {
        return true
      }
      return false
    })
  }
})

const tabCloseEvent: VxeTabsEvents.TabClose = ({ name, nextName }) => {
  VxeUI.modal.message({
    content: '已关闭',
    status: 'success'
  })
  tabList.value = tabList.value.filter(item => item.name !== name)
  selectTab.value = nextName
}

const tabCloseFailEvent: VxeTabsEvents.TabCloseFail = () => {
  VxeUI.modal.message({
    content: '阻止关闭',
    status: 'warning'
  })
}
</script>

### 可刷新
<vxe-tabs
  v-model="selectTab"
  type="round-card"
  :height="140"
  :options="tabList"
  :refresh-config="refreshConfig"
  @tabClose="tabCloseEvent">
  <template #default1>
    <div>内容1</div>
    <div>内容1</div>
    <div>内容1</div>
    <div>内容1</div>
    <div>内容1</div>
    <div>内容1</div>
  </template>
</vxe-tabs>

<script lang="ts" setup>
import { ref } from 'vue'
import { VxeTabsPropTypes, VxeTabsEvents } from 'vxe-pc-ui'

const selectTab = ref<VxeTabsPropTypes.ModelValue>('1')

const tabList = ref<VxeTabsPropTypes.Options>([
  { name: '1', title: '标题1', slots: { default: 'default1' } }
])

const refreshConfig = ref<VxeTabsPropTypes.RefreshConfig>({
  enabled: true,
  queryMethod () {
    // 模拟后台接口
    return new Promise<void>(resolve => {
      setTimeout(() => {
        resolve()
      }, 500)
    })
  }
})

const tabCloseEvent: VxeTabsEvents.TabClose = ({ name, nextName }) => {
  tabList.value = tabList.value.filter(item => item.name !== name)
  selectTab.value = nextName
}
</script>

### 权限码
<vxe-tabs v-model="selectTab">
  <vxe-tab-pane title="标题1" name="1" permission-code="xxx">内容1</vxe-tab-pane>
</vxe-tabs>

## Pulldown 下拉容器

### 基础
<vxe-pulldown v-model="showPull" @visible-change="visibleChangeEvent">
  <template #default>
    <vxe-button mode="text" icon="vxe-icon-arrow-down" @click="toggleEvent">下拉菜单</vxe-button>
  </template>
  <template #dropdown>
    <div>
      <vxe-link class="my-dropdown-link" @click="clickEvent(1)">菜单1</vxe-link>
      <vxe-link class="my-dropdown-link" @click="clickEvent(2)">菜单2</vxe-link>
      <vxe-link class="my-dropdown-link" @click="clickEvent(3)">菜单3</vxe-link>
      <vxe-link class="my-dropdown-link" @click="clickEvent(4)">菜单4</vxe-link>
      <vxe-link class="my-dropdown-link" @click="clickEvent(5)">菜单5</vxe-link>
    </div>
  </template>
</vxe-pulldown>

<script lang="ts" setup>
import { ref } from 'vue'
import { VxePulldownEvents } from 'vxe-pc-ui'

const showPull = ref(false)

const toggleEvent = () => {
  showPull.value = !showPull.value
}

const clickEvent = (num: number) => {
  showPull.value = false
  console.log(num)
}

const visibleChangeEvent: VxePulldownEvents.VisibleChange = ({ visible }) => {
  console.log('展开/隐藏', visible)
}
</script>

<style lang="scss" scoped>
.my-dropdown-link {
  display: block;
  text-align: center;
  line-height: 28px;
}
</style>

### 配置列表
<vxe-pulldown :options="pullOptions" trigger="click" @option-click="optionClickEvent">
  <template #default>
    <vxe-button mode="text">下拉按钮</vxe-button>
    <vxe-icon name="arrow-down"></vxe-icon>
  </template>
</vxe-pulldown>

<script lang="ts" setup>
import { ref } from 'vue'
import { VxeUI, VxePulldownPropTypes, VxePulldownEvents } from 'vxe-pc-ui'

const pullOptions = ref<VxePulldownPropTypes.Options>([
  { label: '选项1', value: '1' },
  { label: '选项2', value: '2' },
  { label: '选项3', value: '3' },
  { label: '选项4', value: '4' },
  { label: '选项5', value: '5' },
  { label: '选项6', value: '6' }
])

const optionClickEvent: VxePulldownEvents.OptionClick = ({ option }) => {
  VxeUI.modal.message({
    content: `点击了${option.value}`,
    status: 'success'
  })
}
</script>

### 自定义插槽模板
<vxe-pulldown v-model="showPull" popup-class-name="dropdown-popup-tmpl">
  <template #default>
    <vxe-button mode="text" icon="vxe-icon-arrow-down" @click="toggleEvent">下拉菜单</vxe-button>
  </template>
  <template #header>
    <div>头部</div>
  </template>
  <template #dropdown>
    <div class="dropdown-body-tmpl">
      <div>内容1</div>
      <div>内容2</div>
      <div>内容3</div>
      <div>内容4</div>
      <div>内容5</div>
      <div>内容6</div>
      <div>内容7</div>
      <div>内容8</div>
    </div>
  </template>
  <template #footer>
    <div>尾部</div>
  </template>
</vxe-pulldown>

<script lang="ts" setup>
import { ref } from 'vue'

const showPull = ref(false)

const toggleEvent = () => {
  showPull.value = !showPull.value
}
</script>

### 可搜索的下拉选择
<vxe-pulldown v-model="showPull">
  <template #default>
    <vxe-input v-model="searchName" placeholder="可搜索的下拉框" @focus="focusEvent" @keyup="keyupEvent"></vxe-input>
  </template>
  <template #dropdown>
    <div class="dropdown-select">
      <div class="list-item1" v-for="item in list" :key="item.value" @click="selectEvent(item)">
        <i class="vxe-icon-user-fill"></i>
        <span>{{ item.label }}</span>
      </div>
    </div>
  </template>
</vxe-pulldown>

<script lang="ts" setup>
import { ref } from 'vue'

interface ItemVO {
  label: string
  value: string
}

const mockData: ItemVO[] = [
  { label: '选项1', value: '1' },
  { label: '选项2', value: '2' },
  { label: '选项3', value: '3' },
  { label: '选项4', value: '4' },
  { label: '选项5', value: '5' },
  { label: '选项6', value: '6' },
  { label: '选项7', value: '7' },
  { label: '选项8', value: '8' },
  { label: '选项9', value: '9' },
  { label: '选项10', value: '10' },
  { label: '选项11', value: '11' },
  { label: '选项12', value: '12' }
]

const showPull = ref(false)
const searchName = ref('')
const list = ref<ItemVO[]>(mockData)

const focusEvent = () => {
  showPull.value = true
}

const keyupEvent = () => {
  list.value = searchName.value ? mockData.filter((item) => item.label.indexOf(searchName.value) > -1) : mockData
}

const selectEvent = (item: ItemVO) => {
  searchName.value = item.label
  showPull.value = false
  list.value = mockData
}
</script>


### 实现下拉表格
<vxe-pulldown ref="pulldownRef" popup-class-name="dropdown-table" transfer>
  <template #default>
    <vxe-input v-model="searchName" suffix-icon="vxe-icon-table" placeholder="实现下拉分页表格" @keyup="keyupEvent" @focus="focusEvent"></vxe-input>
  </template>

  <template #dropdown>
    <div class="dropdown-table-body">
      <vxe-grid v-bind="gridOptions" v-on="gridEvents"></vxe-grid>
    </div>
  </template>
</vxe-pulldown>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import type { VxeGridProps, VxeGridListeners } from 'vxe-table'
import { VxePulldownInstance } from 'vxe-pc-ui'

interface RowVO {
  name: string
  role: string
  sex: string
}

const searchName = ref('')

const mockList: RowVO[] = [
  { name: 'Test1', role: '前端', sex: '男' },
  { name: 'Test2', role: '后端', sex: '女' },
  { name: 'Test3', role: '测试', sex: '男' },
  { name: 'Test43', role: '设计师', sex: '女' },
  { name: 'Test512', role: '前端', sex: '男' },
  { name: 'Test61', role: '前端', sex: '男' },
  { name: 'Test71', role: '设计师', sex: '女' },
  { name: 'Test58', role: '前端', sex: '男' },
  { name: 'Test77', role: '后端', sex: '女' },
  { name: 'Test916', role: '前端', sex: '男' },
  { name: 'Test28', role: '后端', sex: '女' },
  { name: 'Test121', role: '前端', sex: '男' },
  { name: 'Test834', role: '测试', sex: '女' },
  { name: 'Test316', role: '前端', sex: '女' },
  { name: 'Test97', role: '测试', sex: '男' },
  { name: 'Test41', role: '前端', sex: '女' },
  { name: 'Test27', role: '测试', sex: '男' },
  { name: 'Test3218', role: '测试', sex: '女' }
]

const gridOptions = reactive<VxeGridProps<RowVO>>({
  border: true,
  autoResize: true,
  loading: false,
  height: '100%',
  rowConfig: {
    isHover: true
  },
  pagerConfig: {
    total: 0,
    currentPage: 1,
    pageSize: 5,
    pageSizes: [5, 10, 20]
  },
  columns: [
    { field: 'name', title: 'Name' },
    { field: 'role', title: 'Role' },
    { field: 'sex', title: 'Sex' }
  ],
  data: []
})

const pulldownRef = ref<VxePulldownInstance>()

const gridEvents: VxeGridListeners<RowVO> = {
  cellClick ({ row }) {
    const $pulldown = pulldownRef.value
    if ($pulldown) {
      searchName.value = row.name
      $pulldown.hidePanel()
    }
  },
  pageChange ({ currentPage, pageSize }) {
    if (gridOptions.pagerConfig) {
      gridOptions.pagerConfig.currentPage = currentPage
      gridOptions.pagerConfig.pageSize = pageSize
    }
    loadList()
  }
}

// 模拟后端接口
const findList = (currentPage = 1, pageSize = 5) => {
  return new Promise<{
    list: RowVO[]
    total: number
  }>(resolve => {
    setTimeout(() => {
      const list = mockList.filter((row) => row.name.indexOf(searchName.value) > -1)
      const limit = (currentPage - 1) * pageSize
      const result = list.slice(limit, limit + pageSize)
      resolve({
        list: result,
        total: list.length
      })
    }, 200)
  })
}

const loadList = () => {
  gridOptions.loading = true
  findList(gridOptions.pagerConfig?.currentPage, gridOptions.pagerConfig?.pageSize).then(({ list, total }) => {
    gridOptions.data = list
    gridOptions.loading = false
    if (gridOptions.pagerConfig) {
      gridOptions.pagerConfig.total = total
    }
  })
}

const focusEvent = () => {
  const $pulldown = pulldownRef.value
  if ($pulldown) {
    $pulldown.showPanel()
  }
}

const keyupEvent = () => {
  loadList()
}

loadList()
</script>

## List虚拟列表

### 纵向虚拟列表

<vxe-button @click="loadData(250000)">加载25w条</vxe-button>

<vxe-list height="600" class="my-list" :loading="loading" :data="list" :virtual-y-config="{enabled: true}">
  <template #default="{ items }">
    <div class="my-list-item" v-for="(item, index) in items" :key="index">
      <span>自定义内容 {{ item.label }}</span>
    </div>
  </template>
</vxe-list>

<script lang="ts" setup>
import { ref, onMounted, nextTick } from 'vue'
import { VxeUI } from 'vxe-pc-ui'

interface ItemVO {
  id: number
  label: string
}

const loading = ref(false)
const list = ref<ItemVO[]>([])

// 模拟后台
const mockList: ItemVO[] = []
const getList = (size: number) => {
  return new Promise<ItemVO[]>(resolve => {
    setTimeout(() => {
      if (size > mockList.length) {
        for (let index = mockList.length; index < size; index++) {
          mockList.push({
            id: index,
            label: `row_${index}`
          })
        }
      }
      resolve(mockList.slice(0, size))
    }, 100)
  })
}

const loadData = async (size: number) => {
  loading.value = true
  list.value = await getList(size)
  loading.value = false
  const startTime = Date.now()
  await nextTick()
  await VxeUI.modal.message({
    content: `渲染 ${size} 行，用时 ${Date.now() - startTime}毫秒`,
    status: 'info'
  })
}

onMounted(async () => {
  loadData(200)
})
</script>

### 纵向虚拟列表
<vxe-button @click="height = 600">设置600</vxe-button>
<vxe-list :height="height" class="my-list" :loading="loading" :data="list" :virtual-y-config="{enabled: true}" auto-resize>
  <template #default="{ items }">
    <div class="my-list-item" v-for="(item, index) in items" :key="index">
      <span>自定义内容 {{ item.label }}</span>
    </div>
  </template>
</vxe-list>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'

interface ItemVO {
  id: number
  label: string
}

const loading = ref(false)
const list = ref<ItemVO[]>([])
const height = ref(200)

// 模拟后台
const mockList: ItemVO[] = []
const getList = (size: number) => {
  return new Promise<ItemVO[]>(resolve => {
    setTimeout(() => {
      if (size > mockList.length) {
        for (let index = mockList.length; index < size; index++) {
          mockList.push({
            id: index,
            label: `row_${index}`
          })
        }
      }
      resolve(mockList.slice(0, size))
    }, 100)
  })
}

const loadData = async (size: number) => {
  loading.value = true
  list.value = await getList(size)
  loading.value = false
}

onMounted(() => {
  loadData(500)
})
</script>

### 响应式高度
<vxe-button @click="height = 600">设置600</vxe-button>
<vxe-list :height="height" class="my-list" :loading="loading" :data="list" :virtual-y-config="{enabled: true}" auto-resize>
  <template #default="{ items }">
    <div class="my-list-item" v-for="(item, index) in items" :key="index">
      <span>自定义内容 {{ item.label }}</span>
    </div>
  </template>
</vxe-list>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'

interface ItemVO {
  id: number
  label: string
}

const loading = ref(false)
const list = ref<ItemVO[]>([])
const height = ref(200)

// 模拟后台
const mockList: ItemVO[] = []
const getList = (size: number) => {
  return new Promise<ItemVO[]>(resolve => {
    setTimeout(() => {
      if (size > mockList.length) {
        for (let index = mockList.length; index < size; index++) {
          mockList.push({
            id: index,
            label: `row_${index}`
          })
        }
      }
      resolve(mockList.slice(0, size))
    }, 100)
  })
}

const loadData = async (size: number) => {
  loading.value = true
  list.value = await getList(size)
  loading.value = false
}

onMounted(() => {
  loadData(500)
})
</script>


### 实现轻量级虚拟表格
<vxe-button @click="loadData(250000)">加载25w条</vxe-button>
<vxe-list class="my-table-list" height="600" :data="list" :virtual-y-config="{enabled: true, gt: 0, sItem: '.my-tr'}">
  <template #default="{ items }">
    <table>
      <thead>
        <tr>
          <th>列1</th>
          <th>列2</th>
          <th>列3</th>
          <th>列4</th>
          <th>列5</th>
        </tr>
      </thead>
      <tbody>
        <tr class="my-tr" v-for="item in items" :key="item.id">
          <td>{{ item.col1 }}</td>
          <td>{{ item.col2 }}</td>
          <td>{{ item.col3 }}</td>
          <td>{{ item.col4 }}</td>
          <td>{{ item.col5 }}</td>
        </tr>
      </tbody>
    </table>
  </template>
</vxe-list>
    
<script lang="ts" setup>
import { ref, onMounted, nextTick } from 'vue'
import { VxeUI } from 'vxe-pc-ui'

interface ItemVO {
  id: number
  col1: string
  col2: string
  col3: string
  col4: string
  col5: string
}

const loading = ref(false)
const list = ref<ItemVO[]>([])

// 模拟后台
const mockList: ItemVO[] = []
const getList = (size: number): Promise<ItemVO[]> => {
  return new Promise(resolve => {
    setTimeout(() => {
      if (size > mockList.length) {
        for (let index = mockList.length; index < size; index++) {
          mockList.push({
            id: index,
            col1: `row ${index} col1`,
            col2: `row ${index} col2`,
            col3: `row ${index} col3`,
            col4: `row ${index} col4`,
            col5: `row ${index} col5`
          })
        }
      }
      resolve(mockList.slice(0, size))
    }, 100)
  })
}

const loadData = async (size: number) => {
  loading.value = true
  list.value = await getList(size)
  loading.value = false
  const startTime = Date.now()
  await nextTick()
  await VxeUI.modal.message({
    content: `渲染 ${size} 行，用时 ${Date.now() - startTime}毫秒`,
    status: 'info'
  })
}

onMounted(async () => {
  loadData(200)
})
</script>

#展示组件

## Avatar 头像

### 基础
<vxe-avatar src="https://vxeui.com/resource/img/user-square.png"></vxe-avatar>

### 尺寸大小
<vxe-avatar src="https://vxeui.com/resource/img/user-square.png" size="medium"></vxe-avatar>

### 图标
<vxe-avatar icon="vxe-icon-user"></vxe-avatar>

### 圆形
<vxe-avatar src="https://vxeui.com/resource/img/user-square.png" circle></vxe-avatar>

### 状态颜色
<vxe-avatar status="primary" content="X"></vxe-avatar>

### 小圆点
<vxe-avatar icon="vxe-icon-user" dot></vxe-avatar>

### 微标数量
<vxe-avatar icon="vxe-icon-user" count="8"></vxe-avatar>

## Badge 微标

### 基础
<vxe-badge content="项目数" count="8"></vxe-badge>

### 尺寸大小
<vxe-badge content="项目数" count="8" size="medium"></vxe-badge>

### 小圆点
<vxe-badge content="项目数" dot></vxe-badge>

## TextEllipsis 文本溢出省略

### 尺寸大小
<vxe-text-ellipsis size="medium" content="中等尺寸，这是一个单行文本，超出一行之后会自动截断，并且会出现省略，后面文字会被隐藏将不会被显示出来。"></vxe-text-ellipsis>

### 状态颜色
<vxe-text-ellipsis status="primary" content="主要颜色，这是一个单行文本，超出一行之后会自动截断，并且会出现省略，后面文字会被隐藏将不会被显示出来。"></vxe-text-ellipsis>

### 单行文字溢出
<vxe-text-ellipsis content="这是一个单行文本，超出一行之后会自动截断，并且会出现省略，后面文字会被隐藏将不会被显示出来。"></vxe-text-ellipsis>

### 多行文字溢出
<vxe-text-ellipsis line-clamp="3" content="这是一个多行的文本溢出省略组件，用于实现多行文本溢出省略，这将非常有用，如果没有超出，则显示全部文本，如超出指定行数之后，文字会被会自动截断，并且会出现省略，后面文字会被隐藏将不会被显示出来。"></vxe-text-ellipsis>

## Image 图片

### 设置宽度
<vxe-image src="https://vxeui.com/resource/img/fj577.jpg" :width="100"></vxe-image>

### 圆形图片
<vxe-image src="https://vxeui.com/resource/avatarImg/avatar1.jpeg" :width="200" circle></vxe-image>

### 预览多张图片
<vxe-image :src="imgList1" :width="200"></vxe-image>

const imgList1 = ref([
  'https://vxeui.com/resource/img/fj577.jpg',
  'https://vxeui.com/resource/img/fj581.jpeg',
  'https://vxeui.com/resource/img/fj573.jpeg'
])

### 懒加载
<vxe-image src="https://vxeui.com/resource/img/fj581.jpeg" loading="lazy" :width="600"></vxe-image>

### 预览
<vxe-button @click="showPreview = !showPreview">切换预览 {{ showPreview }}</vxe-button>
<vxe-image src="https://vxeui.com/resource/img/fj577.jpg" :width="200" :show-preview="showPreview"></vxe-image>

const showPreview = ref(true)

### 点击遮罩层关闭
<vxe-image src="https://vxeui.com/resource/img/fj577.jpg" :width="100" mask-closable></vxe-image>

### 工具栏按钮
<vxe-image src="https://vxeui.com/resource/img/fj577.jpg" :width="200" :toolbar-config="toolbarConfig"></vxe-image>
const toolbarConfig = reactive<VxeImagePropTypes.ToolbarConfig>({
  zoomOut: true,
  zoomIn: true,
  pctFull: false,
  pct11: false,
  rotateLeft: true,
  rotateRight: true,
  print: false,
  download: false
})

### 工具栏图标
<vxe-image src="https://vxeui.com/resource/img/fj577.jpg" :width="200" :toolbar-config="toolbarConfig"></vxe-image>
const toolbarConfig = reactive<VxeImagePropTypes.ToolbarConfig>({
  zoomOut: {
    icon: 'vxe-icon-square-minus'
  },
  zoomIn: {
    icon: 'vxe-icon-square-plus'
  },
  pctFull: {
    icon: 'vxe-icon-mobile'
  },
  pct11: {
    icon: 'vxe-icon-minimize'
  },
  rotateLeft: {
    icon: 'vxe-icon-swap-left'
  },
  rotateRight: {
    icon: 'vxe-icon-swap-right'
  },
  print: {
    icon: 'vxe-icon-envelope'
  },
  download: {
    icon: 'vxe-icon-cloud-upload'
  }
})

## ImageGroup 图片组

### 设置宽高
<vxe-image-group :url-list="urlList" :image-style="{width: 60, height: 60}"></vxe-image-group>

const urlList = ref([
  'https://vxeui.com/resource/img/fj573.jpeg',
  'https://vxeui.com/resource/img/fj562.png',
  'https://vxeui.com/resource/img/fj187.jpg'
])

### 预览
<vxe-button @click="showPreview = !showPreview">切换预览 {{ showPreview }}</vxe-button>
<vxe-image-group :url-list="urlList" :image-style="{width: 100, height: 100}" :show-preview="showPreview"></vxe-image-group>

const showPreview = ref(true)
const urlList = ref([
  'https://vxeui.com/resource/img/fj573.jpeg',
  'https://vxeui.com/resource/img/fj562.png',
  'https://vxeui.com/resource/img/fj187.jpg'
])

### 打印按钮
<vxe-image-group :url-list="urlList" :image-style="{width: 100, height: 100}" :show-print-button="false"></vxe-image-group>

const urlList = ref([
  'https://vxeui.com/resource/img/fj573.jpeg',
  'https://vxeui.com/resource/img/fj562.png',
  'https://vxeui.com/resource/img/fj187.jpg'
])

### 下载按钮
<vxe-image-group :url-list="urlList" :image-style="{width: 100, height: 100}" show-download-button></vxe-image-group>

const urlList = ref([
  'https://vxeui.com/resource/img/fj573.jpeg',
  'https://vxeui.com/resource/img/fj562.png',
  'https://vxeui.com/resource/img/fj187.jpg'
])

## ImagePreview 图片预览

### 打开预览
<vxe-button status="primary" @click="clickEvent">点击预览图片</vxe-button>

const clickEvent = () => {
  VxeUI.previewImage({
    activeIndex: 1,
    urlList: [
      'https://vxeui.com/resource/img/fj573.jpeg',
      'https://vxeui.com/resource/img/fj562.png',
      'https://vxeui.com/resource/img/fj187.jpg'
    ]
  })
}

## Calendar 日历

### 尺寸大小
<vxe-calendar size="medium"></vxe-calendar>

### 高度
<vxe-calendar height="400" width="400"></vxe-calendar>

### 日期视图
<vxe-calendar v-model="val"></vxe-calendar>

### 周视图
<vxe-calendar v-model="val" type="week"></vxe-calendar>

### 月视图
<vxe-calendar v-model="val" type="month"></vxe-calendar>

### 季度视图
<vxe-calendar v-model="val" type="quarter"></vxe-calendar>

### 年视图
<vxe-calendar v-model="val" type="year"></vxe-calendar>

### 显示节假日
<vxe-calendar v-model="val" :festival-method="festivalCalendarMethod"></vxe-calendar>

const val = ref('2020-10-01')

const calendarMaps = ref({
  20200930: {
    label: '十四' // 显示节日名称
  },
  20201001: {
    label: '国庆节,中秋节', // 如果同一天拥有多个节日重叠，用逗号分开
    important: true, // 是否标记为重要节日
    extra: '休' // 右上角额外显示的名称
  },
  20201002: {
    label: '十六',
    extra: '休'
  },
  20201003: {
    label: '十七',
    extra: '休'
  },
  20201004: {
    label: '十八',
    extra: '休'
  },
  20201005: {
    label: '十九',
    extra: '休'
  },
  20201006: {
    label: '二十',
    extra: '休'
  },
  20201007: {
    label: '廿一',
    extra: '休'
  },
  20201008: {
    label: '寒霜',
    important: true,
    extra: '休'
  },
  20201009: {
    label: '廿三'
  },
  20201010: {
    label: '廿四',
    extra: {
      label: '班',
      important: true // 是否标记为重要节日
    }
  }
})

const festivalCalendarMethod: VxeCalendarPropTypes.FestivalMethod = (params) => {
  const { date, viewType } = params
  if (viewType === 'day') {
    const ymd = XEUtils.toDateString(date, 'yyyyMMdd')
    return calendarMaps.value[ymd] || { label: '无' }
  }
}

### 事件监听
<vxe-calendar v-model="val" v-on="calendarEvents"></vxe-calendar>

const val = ref('')

const calendarEvents: VxeCalendarListeners = {
  change ({ value }) {
    console.log('change', value)
  },
  viewChange ({ viewType }) {
    console.log('view-change', viewType)
  }
}

# 导航组件

## Breadcrumb 面包屑

### 基础
<vxe-breadcrumb>
  <vxe-breadcrumb-item title="首页"></vxe-breadcrumb-item>
  <vxe-breadcrumb-item title="系统设置"></vxe-breadcrumb-item>
</vxe-breadcrumb>

## Menu菜单

### 基础
<vxe-menu v-model="selectNav" :options="navList" @click="clickEvent"></vxe-menu>

const selectNav = ref('user')
const navList = ref<VxeMenuPropTypes.Options>([
  { name: 'home', title: '首页' },
  {
    name: 'user',
    title: '个人中心',
    children: [
      { name: 'changePassword', title: '修改密码' }
    ]
  },
  {
    name: 'system',
    title: '系统设置',
    children: [
      { name: 'menu', title: '菜单配置' },
      { name: 'permission', title: '权限配置' }
    ]
  }
])

const clickEvent: VxeMenuEvents.Click = ({ menu }) => {
  console.log(menu.name)
}

### 加载中
<vxe-menu v-model="selectNav" :loading="loading" :options="navList" @click="clickEvent"></vxe-menu>

const selectNav = ref('user')
const loading = ref(false)
const navList = ref<VxeMenuPropTypes.Options>([])

const loadList = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    navList.value = [
      { name: 'home', title: '首页' },
      {
        name: 'user',
        title: '个人中心',
        children: [
          { name: 'changePassword', title: '修改密码' }
        ]
      },
      {
        name: 'system',
        title: '系统设置',
        children: [
          { name: 'menu', title: '菜单配置' },
          { name: 'permission', title: '权限配置' }
        ]
      }
    ]
  }, 3000)
}

const clickEvent: VxeMenuEvents.Click = ({ menu }) => {
  console.log(menu.name)
}

### 图标
<vxe-menu v-model="selectNav" :options="navList" @click="clickEvent"></vxe-menu>

const selectNav = ref('user')
const navList = ref<VxeMenuPropTypes.Options>([
  { name: 'home', title: '首页', icon: 'vxe-icon-home' },
  {
    name: 'user',
    title: '个人中心',
    icon: 'vxe-icon-user',
    children: [
      { name: 'changePassword', icon: 'vxe-icon-lock', title: '修改密码' }
    ]
  },
  {
    name: 'system',
    title: '系统设置',
    icon: 'vxe-icon-setting',
    children: [
      { name: 'menu', icon: 'vxe-icon-menu', title: '菜单配置' },
      { name: 'permission', icon: 'vxe-icon-user', title: '权限配置' }
    ]
  }
])

const clickEvent: VxeMenuEvents.Click = ({ menu }) => {
  console.log(menu.name)
}

### 路由模式
<vxe-menu v-model="selectNav" :options="navList"></vxe-menu>

const selectNav = ref('form')
const navList = ref<VxeMenuPropTypes.Options>([
  { name: 'home', title: '开发指南', icon: 'vxe-icon-home', routerLink: { name: 'StartInstall' } },
  {
    name: 'formGroup',
    title: '表单文档',
    icon: 'vxe-icon-menu',
    children: [
      { name: 'form', title: '表单', routerLink: { name: 'ComponentFormBasicsBase' } }
    ]
  },
  {
    name: 'tableGroup',
    title: '表格文档',
    icon: 'vxe-icon-table',
    children: [
      { name: 'table', title: '基础表格', routerLink: { name: 'ComponentTableBaseBasic' } },
      { name: 'grid', title: '配置式表格', routerLink: { name: 'ComponentGridBaseBasic' } }
    ]
  }
])

### 权限码
<vxe-menu v-model="selectNav" :options="navList"></vxe-menu>

const selectNav = ref('form')
const navList = ref<VxeMenuPropTypes.Options>([
  { name: 'home', title: '开发指南', permissionCode: 'xxx', routerLink: { name: 'StartInstall' } },
  {
    name: 'formGroup',
    title: '表单文档',
    permissionCode: 'xxx',
    children: [
      { name: 'form', title: '表单', permissionCode: 'xxx', routerLink: { name: 'ComponentFormBasicsBase' } }
    ]
  },
  {
    name: 'tableGroup',
    title: '表格文档',
    permissionCode: 'xxx',
    children: [
      { name: 'table', title: '基础表格', permissionCode: 'xxx', routerLink: { name: 'ComponentTableBaseBasic' } },
      { name: 'grid', title: '配置式表格', permissionCode: 'xxx', routerLink: { name: 'ComponentGridBaseBasic' } }
    ]
  }
])

### 自定义插槽模板
<vxe-menu v-model="selectNav" :options="navList" @click="clickEvent">
  <template #option="{ option }">
    <span style="color: red">{{ option.title }}</span>
  </template>
</vxe-menu>

const selectNav = ref('user')
const navList = ref<VxeMenuPropTypes.Options>([
  { name: 'home', title: '首页' },
  {
    name: 'user',
    title: '个人中心',
    children: [
      { name: 'changePassword', title: '修改密码' }
    ]
  },
  {
    name: 'system',
    title: '系统设置',
    children: [
      { name: 'menu', title: '菜单配置' },
      { name: 'permission', title: '权限配置' }
    ]
  }
])

const clickEvent: VxeMenuEvents.Click = ({ menu }) => {
  console.log(menu.name)
}

## Anchor 锚点

### 自定义容器
<vxe-row>
  <vxe-col>
    <vxe-anchor container="#www">
    <vxe-anchor-link href="#xxx1">菜单1</vxe-anchor-link>
    <vxe-anchor-link href="#xxx2">菜单2</vxe-anchor-link>
  </vxe-anchor>
  </vxe-col>
  <vxe-col>
    <div id="www" style="height: 300px;overflow: auto;">
      <div>内容</div>
      <div>内容</div>
      <div id="xxx1">菜单1</div>
      <div>内容</div>
      <div>内容</div>
      <div>内容</div>
      <div>内容</div>
      <div>内容</div>
      <div>内容</div>
      <div>内容</div>
      <div>内容</div>
      <div>内容</div>
      <div>内容</div>
      <div id="xxx2">菜单2</div>
      <div>内容</div>
      <div>内容</div>
      <div>内容</div>
      <div>内容</div>
      <div>内容</div>
      <div>内容</div>
      <div>内容</div>
      <div>内容</div>
      <div>内容</div>
      <div>内容</div>
    </div>
  </vxe-col>
</vxe-row>

## Pager 分页

### 尺寸大小
<vxe-pager v-model:current-page="pageVO1.currentPage" v-model:page-size="pageVO1.pageSize" :total="pageVO1.total" />

const pageVO1 = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 88
})

const pageVO2 = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 88
})

const pageVO3 = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 88
})

const pageVO4 = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 88
})

### 对齐方式
<vxe-pager
  align="left"
  v-model:current-page="pageVO.currentPage"
  v-model:page-size="pageVO.pageSize"
  :total="pageVO.total">
</vxe-pager>

const pageVO = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 8
})

### 自定义页大小列表
<vxe-pager
  v-model:current-page="pageVO.currentPage"
  v-model:page-size="pageVO.pageSize"
  :total="pageVO.total"
  :page-sizes="[10, 20, 100, {label: '大量数据', value: 1000}, {label: '全量数据', value: -1}]"
  @page-change="pageChangeEvent">
</vxe-pager>
    
const pageVO = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 8
})

const pageChangeEvent = () => {
  console.log(`分页事件：第 ${pageVO.currentPage} 页，每页  ${pageVO.pageSize} 条`)
}

### 自定义布局
<vxe-pager
  v-model:current-page="pageVO.currentPage"
  v-model:page-size="pageVO.pageSize"
  :total="pageVO.total"
  :layouts="['PrevPage', 'NextPage']"
  @page-change="pageChangeEvent">
</vxe-pager>

const pageVO = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 150
})

const pageChangeEvent: VxePagerEvents.PageChange = () => {
  console.log(`分页事件：第 ${pageVO.currentPage} 页，每页  ${pageVO.pageSize} 条`)
}

### 自定义模版

#### 左侧区域
<vxe-pager
  v-model:current-page="pageVO1.currentPage"
  v-model:page-size="pageVO1.pageSize"
  :total="pageVO1.total">
  <template #left>
    <vxe-image src="https://vxeui.com/resource/img/546.gif" height="24"></vxe-image>
    <vxe-button content="下拉按钮">
      <template #dropdowns>
        <vxe-button content="批量修改" mode="text"></vxe-button>
        <vxe-button content="批量管理" mode="text"></vxe-button>
        <vxe-button content="批量删除" mode="text"></vxe-button>
      </template>
    </vxe-button>
    <vxe-image src="https://vxeui.com/resource/img/546.gif" height="24"></vxe-image>
  </template>
</vxe-pager>

const pageVO1 = reactive({
  currentPage: 1,
  pageSize: 15,
  total: 100
})

#### 右侧区域
<vxe-pager
  v-model:current-page="pageVO1.currentPage"
  v-model:page-size="pageVO1.pageSize"
  :total="pageVO1.total">
  <template #right>
    <vxe-image src="https://vxeui.com/resource/img/546.gif" height="24"></vxe-image>
    <vxe-button content="下拉按钮">
      <template #dropdowns>
        <vxe-button content="批量修改" mode="text"></vxe-button>
        <vxe-button content="批量管理" mode="text"></vxe-button>
        <vxe-button content="批量删除" mode="text"></vxe-button>
      </template>
    </vxe-button>
    <vxe-image src="https://vxeui.com/resource/img/546.gif" height="24"></vxe-image>
  </template>
</vxe-pager>

const pageVO1 = reactive({
  currentPage: 1,
  pageSize: 15,
  total: 100
})

#### 功能区
<vxe-pager
  v-model:current-page="pageVO2.currentPage"
  v-model:page-size="pageVO2.pageSize"
  :layouts="['Home', 'PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'End', 'Sizes', 'FullJump', 'PageCount', 'Total']"
  :total="pageVO2.total">
  <template #home>
    <span style="background: #f7b6b6;" @click="pageVO2.currentPage = 1">首页</span>
  </template>
  <template #prevJump>
    <span style="background: #8989d7;" @click="pageVO2.currentPage -= 3">向上跳页</span>
  </template>
  <template #prevPage>
    <span style="background: #f785ef;" @click="pageVO2.currentPage--">上一页</span>
  </template>
  <template #number="{ numList }">
    <span>
      <span v-for="(num, i) in numList" :key="i" @click="pageVO2.currentPage = num">{{ num }}，</span>
    </span>
  </template>
  <template #nextPage>
    <span style="background: #c0f9dc;" @click="pageVO2.currentPage++">下一页</span>
  </template>
  <template #nextJump>
    <span style="background: #48f146;" @click="pageVO2.currentPage += 3">向下跳页</span>
  </template>
  <template #end="{ pageCount }">
    <span style="background: #f9f192;" @click="pageVO2.currentPage = pageCount">末页</span>
  </template>
  <template #sizes="{ options }">
    <span style="background: #cff395;">选择页大小 <vxe-select v-model="pageVO2.pageSize" :options="options" transfer></vxe-select></span>
  </template>
  <template #fullJump>
    <span style="background: #dbd7d7;">跳页<vxe-number-input v-model="pageVO2.currentPage" type="integer"></vxe-number-input></span>
  </template>
  <template #pageCount="{ pageCount }">
    <span style="background: #f3b27a;">总页数 {{ pageCount }}</span>
  </template>
  <template #total="{ total }">
    <span style="background: #f95e5e;">总条数 {{ total }}</span>
  </template>
</vxe-pager>

const pageVO1 = reactive({
  currentPage: 1,
  pageSize: 15,
  total: 200
})

const pageVO2 = reactive({
  currentPage: 1,
  pageSize: 15,
  total: 200
})

# Form 表单

## 基础表单

### 基础功能
<template>
  <div>
    <vxe-form
      ref="formRef"
      :data="formData"
      @submit="submitEvent"
      @reset="resetEvent">
      <vxe-form-item title="名称" field="name" span="24" :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.name" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="性别" field="sex" span="12" :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.sex" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="年龄" field="age" span="12" :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.age" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="地址" field="address" span="24" :item-render="{}">
        <template #default="params">
          <vxe-textarea v-model="formData.address" @change="changeEvent(params)"></vxe-textarea>
        </template>
      </vxe-form-item>
      <vxe-form-item align="center" span="24" :item-render="{}">
        <template #default>
          <vxe-button type="submit" status="primary" content="提交"></vxe-button>
          <vxe-button type="reset" content="重置"></vxe-button>
        </template>
      </vxe-form-item>
    </vxe-form>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { VxeUI, VxeFormInstance, VxeFormEvents } from 'vxe-pc-ui'

interface FormDataVO {
  name: string
  nickname: string
  sex: string
  age: string
  address: string
}

const formRef = ref<VxeFormInstance<FormDataVO>>()

const formData = ref<FormDataVO>({
  name: 'test1',
  nickname: 'Testing',
  sex: '',
  age: '',
  address: ''
})

const changeEvent = (params: any) => {
  const $form = formRef.value
  if ($form) {
    $form.updateStatus(params)
  }
}

const submitEvent: VxeFormEvents.Submit = () => {
  VxeUI.modal.message({ content: '保存成功', status: 'success' })
}

const resetEvent: VxeFormEvents.Reset = () => {
  VxeUI.modal.message({ content: '重置事件', status: 'info' })
}
</script>

### 尺寸大小
<template>
  <div>
    <vxe-form :data="formData2" size="medium">
      <vxe-form-item title="名称" field="name" :item-render="{}">
        <template #default="{ data }">
          <vxe-input v-model="data.name" placeholder="请输入名称" clearable></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="性别" field="sex" :item-render="{}">
        <template #default="{ data }">
          <vxe-select v-model="data.sex" placeholder="请选择性别" clearable>
            <vxe-option value="1" label="女"></vxe-option>
            <vxe-option value="2" label="男"></vxe-option>
          </vxe-select>
        </template>
      </vxe-form-item>
      <vxe-form-item>
        <template #default>
          <vxe-button type="submit" status="primary" content="中等尺寸"></vxe-button>
        </template>
      </vxe-form-item>
    </vxe-form>
    <vxe-form :data="formData3" size="small">
      <vxe-form-item title="名称" field="name" :item-render="{}">
        <template #default="{ data }">
          <vxe-input v-model="data.name" placeholder="请输入名称" clearable></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="性别" field="sex" :item-render="{}">
        <template #default="{ data }">
          <vxe-select v-model="data.sex" placeholder="请选择性别" clearable>
            <vxe-option value="1" label="女"></vxe-option>
            <vxe-option value="2" label="男"></vxe-option>
          </vxe-select>
        </template>
      </vxe-form-item>
      <vxe-form-item>
        <template #default>
          <vxe-button type="submit" status="primary" content="小型尺寸"></vxe-button>
        </template>
      </vxe-form-item>
    </vxe-form>
  </div>
</template>

<script lang="ts" setup>
import { reactive } from 'vue'

interface FormDataVO {
  name: string
  sex: string
}

const formData2 = reactive<FormDataVO>({
  name: '',
  sex: '1'
})

const formData3 = reactive<FormDataVO>({
  name: '',
  sex: '1'
})

</script>

### 数据类型
<template>
  <div>
    <vxe-form
      ref="formRef"
      :data="formData"
      @submit="submitEvent"
      @reset="resetEvent">
      <vxe-form-item title="名称" field="name" span="24" :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.name" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="性别" field="sex" span="12" :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.sex" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="年龄" field="age" span="12" :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.age" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item align="center" span="24" :item-render="{}">
        <template #default>
          <vxe-button type="submit" status="primary" content="提交"></vxe-button>
          <vxe-button type="reset" content="重置"></vxe-button>
        </template>
      </vxe-form-item>
    </vxe-form>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { VxeUI, VxeFormInstance, VxeFormEvents } from 'vxe-pc-ui'

interface FormDataVO {
  name: string
  nickname: string
  sex: string
  age: string
}

const formRef = ref<VxeFormInstance<FormDataVO>>()

const formData = ref<FormDataVO>({
  name: 'test1',
  nickname: 'Testing',
  sex: '',
  age: ''
})

const changeEvent = (params: any) => {
  const $form = formRef.value
  if ($form) {
    $form.updateStatus(params)
  }
}

const submitEvent: VxeFormEvents.Submit = () => {
  VxeUI.modal.message({ content: '保存成功', status: 'success' })
}

const resetEvent: VxeFormEvents.Reset = () => {
  VxeUI.modal.message({ content: '重置事件', status: 'info' })
}
</script>

### 横向排列
<template>
  <div>
    <vxe-form
      :data="formData"
      @submit="submitEvent"
      @reset="resetEvent">
      <vxe-form-item title="名称" field="name" :item-render="{}">
        <template #default>
          <vxe-input v-model="formData.name"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="性别" field="sex" :item-render="{}">
        <template #default>
          <vxe-input v-model="formData.sex"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="年龄" field="age" :item-render="{}">
        <template #default>
          <vxe-input v-model="formData.age"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item :item-render="{}">
        <template #default>
          <vxe-button type="submit" status="primary" content="提交"></vxe-button>
          <vxe-button type="reset" content="重置"></vxe-button>
        </template>
      </vxe-form-item>
    </vxe-form>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { VxeUI, VxeFormEvents } from 'vxe-pc-ui'

interface FormDataVO {
  name: string
  nickname: string
  sex: string
  age: string
}

const formData = ref<FormDataVO>({
  name: 'test1',
  nickname: 'Testing',
  sex: '',
  age: ''
})

const submitEvent: VxeFormEvents.Submit = () => {
  VxeUI.modal.message({ content: '保存成功', status: 'success' })
}

const resetEvent: VxeFormEvents.Reset = () => {
  VxeUI.modal.message({ content: '重置事件', status: 'info' })
}
</script>


### 上下布局
<template>
  <div>
    <vxe-form
      ref="formRef"
      vertical
      :data="formData"
      @submit="submitEvent"
      @reset="resetEvent">
      <vxe-form-item title="名称" field="name" span="24" :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.name" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="性别" field="sex" span="24" :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.sex" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item align="center" span="24" :item-render="{}">
        <template #default>
          <vxe-button type="submit" status="primary" content="提交"></vxe-button>
          <vxe-button type="reset" content="重置"></vxe-button>
        </template>
      </vxe-form-item>
    </vxe-form>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { VxeUI, VxeFormInstance, VxeFormEvents } from 'vxe-pc-ui'

interface FormDataVO {
  name: string
  sex: string
}

const formRef = ref<VxeFormInstance<FormDataVO>>()

const formData = ref<FormDataVO>({
  name: 'test1',
  sex: '',
})

const changeEvent = (params: any) => {
  const $form = formRef.value
  if ($form) {
    $form.updateStatus(params)
  }
}

const submitEvent: VxeFormEvents.Submit = () => {
  VxeUI.modal.message({ content: '保存成功', status: 'success' })
}

const resetEvent: VxeFormEvents.Reset = () => {
  VxeUI.modal.message({ content: '重置事件', status: 'info' })
}
</script>


### 边距
<template>
  <div>
    <vxe-form
      ref="formRef"
      :padding="false"
      :data="formData"
      @submit="submitEvent"
      @reset="resetEvent">
      <vxe-form-item title="名称" field="name" span="24" :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.name" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="性别" field="sex" span="12" :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.sex" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="年龄" field="age" span="12" :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.age" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item align="center" span="24" :item-render="{}">
        <template #default>
          <vxe-button type="submit" status="primary" content="提交"></vxe-button>
          <vxe-button type="reset" content="重置"></vxe-button>
        </template>
      </vxe-form-item>
    </vxe-form>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { VxeUI, VxeFormInstance, VxeFormEvents } from 'vxe-pc-ui'

interface FormDataVO {
  name: string
  nickname: string
  sex: string
  age: string
}

const formRef = ref<VxeFormInstance<FormDataVO>>()

const formData = ref<FormDataVO>({
  name: 'test1',
  nickname: 'Testing',
  sex: '',
  age: ''
})

const changeEvent = (params: any) => {
  const $form = formRef.value
  if ($form) {
    $form.updateStatus(params)
  }
}

const submitEvent: VxeFormEvents.Submit = () => {
  VxeUI.modal.message({ content: '保存成功', status: 'success' })
}

const resetEvent: VxeFormEvents.Reset = () => {
  VxeUI.modal.message({ content: '重置事件', status: 'info' })
}
</script>

### 标题冒号
<template>
  <div>
    <vxe-form
      ref="formRef"
      title-colon
      :data="formData"
      @submit="submitEvent"
      @reset="resetEvent">
      <vxe-form-item title="名称" field="name" span="24" :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.name" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="性别" field="sex" span="12" :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.sex" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="年龄" field="age" span="12" :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.age" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item align="center" span="24" :item-render="{}">
        <template #default>
          <vxe-button type="submit" status="primary" content="提交"></vxe-button>
          <vxe-button type="reset" content="重置"></vxe-button>
        </template>
      </vxe-form-item>
    </vxe-form>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { VxeUI, VxeFormInstance, VxeFormEvents } from 'vxe-pc-ui'

interface FormDataVO {
  name: string
  nickname: string
  sex: string
  age: string
}

const formRef = ref<VxeFormInstance<FormDataVO>>()

const formData = ref<FormDataVO>({
  name: 'test1',
  nickname: 'Testing',
  sex: '',
  age: ''
})

const changeEvent = (params: any) => {
  const $form = formRef.value
  if ($form) {
    $form.updateStatus(params)
  }
}

const submitEvent: VxeFormEvents.Submit = () => {
  VxeUI.modal.message({ content: '保存成功', status: 'success' })
}

const resetEvent: VxeFormEvents.Reset = () => {
  VxeUI.modal.message({ content: '重置事件', status: 'info' })
}
</script>

### 标题加粗
<template>
  <div>
    <vxe-form
      ref="formRef"
      title-bold
      :data="formData"
      @submit="submitEvent"
      @reset="resetEvent">
      <vxe-form-item title="名称" field="name" span="24" :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.name" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="性别" field="sex" span="12" :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.sex" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="年龄" field="age" span="12" :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.age" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item align="center" span="24" :item-render="{}">
        <template #default>
          <vxe-button type="submit" status="primary" content="提交"></vxe-button>
          <vxe-button type="reset" content="重置"></vxe-button>
        </template>
      </vxe-form-item>
    </vxe-form>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { VxeUI, VxeFormInstance, VxeFormEvents } from 'vxe-pc-ui'

interface FormDataVO {
  name: string
  nickname: string
  sex: string
  age: string
}

const formRef = ref<VxeFormInstance<FormDataVO>>()

const formData = ref<FormDataVO>({
  name: 'test1',
  nickname: 'Testing',
  sex: '',
  age: ''
})

const changeEvent = (params: any) => {
  const $form = formRef.value
  if ($form) {
    $form.updateStatus(params)
  }
}

const submitEvent: VxeFormEvents.Submit = () => {
  VxeUI.modal.message({ content: '保存成功', status: 'success' })
}

const resetEvent: VxeFormEvents.Reset = () => {
  VxeUI.modal.message({ content: '重置事件', status: 'info' })
}
</script>

### 标题宽度
<template>
  <div>
    <vxe-form
      ref="formRef"
      :title-width="120"
      :data="formData"
      @submit="submitEvent"
      @reset="resetEvent">
      <vxe-form-item title="名称" field="name" span="24" :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.name" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="性别" field="sex" span="12" :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.sex" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="年龄" field="age" span="12" :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.age" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item align="center" span="24" :item-render="{}">
        <template #default>
          <vxe-button type="submit" status="primary" content="提交"></vxe-button>
          <vxe-button type="reset" content="重置"></vxe-button>
        </template>
      </vxe-form-item>
    </vxe-form>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { VxeUI, VxeFormInstance, VxeFormEvents } from 'vxe-pc-ui'

interface FormDataVO {
  name: string
  nickname: string
  sex: string
  age: string
}

const formRef = ref<VxeFormInstance<FormDataVO>>()

const formData = ref<FormDataVO>({
  name: 'test1',
  nickname: 'Testing',
  sex: '',
  age: ''
})

const changeEvent = (params: any) => {
  const $form = formRef.value
  if ($form) {
    $form.updateStatus(params)
  }
}

const submitEvent: VxeFormEvents.Submit = () => {
  VxeUI.modal.message({ content: '保存成功', status: 'success' })
}

const resetEvent: VxeFormEvents.Reset = () => {
  VxeUI.modal.message({ content: '重置事件', status: 'info' })
}
</script>

### 标题对齐方式
<template>
  <div>
    <vxe-form
      ref="formRef"
      title-align="left"
      title-width="120"
      :data="formData"
      @submit="submitEvent"
      @reset="resetEvent">
      <vxe-form-item title="名称" field="name" span="24" :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.name" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="性别" field="sex" span="12" :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.sex" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="年龄" field="age" span="12" :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.age" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item align="center" span="24" :item-render="{}">
        <template #default>
          <vxe-button type="submit" status="primary" content="提交"></vxe-button>
          <vxe-button type="reset" content="重置"></vxe-button>
        </template>
      </vxe-form-item>
    </vxe-form>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { VxeUI, VxeFormInstance, VxeFormEvents } from 'vxe-pc-ui'

interface FormDataVO {
  name: string
  nickname: string
  sex: string
  age: string
}

const formRef = ref<VxeFormInstance<FormDataVO>>()

const formData = ref<FormDataVO>({
  name: 'test1',
  nickname: 'Testing',
  sex: '',
  age: ''
})

const changeEvent = (params: any) => {
  const $form = formRef.value
  if ($form) {
    $form.updateStatus(params)
  }
}

const submitEvent: VxeFormEvents.Submit = () => {
  VxeUI.modal.message({ content: '保存成功', status: 'success' })
}

const resetEvent: VxeFormEvents.Reset = () => {
  VxeUI.modal.message({ content: '重置事件', status: 'info' })
}
</script>

### 内容对齐方式
<template>
  <div>
    <vxe-form
      :data="formData">
      <vxe-form-item title="名称" field="name" span="24">
        <template #default>
          <span>{{ formData.name }}</span>
        </template>
      </vxe-form-item>
    </vxe-form>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'

interface FormDataVO {
  name: string
  nickname: string
  sex: string
  age: string
}

const formData = ref<FormDataVO>({
  name: 'test1',
  nickname: 'Testing',
  sex: '',
  age: ''
})
</script>

### 溢出隐藏
<template>
  <div>
    <vxe-form
      ref="formRef"
      title-colon
      title-width="120"
      title-align="right"
      :data="formData"
      @submit="submitEvent"
      @reset="resetEvent">
      <vxe-form-item title="太长，超出自动换行太长，超出自动换行" field="name" span="24" :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.name" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="性别" field="sex" span="12" :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.sex" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="年龄" field="age" span="12" :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.age" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item align="center" span="24" :item-render="{}">
        <template #default>
          <vxe-button type="submit" status="primary" content="提交"></vxe-button>
          <vxe-button type="reset" content="重置"></vxe-button>
        </template>
      </vxe-form-item>
    </vxe-form>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { VxeUI, VxeFormInstance, VxeFormEvents } from 'vxe-pc-ui'

interface FormDataVO {
  name: string
  nickname: string
  sex: string
  age: string
}

const formRef = ref<VxeFormInstance<FormDataVO>>()

const formData = ref<FormDataVO>({
  name: 'test1',
  nickname: 'Testing',
  sex: '',
  age: ''
})

const changeEvent = (params: any) => {
  const $form = formRef.value
  if ($form) {
    $form.updateStatus(params)
  }
}

const submitEvent: VxeFormEvents.Submit = () => {
  VxeUI.modal.message({ content: '保存成功', status: 'success' })
}

const resetEvent: VxeFormEvents.Reset = () => {
  VxeUI.modal.message({ content: '重置事件', status: 'info' })
}
</script>

### 标题前缀图标
<template>
  <div>
    <vxe-form
      ref="formRef"
      :data="formData"
      @submit="submitEvent"
      @reset="resetEvent">
      <vxe-form-item title="名称" field="name" span="24" :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.name" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="性别" field="sex" span="12" :item-render="{}" :title-prefix="{ content: '左边图标', icon: 'vxe-icon-user-fill' }">
        <template #default="params">
          <vxe-input v-model="formData.sex" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="年龄" field="age" span="12" :item-render="{}" :title-prefix="{ content: '右边图标', icon: 'vxe-icon-warning-triangle', iconStatus: 'error' }">
        <template #default="params">
          <vxe-input v-model="formData.age" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item align="center" span="24" :item-render="{}">
        <template #default>
          <vxe-button type="submit" status="primary" content="提交"></vxe-button>
          <vxe-button type="reset" content="重置"></vxe-button>
        </template>
      </vxe-form-item>
    </vxe-form>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { VxeUI, VxeFormInstance, VxeFormEvents } from 'vxe-pc-ui'

interface FormDataVO {
  name: string
  nickname: string
  sex: string
  age: string
}

const formRef = ref<VxeFormInstance<FormDataVO>>()

const formData = ref<FormDataVO>({
  name: 'test1',
  nickname: 'Testing',
  sex: '',
  age: ''
})

const changeEvent = (params: any) => {
  const $form = formRef.value
  if ($form) {
    $form.updateStatus(params)
  }
}

const submitEvent: VxeFormEvents.Submit = () => {
  VxeUI.modal.message({ content: '保存成功', status: 'success' })
}

const resetEvent: VxeFormEvents.Reset = () => {
  VxeUI.modal.message({ content: '重置事件', status: 'info' })
}
</script>

### 标题后缀图标
<template>
  <div>
    <vxe-form
      ref="formRef"
      :data="formData"
      @submit="submitEvent"
      @reset="resetEvent">
      <vxe-form-item title="名称" field="name" span="24" :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.name" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="性别" field="sex" span="12" :item-render="{}" :title-suffix="{ content: '右边图标', icon: 'vxe-icon-user-fill' }">
        <template #default="params">
          <vxe-input v-model="formData.sex" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="年龄" field="age" span="12" :item-render="{}" :title-suffix="{ content: '右边图标', icon: 'vxe-icon-warning-triangle', iconStatus: 'error' }">
        <template #default="params">
          <vxe-input v-model="formData.age" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item align="center" span="24" :item-render="{}">
        <template #default>
          <vxe-button type="submit" status="primary" content="提交"></vxe-button>
          <vxe-button type="reset" content="重置"></vxe-button>
        </template>
      </vxe-form-item>
    </vxe-form>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { VxeUI, VxeFormInstance, VxeFormEvents } from 'vxe-pc-ui'

interface FormDataVO {
  name: string
  nickname: string
  sex: string
  age: string
}

const formRef = ref<VxeFormInstance<FormDataVO>>()

const formData = ref<FormDataVO>({
  name: 'test1',
  nickname: 'Testing',
  sex: '',
  age: ''
})

const changeEvent = (params: any) => {
  const $form = formRef.value
  if ($form) {
    $form.updateStatus(params)
  }
}

const submitEvent: VxeFormEvents.Submit = () => {
  VxeUI.modal.message({ content: '保存成功', status: 'success' })
}

const resetEvent: VxeFormEvents.Reset = () => {
  VxeUI.modal.message({ content: '重置事件', status: 'info' })
}
</script>

### 边框
<template>
  <div>
    <vxe-form
      border
      vertical-align="center"
      title-width="100"
      :data="formData">
      <vxe-form-item title="名称" field="name" span="12"></vxe-form-item>
      <vxe-form-item title="昵称" field="nickname" span="12"></vxe-form-item>
      <vxe-form-item title="性别" field="sex" span="12"></vxe-form-item>
      <vxe-form-item title="年龄" field="age" span="12"></vxe-form-item>
    </vxe-form>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'

interface FormDataVO {
  name: string
  nickname: string
  sex: string
  age: string
}

const formData = ref<FormDataVO>({
  name: 'test1',
  nickname: 'Testing',
  sex: '女',
  age: '22'
})
</script>

### 标题背景
<template>
  <div>
    <vxe-form
      border
      title-background
      vertical-align="center"
      title-width="100"
      :data="formData">
      <vxe-form-item title="名称" field="name" span="12"></vxe-form-item>
      <vxe-form-item title="昵称" field="nickname" span="12"></vxe-form-item>
      <vxe-form-item title="性别" field="sex" span="12"></vxe-form-item>
      <vxe-form-item title="年龄" field="age" span="12"></vxe-form-item>
    </vxe-form>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'

interface FormDataVO {
  name: string
  nickname: string
  sex: string
  age: string
}

const formData = ref<FormDataVO>({
  name: 'test1',
  nickname: 'Testing',
  sex: '女',
  age: '22'
})
</script>

### 格式化内容
<template>
  <div>
    <vxe-form border title-background :title-width="100" :data="formData">
      <vxe-form-item title="名称" field="name" span="12"></vxe-form-item>
      <vxe-form-item title="性别" field="sex" span="12" :formatter="formatSex"></vxe-form-item>
      <vxe-form-item title="银行卡" field="bankCard" span="12" :formatter="formatBankCard"></vxe-form-item>
      <vxe-form-item title="日期" field="date" span="12" :formatter="formatDate"></vxe-form-item>
      <vxe-form-item title="金额" field="amount" span="12" :formatter="formatAmount"></vxe-form-item>
    </vxe-form>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { VxeFormItemPropTypes } from 'vxe-pc-ui'
import XEUtils from 'xe-utils'

interface FormDataVO {
  name: string
  nickname: string
  bankCard: string
  sex: string
  date: string
  amount: number
  age: string
}

const formData = ref<FormDataVO>({
  name: 'test1',
  nickname: 'Testing',
  bankCard: '****************',
  sex: '1',
  date: '2022-10-24T08:14:18.000Z',
  amount: ********,
  age: '54'
})

const sexList = [
  { label: '女', value: '0' },
  { label: '男', value: '1' }
]

const formatSex: VxeFormItemPropTypes.Formatter<FormDataVO> = ({ itemValue }) => {
  const item = sexList.find(item => item.value === itemValue)
  return item ? item.label : itemValue
}

const formatBankCard: VxeFormItemPropTypes.Formatter<FormDataVO> = ({ itemValue }) => {
  return XEUtils.commafy(XEUtils.toValueString(itemValue), { spaceNumber: 4, separator: ' ' })
}

const formatDate: VxeFormItemPropTypes.Formatter<FormDataVO> = ({ itemValue }) => {
  return XEUtils.toDateString(itemValue, 'yyyy-MM-dd')
}

const formatAmount: VxeFormItemPropTypes.Formatter<FormDataVO> = ({ itemValue }) => {
  return XEUtils.commafy(XEUtils.toNumber(itemValue), { digits: 2 })
}
</script>


### 分组/分行/分列
<template>
  <div>
    <vxe-form
      ref="formRef"
      :data="formData"
      @submit="submitEvent"
      @reset="resetEvent">
      <vxe-form-group span="24" title="分组1" title-bold vertical>
        <vxe-form-item title="名称" field="name" span="12" :item-render="{}">
          <template #default="params">
            <vxe-input v-model="formData.name" @change="changeEvent(params)"></vxe-input>
          </template>
        </vxe-form-item>
        <vxe-form-item title="性别" field="sex" span="12" :item-render="{}">
          <template #default="params">
            <vxe-input v-model="formData.sex" @change="changeEvent(params)"></vxe-input>
          </template>
        </vxe-form-item>
        <vxe-form-item title="年龄" field="age" span="24" :item-render="{}">
          <template #default="params">
            <vxe-number-input v-model="formData.age" @change="changeEvent(params)"></vxe-number-input>
          </template>
        </vxe-form-item>
        <vxe-form-item title="生日" field="birthday" span="24" :item-render="{}">
          <template #default="params">
            <vxe-date-picker v-model="formData.birthday" @change="changeEvent(params)"></vxe-date-picker>
          </template>
        </vxe-form-item>
      </vxe-form-group>
      <vxe-form-group span="24" title="分组2" title-bold vertical>
        <vxe-form-item title="昵称" field="nickname" span="24" :item-render="{}">
          <template #default="params">
            <vxe-input v-model="formData.nickname" @change="changeEvent(params)"></vxe-input>
          </template>
        </vxe-form-item>
      </vxe-form-group>
      <vxe-form-group span="24" title="分组3" title-bold vertical>
        <vxe-form-item title="描述" field="describe" span="24" :item-render="{}">
          <template #default="params">
            <vxe-textarea v-model="formData.describe" @change="changeEvent(params)"></vxe-textarea>
          </template>
        </vxe-form-item>
      </vxe-form-group>
      <vxe-form-item align="center" span="24" :item-render="{}">
        <template #default>
          <vxe-button type="submit" status="primary" content="提交"></vxe-button>
          <vxe-button type="reset" content="重置"></vxe-button>
        </template>
      </vxe-form-item>
    </vxe-form>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { VxeUI, VxeFormInstance, VxeFormEvents } from 'vxe-pc-ui'

interface FormDataVO {
  name: string
  nickname: string
  sex: string
  age: number
  birthday: string
  describe: string
}

const formRef = ref<VxeFormInstance<FormDataVO>>()

const formData = ref<FormDataVO>({
  name: '',
  nickname: '',
  sex: '0',
  age: 22,
  birthday: '',
  describe: ''
})

const changeEvent = (params: any) => {
  const $form = formRef.value
  if ($form) {
    $form.updateStatus(params)
  }
}

const submitEvent: VxeFormEvents.Submit = () => {
  VxeUI.modal.message({ content: '保存成功', status: 'success' })
}

const resetEvent: VxeFormEvents.Reset = () => {
  VxeUI.modal.message({ content: '重置事件', status: 'info' })
}
</script>

### 自定义布局
<template>
  <div>
    <vxe-form
      ref="formRef"
      custom-layout
      :data="formData"
      :rules="formRules"
      @submit="submitEvent"
      @reset="resetEvent">
      <div style="width: 100%">
        <div style="color: red;">自定义内容</div>
        <vxe-form-item title="名称" field="name" span="24" :item-render="{}">
          <template #default="params">
            <vxe-input v-model="formData.name" @change="changeEvent(params)"></vxe-input>
          </template>
        </vxe-form-item>
      </div>
      <div style="width: 100%;height: 180px;padding-left: 30px;">
        <vxe-form-item title="性别" field="sex" span="12" :item-render="{}">
          <template #default="params">
            <vxe-input v-model="formData.sex" @change="changeEvent(params)"></vxe-input>
          </template>
        </vxe-form-item>
        <div style="color: red;">自定义内容</div>
        <vxe-form-item title="角色" field="role" span="12" :item-render="{}">
          <template #default="params">
            <vxe-input v-model="formData.role" @change="changeEvent(params)"></vxe-input>
          </template>
        </vxe-form-item>
        <vxe-form-item title="年龄" field="age" span="12" :item-render="{}">
          <template #default="params">
            <vxe-number-input v-model="formData.age" @change="changeEvent(params)"></vxe-number-input>
          </template>
        </vxe-form-item>
      </div>
      <div style="width: 100%">
        <vxe-button type="submit" status="primary" content="提交"></vxe-button>
        <vxe-button type="reset" content="重置"></vxe-button>
      </div>
    </vxe-form>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { VxeUI, VxeFormPropTypes, VxeFormInstance, VxeFormEvents } from 'vxe-pc-ui'

interface FormDataVO {
  name: string
  nickname: string
  role: string
  sex: string
  age: number
}

const formRef = ref<VxeFormInstance<FormDataVO>>()

const formData = ref<FormDataVO>({
  name: 'test1',
  nickname: 'Testing',
  role: '',
  sex: '',
  age: 18
})

const formRules: VxeFormPropTypes.Rules<FormDataVO> = {
  name: [
    { required: true, message: '请输入名称' },
    { min: 3, max: 5, message: '长度在 3 到 5 个字符' }
  ],
  role: [
    { required: true, message: '请输入' }
  ],
  sex: [
    { required: true, message: '请选择性别' }
  ],
  age: [
    { required: true, message: '请输入年龄' },
    {
      validator ({ itemValue }) {
        // 自定义校验
        if (Number(itemValue) > 35 || Number(itemValue) < 18) {
          return new Error('年龄在 18 ~ 35 之间')
        }
      }
    }
  ]
}

const changeEvent = (params: any) => {
  const $form = formRef.value
  if ($form) {
    $form.updateStatus(params)
  }
}

const submitEvent: VxeFormEvents.Submit = () => {
  VxeUI.modal.message({ content: '保存成功', status: 'success' })
}

const resetEvent: VxeFormEvents.Reset = () => {
  VxeUI.modal.message({ content: '重置事件', status: 'info' })
}
</script>

## 折叠表单

### 展开与收起
<template>
  <div>
    <vxe-form
      title-colon
      ref="formRef"
      title-width="100"
      title-align="right"
      :data="formData"
      @submit="submitEvent">
      <vxe-form-item title="名称" field="name" span="12" :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.name" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="昵称" field="nickname" span="12" :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.nickname" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="角色" field="role" span="12" folding :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.role" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="性别" field="sex" span="12" folding :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.sex" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="年龄" field="age" span="12" folding :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.age" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="大小" field="num" span="12" folding :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.num" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="创建时间" field="createDate" span="12" folding :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.createDate" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="更新时间" field="updateDate" span="12" folding :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.updateDate" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item align="center" span="24" collapse-node :item-render="{}">
        <template #default>
          <vxe-button type="submit" status="primary" content="搜索"></vxe-button>
          <vxe-button type="reset" content="重置"></vxe-button>
        </template>
      </vxe-form-item>
    </vxe-form>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { VxeUI, VxeFormInstance, VxeFormEvents } from 'vxe-pc-ui'

interface FormDataVO {
  name: string
  role: string
  nickname: string
  sex: string
  age: string
  num: string
  createDate: string
  updateDate: string
}

const formRef = ref<VxeFormInstance<FormDataVO>>()

const formData = ref<FormDataVO>({
  name: 'test1',
  role: '',
  nickname: 'Testing',
  sex: '',
  age: '',
  num: '',
  createDate: '',
  updateDate: ''
})

const changeEvent = (params: any) => {
  const $form = formRef.value
  if ($form) {
    $form.updateStatus(params)
  }
}

const submitEvent: VxeFormEvents.Submit = () => {
  VxeUI.modal.message({ content: '保存成功', status: 'success' })
}
</script>

### 自定义按钮文字
<template>
  <div>
    <vxe-form
      title-colon
      ref="formRef"
      title-width="100"
      title-align="right"
      :collapse-config="{ foldButtonText: '收起条件区', unfoldButtonText: '展开条件区' }"
      :data="formData"
      @submit="submitEvent">
      <vxe-form-item title="名称" field="name" span="12" :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.name" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="昵称" field="nickname" span="12" :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.nickname" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="角色" field="role" span="12" folding :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.role" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="性别" field="sex" span="12" folding :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.sex" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="年龄" field="age" span="12" folding :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.age" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="大小" field="num" span="12" folding :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.num" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="创建时间" field="createDate" span="12" folding :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.createDate" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="更新时间" field="updateDate" span="12" folding :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.updateDate" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item align="center" span="24" collapse-node :item-render="{}">
        <template #default>
          <vxe-button type="submit" status="primary" content="搜索"></vxe-button>
          <vxe-button type="reset" content="重置"></vxe-button>
        </template>
      </vxe-form-item>
    </vxe-form>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { VxeUI, VxeFormInstance, VxeFormEvents } from 'vxe-pc-ui'

interface FormDataVO {
  name: string
  role: string
  nickname: string
  sex: string
  age: string
  num: string
  createDate: string
  updateDate: string
}

const formRef = ref<VxeFormInstance<FormDataVO>>()

const formData = ref<FormDataVO>({
  name: 'test1',
  role: '',
  nickname: 'Testing',
  sex: '',
  age: '',
  num: '',
  createDate: '',
  updateDate: ''
})

const changeEvent = (params: any) => {
  const $form = formRef.value
  if ($form) {
    $form.updateStatus(params)
  }
}

const submitEvent: VxeFormEvents.Submit = () => {
  VxeUI.modal.message({ content: '保存成功', status: 'success' })
}
</script>

### 自定义按钮图标
<template>
  <div>
    <vxe-form
      title-colon
      ref="formRef"
      title-width="100"
      title-align="right"
      :collapse-config="{ foldButtonText: '关闭', unfoldButtonText: '更多', foldIcon: 'vxe-icon-funnel', unfoldIcon: 'vxe-icon-error-circle' }"
      :data="formData"
      @submit="submitEvent">
      <vxe-form-item title="名称" field="name" span="12" :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.name" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="昵称" field="nickname" span="12" :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.nickname" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="角色" field="role" span="12" folding :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.role" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="性别" field="sex" span="12" folding :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.sex" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="年龄" field="age" span="12" folding :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.age" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="大小" field="num" span="12" folding :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.num" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="创建时间" field="createDate" span="12" folding :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.createDate" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="更新时间" field="updateDate" span="12" folding :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.updateDate" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item align="center" span="24" collapse-node :item-render="{}">
        <template #default>
          <vxe-button type="submit" status="primary" content="搜索"></vxe-button>
          <vxe-button type="reset" content="重置"></vxe-button>
        </template>
      </vxe-form-item>
    </vxe-form>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { VxeUI, VxeFormInstance, VxeFormEvents } from 'vxe-pc-ui'

interface FormDataVO {
  name: string
  role: string
  nickname: string
  sex: string
  age: string
  num: string
  createDate: string
  updateDate: string
}

const formRef = ref<VxeFormInstance<FormDataVO>>()

const formData = ref<FormDataVO>({
  name: 'test1',
  role: '',
  nickname: 'Testing',
  sex: '',
  age: '',
  num: '',
  createDate: '',
  updateDate: ''
})

const changeEvent = (params: any) => {
  const $form = formRef.value
  if ($form) {
    $form.updateStatus(params)
  }
}

const submitEvent: VxeFormEvents.Submit = () => {
  VxeUI.modal.message({ content: '保存成功', status: 'success' })
}
</script>

## 数据校验
### 校验规则配置
### 字符串校验规则
### 数值校验规则
### 正则校验规则
### 数组校验规则
### 自定义校验规则
### 错误提示样式
### 手动调用


