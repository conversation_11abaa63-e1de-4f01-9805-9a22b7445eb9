Welcome to Vue Flow! ​
======================

Vue Flow is a bridge to the world of interactive flowcharts and graphs, empowering you to bring dynamism and interactivity to flowcharts and graphic representations. Whether it's crafting personal diagrams, generating dynamic editors, or anything else your imagination conjures up, Vue Flow is your creative companion.

Vue Flow makes it effortless to customize and extend basic functionalities by allowing the integration of your own bespoke nodes and edges. Additional components such as Background, Minimap, and Controls further enrich the interface, transforming your creations into engaging platforms.

Play Online ​
-------------

Try out the sandbox starter templates for Vue Flow in JavaScript and TypeScript and get a feel for the library.

### new.vueflow.dev/js### new.vueflow.dev/tsKey Features ​
--------------

### Seamless Setup ​

Vue Flow gets you into the action quickly. With built\-in features like element dragging, zooming and panning, and selection, Vue Flow is ready to go right out of the box.

### Customizable ​

Vue Flow is yours to shape. From custom nodes and edges to connection lines, you can extend the functionality of Vue Flow to fit your creative needs.

### Efficient and Responsive ​

Changes are tracked reactively by Vue Flow, ensuring that only the necessary elements are re\-rendered.

### Utilities and Composability ​

Vue Flow is designed for complex uses, with built\-in graph helper and state composable functions.

### Additional Components ​

Vue Flow comes with supplementary components to enhance the user interface.

* Background: Vue Flow offers two built\-in patterns, with further configurations likeheight, width, or color for personalizations.
* MiniMap: This feature provides a birds\-eye view of your nodes in a small map,located at the bottom\-right corner.
* Controls: Vue Flow lets you handle zoom functions from a control panel on the bottomleft.
* NodeToolbar: Get access to essential tools directly from the Node itself for easy manipulation and control of the Node
* NodeResizer: Seamlessly adjust the size of your Node to fit your needs and preferences.

### Reliable ​

Vue Flow is written fully in TypeScript, ensuring a reliable and secure experience for developers.

PagerNext pageGetting Started
