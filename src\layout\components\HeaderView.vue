<template>
  <div class="page-header">
    <!-- Left side -->
    <div class="header-left">
      <vxe-button
        class="collapseBtn"
        mode="text"
        :icon="appStore.collapseAside ? 'vxe-icon-menu-unfold' : 'vxe-icon-menu-fold'"
        @click="appStore.toggleCollapseAside()"
      ></vxe-button>
    </div>

    <!-- Right side -->
    <div v-if="userInfo" class="header-right">
      <!-- Version link -->
      <span class="right-item">
        <vxe-link status="primary" href="https://github.com/x-extends/vxe-admin-template" target="_blank">
          项目文档
        </vxe-link>
      </span>

      <!-- External links -->
      <span class="right-item external-links">
        <vxe-link icon="vxe-icon-github-fill" href="https://github.com/x-extends/vxe-admin-template" target="_blank" class="github-link">
          Github
        </vxe-link>
        <vxe-link href="https://gitee.com/x-extends/vxe-admin-template" target="_blank">
          <vxe-icon status="error" name="gitee-fill"></vxe-icon>
          <span>Gitee</span>
        </vxe-link>
      </span>

      <!-- Language selector -->
      <span class="right-item">
        <vxe-pulldown :options="langPullList" trigger="click" @option-click="langOptionClickEvent">
          <template #default>
            <vxe-button mode="text">
              <template #icon>
                <vxe-icon name="language"></vxe-icon>
              </template>
              <span class="right-item-title">{{ langLabel }}</span>
            </vxe-button>
          </template>
        </vxe-pulldown>
      </span>

      <!-- Theme selector -->
      <span class="right-item">
        <vxe-pulldown trigger="click">
          <template #default>
            <vxe-button mode="text">
              <template #icon>
                <vxe-icon :name="currTheme === 'dark' ? 'moon' : 'sun'"></vxe-icon>
              </template>
              <span class="right-item-title">{{ currTheme === 'dark' ? '暗黑' : '明亮' }}</span>
            </vxe-button>
          </template>
          <template #dropdown>
            <div class="theme-dropdown">
              <div class="theme-section-title">主题模式</div>
              <vxe-radio-group v-model="currTheme" class="theme-radio-group">
                <vxe-radio label="light">明亮</vxe-radio>
                <vxe-radio label="dark">暗黑</vxe-radio>
              </vxe-radio-group>
              <div class="theme-section-title">主题颜色</div>
              <div class="color-picker">
                <div
                  v-for="color in colorList"
                  :key="color"
                  class="color-item"
                  :style="{ backgroundColor: color }"
                  @click="currPrimaryColor = color"
                ></div>
              </div>
              <div class="theme-section-title size-title">组件尺寸</div>
              <vxe-select v-model="currCompSize" :options="sizeOptions" class="size-select"></vxe-select>
            </div>
          </template>
        </vxe-pulldown>
      </span>

      <!-- User dropdown -->
      <span class="right-item">
        <vxe-pulldown :options="userPullList" trigger="click" @option-click="userOptionClickEvent">
          <template #default>
            <div class="user-avatar">
              <img class="user-picture" :src="userInfo.pictureUrl || 'https://avatars.githubusercontent.com/u/1?v=4'" />
              <span class="user-name">{{ userInfo.name }}</span>
            </div>
          </template>
        </vxe-pulldown>
      </span>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAppStore } from '@/stores/app'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const appStore = useAppStore()
const userStore = useUserStore()

const userInfo = computed(() => userStore.userInfo)

// Language options
const langPullList = ref([
  { label: '中文', value: 'zh-CN' },
  { label: '英文', value: 'en-US' }
])

const langLabel = computed(() => {
  const item = langPullList.value.find(item => item.value === appStore.language)
  return item ? item.label : appStore.language
})

// User dropdown options
const userPullList = ref([
  { label: '退出登录', value: 'logout' }
])

// Theme settings
const currTheme = computed({
  get() {
    return appStore.theme
  },
  set(name) {
    appStore.setTheme(name)
  }
})

const currPrimaryColor = computed({
  get() {
    return appStore.primaryColor
  },
  set(color) {
    appStore.setPrimaryColor(color || '')
  }
})

const currCompSize = computed({
  get() {
    return appStore.componentsSize
  },
  set(size) {
    appStore.setComponentsSize(size)
  }
})

// Color options
const colorList = ref([
  '#409eff', '#29D2F8', '#31FC49', '#3FF2B3', '#B52DFE',
  '#FC3243', '#FA3077', '#D1FC44', '#FEE529', '#FA9A2C'
])

// Size options
const sizeOptions = ref([
  { label: '默认', value: '' },
  { label: '中', value: 'medium' },
  { label: '小', value: 'small' },
  { label: '迷你', value: 'mini' }
])

// Logout handler
const logoutEvent = () => {
  userStore.logout().then(() => {
    router.push('/login')
  })
}

// Event handlers
const langOptionClickEvent = ({ option }) => {
  appStore.setLanguage(option.value)
}

const userOptionClickEvent = ({ option }) => {
  if (option.value === 'logout') {
    logoutEvent()
  }
}
</script>

<style scoped>
.page-header {
  height: 3.5rem;
  display: flex;
  align-items: center;
  padding: 0 1rem;
  background-color: white;
}

.header-left {
  flex-grow: 1;
}

.header-right {
  display: flex;
  align-items: center;
}

.right-item {
  margin-left: 1.5rem;
  cursor: pointer;
}

.right-item-title {
  vertical-align: middle;
}

.external-links {
  display: flex;
  align-items: center;
}

.github-link {
  margin-right: 0.75rem;
}

.theme-dropdown {
  padding: 0.75rem;
}

.theme-section-title {
  margin-bottom: 0.5rem;
}

.theme-radio-group {
  margin-bottom: 0.75rem;
}

.color-picker {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.color-item {
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 0.25rem;
  cursor: pointer;
  border: 1px solid #e5e7eb;
}

.size-title {
  margin-top: 0.75rem;
}

.size-select {
  width: 100%;
}

.user-avatar {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.user-picture {
  width: 1.5rem;
  height: 1.5rem;
  margin-left: 0.125rem;
  margin-right: 0.125rem;
  border-radius: 9999px;
}

.user-name {
  margin-left: 0.25rem;
  vertical-align: middle;
}

.collapseBtn {
  font-size: 1.125rem;
  color: #4b5563;
}

.collapseBtn:hover {
  color: #1f2937;
}
</style>
