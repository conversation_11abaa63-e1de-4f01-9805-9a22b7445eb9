<template>
    <grid-layout ref="gridLayoutRef" v-bind="$attrs" :row-height="computedRowHeight" 
        :class="{ 'layout-container': fullscreen }"
        @layout-before-mount="$emit('layout-before-mount', $event)" 
        @layout-mounted="$emit('layout-mounted', $event)"
        @layout-updated="$emit('layout-updated', $event)" 
        @layout-ready="$emit('layout-ready', $event)"
        @update:layout="$emit('update:layout', $event)" 
        @breakpoint-changed="$emit('breakpoint-changed', $event)"
        @size-change="$emit('size-change', $event)">
        <slot></slot>
    </grid-layout>
</template>

<script lang="ts" setup>
import { GridLayout } from 'grid-layout-plus'
import { gridProps, gridEmits } from '../../types/layout/grid'
import { computed, ref, onMounted, onUnmounted } from 'vue'

interface GridLayoutProps {
    fullscreen?: boolean
    rowHeight?: number
}

const props = withDefaults(defineProps<GridLayoutProps>(), {
    fullscreen: false,
    rowHeight: undefined
})

// 定义所有事件
const emit = defineEmits([
    ...gridEmits,
    'size-change'  // 添加新的尺寸变化事件
])

const gridLayoutRef = ref()
const parentHeight = ref(0)

 
const computedRowHeight = computed(() => {
    // 如果传入了 rowHeight，则优先使用传入的值
    if (props.rowHeight) {
        return props.rowHeight
    }
    // 否则在 fullscreen 模式下使用计算的高度
    return props.fullscreen ? parentHeight.value : 100 // 默认值
})

// 更新父容器高度的函数
const updateParentHeight = () => {
    if (!gridLayoutRef.value?.$el?.parentElement || !props.fullscreen) return
    
    const parent = gridLayoutRef.value.$el.parentElement
    const computedStyle = window.getComputedStyle(parent)
    const paddingTop = parseInt(computedStyle.paddingTop)
    const paddingBottom = parseInt(computedStyle.paddingBottom)
    parentHeight.value = parent.clientHeight - paddingTop - paddingBottom
}

// 获取组件尺寸的方法
const getLayoutSize = () => {
    if (!gridLayoutRef.value?.$el) return { width: 0, height: 0 }
    
    const el = gridLayoutRef.value.$el
    return {
        width: el.offsetWidth,
        height: el.offsetHeight
    }
}

// 暴露方法给父组件
defineExpose({
    getLayoutSize
})

let resizeObserver: ResizeObserver | null = null

onMounted(() => {
    if (props.fullscreen) {
        updateParentHeight()
        window.addEventListener('resize', updateParentHeight)
    }
    
    // 添加 ResizeObserver
    if (gridLayoutRef.value?.$el) {
        resizeObserver = new ResizeObserver(() => {
            const size = getLayoutSize()
            emit('size-change', size)  // 现在可以正确触发事件
        })
        resizeObserver.observe(gridLayoutRef.value.$el)
    }
})

onUnmounted(() => {
    if (props.fullscreen) {
        window.removeEventListener('resize', updateParentHeight)
    }
    
    // 清理 ResizeObserver
    if (resizeObserver) {
        resizeObserver.disconnect()
        resizeObserver = null
    }
})
</script>

<style scoped>
:deep(.vue-grid-layout) {
    width: 100%;
    height: 100%;
}

.layout-container {
    height: 100%;
    width: 100%;
    box-sizing: border-box;
}
</style>
