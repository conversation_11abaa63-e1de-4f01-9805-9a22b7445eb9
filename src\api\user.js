import request from '../plugins/axios'
import { mockGenerator } from '../utils/mockGenerator'

/**
 * User login
 * @param {Object} data - username, password
 * @returns {Promise}
 */
export function login(data) {
  // Use mock data in development mode
  if (import.meta.env.DEV && window.__MOCK_USER_LOGIN__) {
    return Promise.resolve(window.__MOCK_USER_LOGIN__(data));
  }

  return request({
    url: '/user/login',
    method: 'post',
    data
  })
}

/**
 * Get user info
 * @returns {Promise}
 */
export function getUserInfo() {
  // Use mock data in development mode
  if (import.meta.env.DEV && window.__MOCK_USER_INFO__) {
    return Promise.resolve(window.__MOCK_USER_INFO__());
  }

  return request({
    url: '/user/info',
    method: 'get'
  })
}

/**
 * User logout
 * @returns {Promise}
 */
export function logout() {
  // In development mode, just return a successful response
  if (import.meta.env.DEV) {
    return Promise.resolve({ code: 200, message: 'Logout successful', data: null });
  }

  return request({
    url: '/user/logout',
    method: 'post'
  })
}

/**
 * 用户类型定义
 * @typedef {Object} UserVO
 * @property {string} _id - 用户ID
 * @property {string} code - 用户编码
 * @property {string} name - 用户名
 * @property {string[]} roleCodes - 关联角色编码列表
 * @property {string} roleCode - 默认角色编码
 * @property {number} roleLevel - 角色级别
 * @property {string} nickname - 昵称
 * @property {string} pictureUrl - 头像URL
 * @property {string} email - 邮箱
 * @property {string} createTime - 创建时间
 * @property {string} updateTime - 更新时间
 */

/**
 * 用户实体对象
 * @type {UserVO}
 */
export const UserVO = {
  _id: '',
  code: '',
  name: '',
  roleCodes: [],
  roleCode: '',
  roleLevel: 0,
  nickname: '',
  pictureUrl: '',
  email: '',
  createTime: '',
  updateTime: ''
};

/**
 * 获取用户列表（分页）
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getPubAdminUserListPage(params) {
  // 使用模拟数据
  if (import.meta.env.DEV && window.__MOCK_USER_LIST_PAGE__) {
    // console.log('params', window.__MOCK_USER_LIST_PAGE__(params));
    return Promise.resolve(window.__MOCK_USER_LIST_PAGE__(params));
  }

  return request({
    url: '/pub/admin/user/listPage',
    method: 'get',
    params
  })
}

/**
 * 删除用户
 * @param {Object} params - 删除参数
 * @returns {Promise}
 */
export function deletePubAdminUserDelete(params) {
  // 使用模拟数据
  if (import.meta.env.DEV && window.__MOCK_USER_DELETE__) {
    return Promise.resolve(window.__MOCK_USER_DELETE__(params));
  }

  return request({
    url: '/pub/admin/user/delete',
    method: 'delete',
    params
  })
}

/**
 * 保存用户（批量）
 * @param {Array} data - 用户数据
 * @returns {Promise}
 */
export function postPubAdminUserSaveBatch(data) {
  // 使用模拟数据
  if (import.meta.env.DEV && window.__MOCK_USER_SAVE_BATCH__) {
    return Promise.resolve(window.__MOCK_USER_SAVE_BATCH__(data));
  }

  return request({
    url: '/pub/admin/user/saveBatch',
    method: 'post',
    data
  })
}

/**
 * 获取角色选项列表
 * @returns {Promise}
 */
export function getPubAdminRoleOptions() {
  // 使用模拟数据
  if (import.meta.env.DEV && window.__MOCK_ROLE_OPTIONS__) {
    return Promise.resolve(window.__MOCK_ROLE_OPTIONS__());
  }

  return request({
    url: '/pub/admin/role/options',
    method: 'get'
  })
}

// 添加模拟数据
if (import.meta.env.DEV) {
  // 模拟用户登录
  window.__MOCK_USER_LOGIN__ = (data) => {
    if (data.username === 'admin' && data.password === '123456') {
      return {
        code: 200,
        message: '登录成功',
        data: {
          token: 'mock-token-admin-12345',
          expire: Date.now() + 24 * 60 * 60 * 1000
        }
      }
    }
    return { code: 401, message: '用户名或密码错误', data: null }
  }

  // 模拟用户信息
  window.__MOCK_USER_INFO__ = () => {
    return {
      code: 200,
      message: '成功',
      data: {
        _id: 'admin-001',
        code: 'admin',
        name: '管理员',
        roleCodes: ['admin', 'editor'],
        roleCode: 'admin',
        roleLevel: 1,
        nickname: '系统管理员',
        pictureUrl: 'https://avatars.githubusercontent.com/u/1?v=4',
        email: '<EMAIL>',
        permissions: ['*:*:*']
      }
    }
  }

  // 模拟用户列表数据
  window.__MOCK_USER_LIST_PAGE__ = (params) => {
    // 使用 mockGenerator 生成10条用户数据
    const userFields = [
      { fieldName: '_id', fieldType: 'string', custom: () => `user-${mockGenerator.generateFieldValue({ fieldType: 'string', length: 6 })}` },
      { fieldName: 'code', fieldType: 'string', length: 8 },
      { fieldName: 'name', fieldType: 'string', length: 5, custom: () => ['管理员', '编辑员', '测试员', '开发员', '运维员', '产品经理', '设计师', '财务', '人事', '总监'][Math.floor(Math.random() * 10)] },
      { fieldName: 'roleCodes', fieldType: 'array', custom: () => {
        const roles = ['admin', 'editor', 'user', 'visitor'];
        const count = Math.floor(Math.random() * 2) + 1; // 1-2个角色
        return roles.sort(() => 0.5 - Math.random()).slice(0, count);
      }},
      { fieldName: 'roleCode', fieldType: 'string', custom: (item) => item.roleCodes[0] },
      { fieldName: 'roleLevel', fieldType: 'number', options: { min: 1, max: 4 } },
      { fieldName: 'nickname', fieldType: 'string', length: 8 },
      { fieldName: 'pictureUrl', fieldType: 'string', custom: () => `https://avatars.githubusercontent.com/u/${Math.floor(Math.random() * 1000) + 1}?v=4` },
      { fieldName: 'email', fieldType: 'string', custom: () => `${mockGenerator.generateFieldValue({ fieldType: 'string', length: 6 })}@example.com` },
      { fieldName: 'createTime', fieldType: 'datetime' },
      { fieldName: 'updateTime', fieldType: 'datetime' }
    ];

    // 生成10条用户数据
    const userList = mockGenerator.generateMockData(userFields, 10);

    // 简单的筛选和排序逻辑
    let result = [...userList]

    // 筛选
    if (params.name) {
      result = result.filter(u => u.name.includes(params.name))
    }
    if (params.nickname) {
      result = result.filter(u => u.nickname.includes(params.nickname))
    }
    if (params.email) {
      result = result.filter(u => u.email.includes(params.email))
    }
    if (params.roleCodes) {
      const codes = params.roleCodes.split(',')
      result = result.filter(u =>
        codes.some(code => u.roleCodes.includes(code))
      )
    }

    // 计算分页
    const total = result.length
    const pageSize = params.pageSize || 10
    const currentPage = params.currentPage || 1
    const start = (currentPage - 1) * pageSize
    const end = start + pageSize

    result = result.slice(start, end)

    return {
      code: 200,
      message: '成功',
      data: {
        records: result,
        total,
        size: pageSize,
        current: currentPage,
        pages: Math.ceil(total / pageSize)
      }
    }
  }

  // 模拟删除用户
  window.__MOCK_USER_DELETE__ = (params) => {
    return {
      code: 200,
      message: '删除成功',
      data: params._id
    }
  }

  // 模拟保存用户
  window.__MOCK_USER_SAVE_BATCH__ = (data) => {
    return {
      code: 200,
      message: '保存成功',
      data: data.map((item, index) => ({
        ...item,
        _id: item._id || `new-user-${Date.now()}-${index}`,
        createTime: item.createTime || new Date().toLocaleString(),
        updateTime: new Date().toLocaleString()
      }))
    }
  }

  // 模拟角色选项
  window.__MOCK_ROLE_OPTIONS__ = () => {
    return {
      code: 200,
      message: '成功',
      data: [
        { label: '管理员', value: 'admin', level: 1 },
        { label: '编辑员', value: 'editor', level: 2 },
        { label: '普通用户', value: 'user', level: 3 },
        { label: '访客', value: 'visitor', level: 4 }
      ]
    }
  }
}