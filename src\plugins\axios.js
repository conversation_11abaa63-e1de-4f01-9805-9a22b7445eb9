import axios from 'axios'
import { getToken } from '../utils/auth'

// Create axios instance
const service = axios.create({
  baseURL: import.meta.env.VITE_BASE_API || '/api',
  timeout: 10000
})

// Request interceptor
service.interceptors.request.use(
  config => {
    // Add token to headers if exists
    const token = getToken()
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
  },
  error => {
    console.error('Request error:', error)
    return Promise.reject(error)
  }
)

// Response interceptor
service.interceptors.response.use(
  response => {
    const res = response.data
    
    // Check if response has error code
    if (res.code && res.code !== 200) {
      console.error('API error:', res.message || 'Error')
      
      // Handle token expired or invalid
      if (res.code === 401 || res.code === 403) {
        // Redirect to login
        window.location.href = '/login'
      }
      return Promise.reject(new Error(res.message || 'Error'))
    } else {
      return res
    }
  },
  error => {
    console.error('Response error:', error)
    return Promise.reject(error)
  }
)

export default service 