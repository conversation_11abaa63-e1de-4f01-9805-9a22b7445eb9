---
description: 
globs: 
alwaysApply: false
---
// .mdc 规则示例，基于 @src 目录结构

@src:
  // 1. 业务视图层
  views:
    expose: true
    allow: [components, api, stores, utils, types, assets]
    description: "页面视图，允许依赖组件、api、状态、工具、类型、资源"

  // 2. 业务组件层
  components:
    expose: true
    allow: [utils, types, assets]
    description: "通用组件，允许依赖工具、类型、资源"

  // 3. 布局相关
  layout:
    expose: true
    allow: [components, utils, types, assets]
    description: "布局相关，允许依赖组件、工具、类型、资源"

  // 3. 模拟数据
  mock:
    expose: true
    allow: [components, utils, types, assets]
    description: "模拟数据, 前期开发阶段模拟返回接口数据"

  // 4. 路由
  router:
    expose: true
    allow: [views, stores, utils]
    description: "路由配置，允许依赖视图、状态、工具"

  // 5. 状态管理
  stores:
    expose: true
    allow: [utils, types]
    description: "状态管理，允许依赖工具、类型"

  // 6. API 层
  api:
    expose: true
    allow: [utils, types]
    description: "接口请求，允许依赖工具、类型"

  // 7. 工具函数
  utils:
    expose: true
    allow: [types]
    description: "工具函数，允许依赖类型"

  // 8. 类型定义
  types:
    expose: true
    allow: []
    description: "类型定义，不依赖其他模块"

  // 9. 静态资源
  assets:
    expose: true
    allow: []
    description: "静态资源，不依赖其他模块"

  // 10. 样式
  styles:
    expose: true
    allow: [assets]
    description: "全局样式，允许依赖资源"

  // 11. 插件
  plugins:
    expose: true
    allow: [utils, types]
    description: "全局插件，允许依赖工具、类型"

  // 12. 入口文件
  main.js:
    expose: false
    allow: [router, stores, plugins, layout, styles]

    description: "项目入口，只能依赖全局配置和布局"