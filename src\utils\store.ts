import { defineStore } from 'pinia';

// 定义 store 的泛型接口，让 values 的值类型完全由用户定义
interface MainState<T = any> {
  values: Map<string, T>;
}

export const useMainStore = defineStore('main', {
  state: (): MainState => ({
    values: new Map(),
  }),
  getters: {
    getValue: (state) => <T>(key: string): T | null => {
      if (localStorage.getItem(key) != null) {
        const value = localStorage.getItem(key);
        return value === '' ? null : JSON.parse(value);
      } else {
        return (state.values.get(key) as T) ?? null;
      }
    },
  },
  actions: {
    setValue<T>(key: string, value: T): void {
      this.values.set(key, value as any);
      localStorage.setItem(key, JSON.stringify(value));
    },
    removeValue(key: string): void {
      this.values.delete(key);
      localStorage.setItem(key, '');
    },
  },
  persist: false
});

// 这一块后面添加泛型提醒,可以把字段进行约束