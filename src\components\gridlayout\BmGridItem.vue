<template>
  <grid-item
    v-bind="itemProps"
    @resize="handleResize"
    @resized="$emit('resized', $event)"
    @move="$emit('move', $event)"
    @moved="$emit('moved', $event)"
    @container-resized="$emit('container-resized', $event)"
  >
    <slot></slot>
  </grid-item>
</template>

<script lang="ts" setup>
import { GridItem } from 'grid-layout-plus'
import { gridItemProps, gridItemEmits } from '../../types/layout/item'
import { computed } from 'vue'

const props = defineProps(gridItemProps)
const emit = defineEmits(gridItemEmits)

const itemProps = computed(() => props)

const handleResize = (i, newH, newW, newHPx, newWPx) => {
  const resizeData = {
    ...itemProps.value,
    i,
    h: newH,
    w: newW,
    heightPx: newHPx,
    widthPx: newWPx
  }
  
  emit('resize', resizeData)
}
</script>

<style scoped>
:deep(.vue-grid-item) {
  overflow: hidden;
  background: #fff;
  border: 1px solid #eee;
  border-radius: 4px;
}

:deep(.vue-grid-item.vue-grid-placeholder) {
  background: #f0f2f5;
  opacity: 0.4;
}
</style>
