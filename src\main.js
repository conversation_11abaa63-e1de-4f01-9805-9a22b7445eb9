import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import { createPinia } from 'pinia'
import 'xe-utils'
import VXETable from 'vxe-table'
import VxeUI from 'vxe-pc-ui'
globalThis.VxeUI = VxeUI;

import './plugins/axios'
import './mock'
import './styles/index.css'

// Import vxe-table styles
import 'vxe-table/lib/style.css'
import 'vxe-pc-ui/lib/style.css'

//引入全局的axios
import axios from "./plugins/axios";

globalThis.axios = axios;

const app = createApp(App)

// Use plugins
app.use(createPinia())
app.use(router)
app.use(VXETable)
app.use(VxeUI)

// 全局store
import {useMainStore} from './utils/store';
const mainStore = useMainStore();
globalThis.mainStore = mainStore;

// 全局dayjs
import dayjs from 'dayjs';
globalThis.dayjs = dayjs;


app.mount('#app')
