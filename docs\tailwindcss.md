TITLE: Styling UI Component with Tailwind CSS in HTML
DESCRIPTION: This snippet provides a standard HTML example of applying Tailwind CSS utility classes to create a card component. It mirrors the JSX example but uses standard `class` attributes, showcasing the direct application of utilities for layout, appearance, spacing, and text styling, including dark mode variants.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/styling-with-utility-classes.mdx#_snippet_1

LANGUAGE: HTML
CODE:
```
<!-- prettier-ignore -->
<div class="mx-auto flex max-w-sm items-center gap-x-4 rounded-xl bg-white p-6 shadow-lg outline outline-black/5 dark:bg-slate-800 dark:shadow-none dark:-outline-offset-1 dark:outline-white/10">
  <img class="size-12 shrink-0" src="/img/logo.svg" alt="ChitChat Logo" />
  <div>
    <div class="text-xl font-medium text-black dark:text-white">ChitChat</div>
    <p class="text-gray-500 dark:text-gray-400">You have a new message!</p>
  </div>
</div>
```

----------------------------------------

TITLE: Styling UI Component with Tailwind CSS in JSX
DESCRIPTION: This snippet demonstrates how to apply Tailwind CSS utility classes directly to HTML elements within a JSX context (like React). It shows the structure of a card component with styling for layout, appearance, spacing, and text, including handling dark mode styles and embedding an SVG.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/styling-with-utility-classes.mdx#_snippet_0

LANGUAGE: JSX
CODE:
```
<div className="mx-auto flex max-w-sm items-center gap-x-4 rounded-xl bg-white p-6 shadow-lg outline outline-black/5 dark:bg-slate-800 dark:shadow-none dark:-outline-offset-1 dark:outline-white/10">
  <svg className="size-12 shrink-0" viewBox="0 0 40 40">
    <defs>
      <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="a">
        <stop stopColor="#2397B3" offset="0%"></stop>
        <stop stopColor="#13577E" offset="100%"></stop>
      </linearGradient>
      <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="b">
        <stop stopColor="#73DFF2" offset="0%"></stop>
        <stop stopColor="#47B1EB" offset="100%"></stop>
      </linearGradient>
    </defs>
    <g fill="none" fillRule="evenodd">
      <path
        d="M28.872 22.096c.084.622.128 1.258.128 1.904 0 7.732-6.268 14-14 14-2.176 0-4.236-.496-6.073-1.382l-6.022 2.007c-1.564.521-3.051-.966-2.53-2.53l2.007-6.022A13.944 13.944 0 0 1 1 24c0-7.331 5.635-13.346 12.81-13.95A9.967 9.967 0 0 0 13 14c0 5.523 4.477 10 10 10a9.955 9.955 0 0 0 5.872-1.904z"
        fill="url(#a)"
        transform="translate(1 1)"
      ></path>
      <path
        d="M35.618 20.073l2.007 6.022c.521 1.564-.966 3.051-2.53 2.53l-6.022-2.007A13.944 13.944 0 0 1 23 28c-7.732 0-14-6.268-14-14S15.268 0 23 0s14 6.268 14 14c0 2.176-.496 4.236-1.382 6.073z"
        fill="url(#b)"
        transform="translate(1 1)"
      ></path>
      <path
        d="M18 17a2 2 0 1 0 0-4 2 2 0 0 0 0 4zM24 17a2 2 0 1 0 0-4 2 2 0 0 0 0 4zM30 17a2 2 0 1 0 0-4 2 2 0 0 0 0 4z"
        fill="#FFF"
      ></path>
    </g>
  </svg>
  <div>
    <div className="text-xl font-medium text-black dark:text-white">ChitChat</div>
    <p className="text-gray-500 dark:text-gray-400">You have a new message!</p>
  </div>
</div>
```

----------------------------------------

TITLE: Adding Viewport Meta Tag in HTML
DESCRIPTION: Required viewport meta tag that should be added to the document head for proper responsive behavior.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/responsive-design.mdx#2025-04-22_snippet_0

LANGUAGE: html
CODE:
```
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
```

----------------------------------------

TITLE: Building Component with Tailwind Utilities (HTML)
DESCRIPTION: Demonstrates building a responsive component using Tailwind CSS utility classes in pure HTML. It shows responsive variants (sm:), spacing (gap), layout (flex, items-center), and state variants (hover:, active:) for styling, serving as the HTML equivalent of the React example.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/styling-with-utility-classes.mdx#_snippet_3

LANGUAGE: html
CODE:
```
<!-- [!code classes:sm:flex-row,sm:py-4,sm:gap-6,sm:mx-0,sm:shrink-0,sm:text-left,sm:items-center] -->
<!-- [!code classes:hover:text-white,hover:bg-purple-600,hover:border-transparent,active:bg-purple-700] -->
<div class="flex flex-col gap-2 p-8 sm:flex-row sm:items-center sm:gap-6 sm:py-4 ...">
  <img class="mx-auto block h-24 rounded-full sm:mx-0 sm:shrink-0" src="/img/erin-lindford.jpg" alt="" />
  <div class="space-y-2 text-center sm:text-left">
    <div class="space-y-0.5">
      <p class="text-lg font-semibold text-black">Erin Lindford</p>
      <p class="font-medium text-gray-500">Product Engineer</p>
    </div>
    <!-- prettier-ignore -->
    <button class="border-purple-200 text-purple-600 hover:border-transparent hover:bg-purple-600 hover:text-white active:bg-purple-700 ...">
      Message
    </button>
  </div>
</div>
```

----------------------------------------

TITLE: Using Block and Inline Display Utilities in Tailwind CSS
DESCRIPTION: HTML example showing how to use Tailwind's inline, inline-block, and block utility classes to control text flow and element placement within a paragraph.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/display.mdx#2025-04-22_snippet_1

LANGUAGE: html
CODE:
```
<!-- [!code classes:inline,inline-block,block] -->
<p>
  When controlling the flow of text, using the CSS property <span class="inline">display: inline</span> will cause the
  text inside the element to wrap normally.
</p>
<p>
  While using the property <span class="inline-block">display: inline-block</span> will wrap the element to prevent the
  text inside from extending beyond its parent.
</p>
<p>
  Lastly, using the property <span class="block">display: block</span> will put the element on its own line and fill its
  parent.
</p>
```

----------------------------------------

TITLE: Customizing Breakpoints with Theme Variables in CSS
DESCRIPTION: Demonstrates how to customize Tailwind breakpoints using theme variables. Shows setting custom breakpoint sizes and creating new breakpoints using rem units.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/responsive-design.mdx#2025-04-22_snippet_7

LANGUAGE: css
CODE:
```
@import "tailwindcss";

@theme {
  --breakpoint-xs: 30rem;
  --breakpoint-2xl: 100rem;
  --breakpoint-3xl: 120rem;
}
```

----------------------------------------

TITLE: Tailwind CSS Hover State Implementation
DESCRIPTION: Illustrates how Tailwind CSS implements hover states using separate classes for default and hover states.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#2025-04-22_snippet_2

LANGUAGE: css
CODE:
```
.bg-sky-500 {
  background-color: #0ea5e9;
}

.hover\:bg-sky-700:hover {
  background-color: #0369a1;
}
```

----------------------------------------

TITLE: Defining Font Family Utility Classes in Tailwind CSS
DESCRIPTION: This example demonstrates basic usage of font family utility classes in Tailwind CSS, including font-sans, font-serif, and font-mono.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/font-family.mdx#2025-04-22_snippet_1

LANGUAGE: html
CODE:
```
<p class="font-sans ...">The quick brown fox ...</p>
<p class="font-serif ...">The quick brown fox ...</p>
<p class="font-mono ...">The quick brown fox ...</p>
```

----------------------------------------

TITLE: Using Grid Display Utility in Tailwind CSS
DESCRIPTION: HTML example showing how to use the grid utility to create a CSS grid container, which is useful for two-dimensional layouts with rows and columns.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/display.mdx#2025-04-22_snippet_5

LANGUAGE: html
CODE:
```
<!-- [!code classes:grid] -->
<div class="grid grid-cols-3 grid-rows-3 gap-4">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Dark Mode Toggle with Tailwind CSS
DESCRIPTION: Example of implementing dark mode support based on user preferences using Tailwind's dark variant. This code defines both light and dark color schemes for a card component, with different background, text, and accent colors.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#2025-04-22_snippet_36

LANGUAGE: html
CODE:
```
<div class="bg-white dark:bg-gray-900 ...">
  <!-- ... -->
  <h3 class="text-gray-900 dark:text-white ...">Writes upside-down</h3>
  <p class="text-gray-500 dark:text-gray-400 ...">
    The Zero Gravity Pen can be used to write in any orientation, including upside-down. It even works in outer space.
  </p>
</div>
```

----------------------------------------

TITLE: Implementing Fixed Header with TailwindCSS
DESCRIPTION: Example showing how to create a fixed header in a contacts list using TailwindCSS fixed positioning. The header remains at the top of the viewport while the content scrolls underneath. Uses the fixed utility class along with top-0, right-0, and left-0 for full-width positioning.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/position.mdx#2025-04-22_snippet_4

LANGUAGE: html
CODE:
```
<div class="relative">
  <div class="fixed top-0 right-0 left-0">Contacts</div>
  <div>
    <div>
      <img src="/img/andrew.jpg" />
      <strong>Andrew Alfred</strong>
    </div>
    <div>
      <img src="/img/debra.jpg" />
      <strong>Debra Houston</strong>
    </div>
    <!-- ... -->
  </div>
</div>
```

----------------------------------------

TITLE: Horizontal Margin Example in HTML with Tailwind CSS
DESCRIPTION: Demonstrates the use of the mx-8 utility class to add horizontal margin to an element.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/margin.mdx#2025-04-22_snippet_3

LANGUAGE: HTML
CODE:
```
<div class="mx-8 ...">mx-8</div>
```

----------------------------------------

TITLE: Vertical Margin Example in HTML with Tailwind CSS
DESCRIPTION: Shows how to add vertical margin to an element using the my-8 utility class.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/margin.mdx#2025-04-22_snippet_4

LANGUAGE: HTML
CODE:
```
<div class="my-8 ...">my-8</div>
```

----------------------------------------

TITLE: Installing Tailwind CSS v4.0
DESCRIPTION: Three-step installation process for Tailwind CSS v4.0, showing how to install the package, add the PostCSS plugin, and import Tailwind in your CSS file.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4/index.mdx#2025-04-22_snippet_1

LANGUAGE: shell
CODE:
```
npm i tailwindcss @tailwindcss/postcss;
```

LANGUAGE: javascript
CODE:
```
export default {
  plugins: ["@tailwindcss/postcss"],
};
```

LANGUAGE: css
CODE:
```
@import "tailwindcss";
```

----------------------------------------

TITLE: Responsive Image Width Example
DESCRIPTION: Demonstrates using responsive utility classes to adjust image width across different breakpoints. Width starts at 16 units, increases to 32 at medium screens, and 48 at large screens.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/responsive-design.mdx#2025-04-22_snippet_1

LANGUAGE: html
CODE:
```
<img class="w-16 md:w-32 lg:w-48" src="..." />
```

----------------------------------------

TITLE: Building Component with Tailwind Utilities (React)
DESCRIPTION: Demonstrates building a responsive component using Tailwind CSS utility classes within a React/JSX context. It showcases responsive variants (@sm:), spacing (space-y, gap-x), layout (flex, items-center), and state variants (hover:, active:) for styling.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/styling-with-utility-classes.mdx#_snippet_2

LANGUAGE: javascript
CODE:
```
<div className="mx-auto max-w-sm space-y-2 rounded-xl bg-white px-8 py-8 shadow-lg ring ring-black/5 @sm:flex @sm:items-center @sm:space-y-0 @sm:gap-x-6 @sm:py-4">
  <img
    className="mx-auto block h-24 rounded-full @sm:mx-0 @sm:shrink-0"
    src={erinLindford.src}
    alt="Woman's Face"
  />
  <div className="space-y-2 text-center @sm:text-left">
    <div className="space-y-0.5">
      <p className="text-lg font-semibold text-black">Erin Lindford</p>
      <p className="font-medium text-gray-500">Product Engineer</p>
    </div>
    <button className="rounded-full border border-purple-200 px-4 py-1 text-sm font-semibold text-purple-600 hover:border-transparent hover:bg-purple-600 hover:text-white active:bg-purple-700">
      Message
    </button>
  </div>
</div>
```

----------------------------------------

TITLE: Defining Custom Color Theme Variable in TailwindCSS
DESCRIPTION: Shows how to define a new color theme variable '--color-mint-500' using the @theme directive, which enables the use of related utility classes.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/theme.mdx#2025-04-22_snippet_0

LANGUAGE: css
CODE:
```
@import "tailwindcss";

@theme {
  --color-mint-500: oklch(0.72 0.11 178);
}
```

----------------------------------------

TITLE: CSS-First Theme Configuration
DESCRIPTION: Demonstrates the new CSS-based configuration approach using @theme directive to define custom properties like fonts, breakpoints, colors, and animation easings.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4/index.mdx#2025-04-22_snippet_6

LANGUAGE: css
CODE:
```
@import "tailwindcss";

@theme {
  --font-display: "Satoshi", "sans-serif";

  --breakpoint-3xl: 1920px;

  --color-avocado-100: oklch(0.99 0 0);
  --color-avocado-200: oklch(0.98 0.04 113.22);
  --color-avocado-300: oklch(0.94 0.11 115.03);
  --color-avocado-400: oklch(0.92 0.19 114.08);
  --color-avocado-500: oklch(0.84 0.18 117.33);
  --color-avocado-600: oklch(0.53 0.12 118.34);

  --ease-fluid: cubic-bezier(0.3, 0, 0, 1);
  --ease-snappy: cubic-bezier(0.2, 0, 0, 1);

  /* ... */
}
```

----------------------------------------

TITLE: Using size-* Utilities for Equal Dimensions
DESCRIPTION: Demonstrates the new size-* utility that sets both width and height simultaneously, simplifying common sizing patterns.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-4/index.mdx#2025-04-22_snippet_8

LANGUAGE: html
CODE:
```
<div>
  <img class="size-10" ...>
  <img class="size-12" ...>
  <img class="size-14" ...>
</div>
```

----------------------------------------

TITLE: Implementing Row Direction with Tailwind CSS Flex Utilities
DESCRIPTION: Example showing how to use flex-row to position flex items horizontally in the same direction as text. This creates a row layout with items flowing from left to right.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/flex-direction.mdx#2025-04-22_snippet_0

LANGUAGE: html
CODE:
```
<!-- [!code classes:flex-row] -->
<div class="flex flex-row ...">
  <div>01</div>
  <div>02</div>
  <div>03</div>
</div>
```

----------------------------------------

TITLE: Building Form with Fieldset and Disabled State (React/JSX)
DESCRIPTION: Provides an example of building a shipping details form using `Fieldset`, `Legend`, `Field`, `Label`, `Input`, and `Select` components. It illustrates how to dynamically disable a `Field` based on state (`country` selection) and how the `data-disabled` attribute is exposed for styling.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/headless-ui-v2/index.mdx#_snippet_5

LANGUAGE: jsx
CODE:
```
import { Button, Description, Field, Fieldset, Input, Label, Legend, Select } from "@headlessui/react";
import { regions } from "./countries";

export function Example() {
  const [country, setCountry] = useState(null);

  return (
    <form action="/shipping">
      <Fieldset>
        <Legend>Shipping details</Legend>
        <Field>
          <Label>Street address</Label>
          <Input name="address" />
        </Field>
        <Field>
          <Label>Country</Label>
          <Description>We currently only ship to North America.</Description>
          <Select name="country" value={country} onChange={(event) => setCountry(event.target.value)}>
            <option></option>
            <option>Canada</option>
            <option>Mexico</option>
            <option>United States</option>
          </Select>
        </Field>
        // [!code highlight:4]
        <Field disabled={!country}>
          <Label className="data-[disabled]:opacity-40">State/province</Label>
          <Select name="region" className="data-[disabled]:opacity-50">
            <option></option>
            {country && regions[country].map((region) => <option>{region}</option>)}
          </Select>
        </Field>
        <Button>Submit</Button>
      </Fieldset>
    </form>
  );
}
```

----------------------------------------

TITLE: Implementing Scrollable Calendar Grid with TailwindCSS
DESCRIPTION: Creates a scrollable calendar grid interface using TailwindCSS utilities with sticky headers and time indicators. The component features both horizontal and vertical scrolling with fixed column and row headers.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/overflow.mdx#2025-04-22_snippet_8

LANGUAGE: JSX
CODE:
```
<div className="overflow-hidden dark:bg-gray-800">
  <div className="grid max-h-90 grid-cols-[70px_repeat(7,150px)] grid-rows-[auto_repeat(16,50px)] overflow-scroll">
    {/* Calendar frame */}
    <div className="sticky top-0 z-10 col-start-1 row-start-1 border-b border-gray-100 bg-white bg-clip-padding py-2 text-sm font-medium text-gray-900 dark:border-black/10 dark:bg-gradient-to-b dark:from-gray-600 dark:to-gray-700 dark:text-gray-200"></div>
    <div className="sticky top-0 z-10 col-start-2 row-start-1 border-b border-gray-100 bg-white bg-clip-padding py-2 text-center text-sm font-medium text-gray-900 dark:border-black/10 dark:bg-gradient-to-b dark:from-gray-600 dark:to-gray-700 dark:text-gray-200">
      Sun
    </div>
    <!-- Additional calendar cells omitted for brevity -->
  </div>
</div>
```

----------------------------------------

TITLE: Creating a Reusable React Component with Tailwind CSS
DESCRIPTION: Demonstrates how to build a reusable React component (`VacationCard`) using Tailwind CSS utility classes for styling. Shows how props can be used to make the component dynamic, allowing for easy reuse with different content.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/styling-with-utility-classes.mdx#_snippet_30

LANGUAGE: jsx
CODE:
```
export function VacationCard({ img, imgAlt, eyebrow, title, pricing, url }) {
  return (
    <div>
      <img className="rounded-lg" src={img} alt={imgAlt} />
      <div className="mt-4">
        <div className="text-xs font-bold text-sky-500">{eyebrow}</div>
        <div className="mt-1 font-bold text-gray-700">
          <a href={url} className="hover:underline">
            {title}
          </a>
        </div>
        <div className="mt-2 text-sm text-gray-600">{pricing}</div>
      </div>
    </div>
  );
}
```

----------------------------------------

TITLE: Responsive Grid Layout with Tailwind CSS and Breakpoints (JSX)
DESCRIPTION: This JSX snippet demonstrates a responsive grid layout using Tailwind CSS utility classes within a React component context. The grid changes from 2 columns to 3 columns at the `@sm` breakpoint using the `@sm:grid-cols-3` class. This requires Tailwind CSS configured with container queries or standard breakpoints.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/styling-with-utility-classes.mdx#_snippet_9

LANGUAGE: JSX
CODE:
```
<div className="grid grid-cols-2 gap-4 text-center font-mono font-medium text-white @sm:grid-cols-3">
  <div className="rounded-lg bg-sky-500 p-4">01</div>
  <div className="rounded-lg bg-sky-500 p-4">02</div>
  <div className="rounded-lg bg-sky-500 p-4">03</div>
  <div className="rounded-lg bg-sky-500 p-4">04</div>
  <div className="rounded-lg bg-sky-500 p-4">05</div>
  <div className="rounded-lg bg-sky-500 p-4">06</div>
</div>
```

----------------------------------------

TITLE: Data Attribute Targeting Example
DESCRIPTION: Demonstrates targeting custom boolean data attributes without configuration.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4/index.mdx#2025-04-22_snippet_9

LANGUAGE: html
CODE:
```
<div data-current class="opacity-75 data-current:opacity-100">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: HTML Variant Comparison in Tailwind CSS v4
DESCRIPTION: Demonstrates the new composable variants feature in Tailwind CSS v4.0, showing how to replace custom selectors like 'group-has-[&:focus]' with simpler variants like 'group-has-focus' that compose naturally.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4-alpha/index.mdx#2025-04-22_snippet_1

LANGUAGE: html
CODE:
```
<div class="group">
  <div class="group-has-[&:focus]:opacity-100"> <!-- [!code --] -->
  <div class="group-has-focus:opacity-100"> <!-- [!code ++] -->
      <!-- ... -->
    </div>
  </div>
</div>
```

----------------------------------------

TITLE: Incorrect Dynamic Class Name Construction in HTML
DESCRIPTION: Example of incorrect class name construction that Tailwind cannot detect because it uses string interpolation, resulting in class names that don't exist in the source files.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/detecting-classes-in-source-files.mdx#2025-04-22_snippet_1

LANGUAGE: html
CODE:
```
<div class="text-{{ error ? 'red' : 'green' }}-600"></div>
```

----------------------------------------

TITLE: Implementing Headless Checkbox (React/JSX)
DESCRIPTION: Illustrates the usage of the new `Checkbox` component along with `Field`, `Label`, and `Description` to create a custom-styled checkbox input with proper accessibility attributes. It shows how state (`data-checked`, `data-focus`) can be used for styling via CSS classes.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/headless-ui-v2/index.mdx#_snippet_2

LANGUAGE: jsx
CODE:
```
import { Checkbox, Description, Field, Label } from "@headlessui/react";
import { CheckmarkIcon } from "./icons/checkmark";
import clsx from "clsx";

function Example() {
  return (
    <Field>
      // [!code highlight:11]
      <Checkbox
        defaultChecked
        className={clsx(
          "size-4 rounded border bg-white dark:bg-white/5",
          "data-[checked]:border-transparent data-[checked]:bg-blue-500",
          "focus:outline-none data-[focus]:outline-2 data-[focus]:outline-offset-2 data-[focus]:outline-blue-500",
        )}
      >
        <CheckmarkIcon className="stroke-white opacity-0 group-data-[checked]:opacity-100" />
      </Checkbox>
      <div>
        <Label>Enable beta features</Label>
        <Description>This will give you early access to any awesome new features we're developing.</Description>
      </div>
    </Field>
  );
}
```

----------------------------------------

TITLE: Creating a Custom Dropdown Menu with Headless UI in React
DESCRIPTION: Implementation of a custom dropdown menu using Headless UI's Menu component system in React. This example demonstrates the compound component pattern with context-based communication between components.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/building-react-and-vue-support-for-tailwind-ui/index.mdx#2025-04-22_snippet_2

LANGUAGE: jsx
CODE:
```
import { Menu } from "@headlessui/react";

function MyDropdown() {
  return (
    <Menu as="div" className="relative">
      <Menu.Button className="rounded bg-blue-600 px-4 py-2 text-white ...">Options</Menu.Button>
      <Menu.Items className="absolute right-0 mt-1">
        <Menu.Item>
          {({ active }) => (
            <a className={`${active && "bg-blue-500 text-white"} ...`} href="/account-settings">
              Account settings
            </a>
          )}
        </Menu.Item>
        <Menu.Item>
          {({ active }) => (
            <a className={`${active && "bg-blue-500 text-white"} ...`} href="/documentation">
              Documentation
            </a>
          )}
        </Menu.Item>
        <Menu.Item disabled>
          <span className="opacity-75 ...">Invite a friend (coming soon!)</span>
        </Menu.Item>
      </Menu.Items>
    </Menu>
  );
}
```

----------------------------------------

TITLE: Applying Dark Mode Classes in HTML with Tailwind CSS
DESCRIPTION: This snippet demonstrates how to use Tailwind CSS classes to style elements differently in dark mode. It includes classes for background color and text color changes.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/dark-mode.mdx#2025-04-22_snippet_0

LANGUAGE: HTML
CODE:
```
<div class="bg-white dark:bg-gray-800 rounded-lg px-6 py-8 ring shadow-xl ring-gray-900/5">
  <div>
    <span class="inline-flex items-center justify-center rounded-md bg-indigo-500 p-2 shadow-lg">
      <svg class="h-6 w-6 stroke-white" ...>
        <!-- ... -->
      </svg>
    </span>
  </div>
  <h3 class="text-gray-900 dark:text-white mt-5 text-base font-medium tracking-tight ">Writes upside-down</h3>
  <p class="text-gray-500 dark:text-gray-400 mt-2 text-sm ">
    The Zero Gravity Pen can be used to write in any orientation, including upside-down. It even works in outer space.
  </p>
</div>
```

----------------------------------------

TITLE: Center Alignment with justify-center
DESCRIPTION: Examples demonstrating both regular and safe center alignment using justify-center and justify-center-safe utilities.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/justify-content.mdx#2025-04-22_snippet_1

LANGUAGE: html
CODE:
```
<div class="flex justify-center ...">
  <div>01</div>
  <div>02</div>
  <div>03</div>
  <div>04</div>
</div>
```

LANGUAGE: html
CODE:
```
<div class="flex justify-center-safe ...">
  <div>01</div>
  <div>02</div>
  <div>03</div>
  <div>04</div>
</div>
```

----------------------------------------

TITLE: Basic Flex Grow Example
DESCRIPTION: HTML example showing how to use the grow utility to allow a flex item to fill available space.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/flex-grow.mdx#2025-04-22_snippet_1

LANGUAGE: html
CODE:
```
<div class="flex ...">
  <div class="size-14 flex-none ...">01</div>
  <div class="size-14 grow ...">02</div>
  <div class="size-14 flex-none ...">03</div>
</div>
```

----------------------------------------

TITLE: Applying Multiple Color Utilities in a Notification Component
DESCRIPTION: Example showcasing how to apply various Tailwind CSS color utilities to create a notification component with background, text, border, stroke, and outline colors, including dark mode variants.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/colors.mdx#2025-04-22_snippet_1

LANGUAGE: html
CODE:
```
<!-- [!code classes:bg-white,border-pink-300,bg-pink-100,stroke-pink-700,text-gray-950,text-gray-500,outline-black/5,text-gray-700,dark:bg-gray-800,dark:border-pink-300/10,dark:bg-pink-400/10,dark:stroke-pink-500,dark:text-gray-400,dark:text-white] -->
<div class="flex items-center gap-4 rounded-lg bg-white p-6 shadow-md outline outline-black/5 dark:bg-gray-800">
  <!-- prettier-ignore -->
  <span class="inline-flex shrink-0 rounded-full border border-pink-300 bg-pink-100 p-2 dark:border-pink-300/10 dark:bg-pink-400/10">
    <svg class="size-6 stroke-pink-700 dark:stroke-pink-500"><!-- ... --></svg>
  </span>
  <div>
    <p class="text-gray-700 dark:text-gray-400">
      <span class="font-medium text-gray-950 dark:text-white">Tom Watson</span> mentioned you in
      <span class="font-medium text-gray-950 dark:text-white">Logo redesign</span>
    </p>
    <time class="mt-1 block text-gray-500" datetime="9:37">9:37am</time>
  </div>
</div>
```

----------------------------------------

TITLE: Tailwind CSS Interactive States Example
DESCRIPTION: Shows how to apply hover, focus, and active states to a button using Tailwind CSS classes.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#2025-04-22_snippet_4

LANGUAGE: html
CODE:
```
<button class="bg-violet-500 hover:bg-violet-600 focus:outline-2 focus:outline-offset-2 focus:outline-violet-500 active:bg-violet-700 ...">
  Save changes
</button>
```

----------------------------------------

TITLE: Responsive Grid Layout with Tailwind CSS Breakpoints
DESCRIPTION: Example of creating a responsive grid that changes column count at different viewport widths using Tailwind's responsive variants. The grid starts with 3 columns on mobile, changes to 4 columns at medium screens, and 6 columns at large screens.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#2025-04-22_snippet_34

LANGUAGE: html
CODE:
```
<div class="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-6">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Default Tailwind CSS Theme Variables in OKLCH Format
DESCRIPTION: This CSS code defines all the default theme variables used in Tailwind CSS. It includes font family definitions and a complete color palette in OKLCH color format with values for red, orange, amber, yellow, lime, green, emerald, teal, cyan, sky, blue, indigo, violet, purple, fuchsia, pink, rose, and slate colors, each with shade variants from 50 to 950.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/theme.mdx#2025-04-22_snippet_18

LANGUAGE: css
CODE:
```
@theme {
  /* prettier-ignore */
  --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;

  --color-red-50: oklch(0.971 0.013 17.38);
  --color-red-100: oklch(0.936 0.032 17.717);
  --color-red-200: oklch(0.885 0.062 18.334);
  --color-red-300: oklch(0.808 0.114 19.571);
  --color-red-400: oklch(0.704 0.191 22.216);
  --color-red-500: oklch(0.637 0.237 25.331);
  --color-red-600: oklch(0.577 0.245 27.325);
  --color-red-700: oklch(0.505 0.213 27.518);
  --color-red-800: oklch(0.444 0.177 26.899);
  --color-red-900: oklch(0.396 0.141 25.723);
  --color-red-950: oklch(0.258 0.092 26.042);

  --color-orange-50: oklch(0.98 0.016 73.684);
  --color-orange-100: oklch(0.954 0.038 75.164);
  --color-orange-200: oklch(0.901 0.076 70.697);
  --color-orange-300: oklch(0.837 0.128 66.29);
  --color-orange-400: oklch(0.75 0.183 55.934);
  --color-orange-500: oklch(0.705 0.213 47.604);
  --color-orange-600: oklch(0.646 0.222 41.116);
  --color-orange-700: oklch(0.553 0.195 38.402);
  --color-orange-800: oklch(0.47 0.157 37.304);
  --color-orange-900: oklch(0.408 0.123 38.172);
  --color-orange-950: oklch(0.266 0.079 36.259);

  --color-amber-50: oklch(0.987 0.022 95.277);
  --color-amber-100: oklch(0.962 0.059 95.617);
  --color-amber-200: oklch(0.924 0.12 95.746);
  --color-amber-300: oklch(0.879 0.169 91.605);
  --color-amber-400: oklch(0.828 0.189 84.429);
  --color-amber-500: oklch(0.769 0.188 70.08);
  --color-amber-600: oklch(0.666 0.179 58.318);
  --color-amber-700: oklch(0.555 0.163 48.998);
  --color-amber-800: oklch(0.473 0.137 46.201);
  --color-amber-900: oklch(0.414 0.112 45.904);
  --color-amber-950: oklch(0.279 0.077 45.635);

  --color-yellow-50: oklch(0.987 0.026 102.212);
  --color-yellow-100: oklch(0.973 0.071 103.193);
  --color-yellow-200: oklch(0.945 0.129 101.54);
  --color-yellow-300: oklch(0.905 0.182 98.111);
  --color-yellow-400: oklch(0.852 0.199 91.936);
  --color-yellow-500: oklch(0.795 0.184 86.047);
  --color-yellow-600: oklch(0.681 0.162 75.834);
  --color-yellow-700: oklch(0.554 0.135 66.442);
  --color-yellow-800: oklch(0.476 0.114 61.907);
  --color-yellow-900: oklch(0.421 0.095 57.708);
  --color-yellow-950: oklch(0.286 0.066 53.813);

  --color-lime-50: oklch(0.986 0.031 120.757);
  --color-lime-100: oklch(0.967 0.067 122.328);
  --color-lime-200: oklch(0.938 0.127 124.321);
  --color-lime-300: oklch(0.897 0.196 126.665);
  --color-lime-400: oklch(0.841 0.238 128.85);
  --color-lime-500: oklch(0.768 0.233 130.85);
  --color-lime-600: oklch(0.648 0.2 131.684);
  --color-lime-700: oklch(0.532 0.157 131.589);
  --color-lime-800: oklch(0.453 0.124 130.933);
  --color-lime-900: oklch(0.405 0.101 131.063);
  --color-lime-950: oklch(0.274 0.072 132.109);

  --color-green-50: oklch(0.982 0.018 155.826);
  --color-green-100: oklch(0.962 0.044 156.743);
  --color-green-200: oklch(0.925 0.084 155.995);
  --color-green-300: oklch(0.871 0.15 154.449);
  --color-green-400: oklch(0.792 0.209 151.711);
  --color-green-500: oklch(0.723 0.219 149.579);
  --color-green-600: oklch(0.627 0.194 149.214);
  --color-green-700: oklch(0.527 0.154 150.069);
  --color-green-800: oklch(0.448 0.119 151.328);
  --color-green-900: oklch(0.393 0.095 152.535);
  --color-green-950: oklch(0.266 0.065 152.934);

  --color-emerald-50: oklch(0.979 0.021 166.113);
  --color-emerald-100: oklch(0.95 0.052 163.051);
  --color-emerald-200: oklch(0.905 0.093 164.15);
  --color-emerald-300: oklch(0.845 0.143 164.978);
  --color-emerald-400: oklch(0.765 0.177 163.223);
  --color-emerald-500: oklch(0.696 0.17 162.48);
  --color-emerald-600: oklch(0.596 0.145 163.225);
  --color-emerald-700: oklch(0.508 0.118 165.612);
  --color-emerald-800: oklch(0.432 0.095 166.913);
  --color-emerald-900: oklch(0.378 0.077 168.94);
  --color-emerald-950: oklch(0.262 0.051 172.552);

  --color-teal-50: oklch(0.984 0.014 180.72);
  --color-teal-100: oklch(0.953 0.051 180.801);
  --color-teal-200: oklch(0.91 0.096 180.426);
  --color-teal-300: oklch(0.855 0.138 181.071);
  --color-teal-400: oklch(0.777 0.152 181.912);
  --color-teal-500: oklch(0.704 0.14 182.503);
  --color-teal-600: oklch(0.6 0.118 184.704);
  --color-teal-700: oklch(0.511 0.096 186.391);
  --color-teal-800: oklch(0.437 0.078 188.216);
  --color-teal-900: oklch(0.386 0.063 188.416);
  --color-teal-950: oklch(0.277 0.046 192.524);

  --color-cyan-50: oklch(0.984 0.019 200.873);
  --color-cyan-100: oklch(0.956 0.045 203.388);
  --color-cyan-200: oklch(0.917 0.08 205.041);
  --color-cyan-300: oklch(0.865 0.127 207.078);
  --color-cyan-400: oklch(0.789 0.154 211.53);
  --color-cyan-500: oklch(0.715 0.143 215.221);
  --color-cyan-600: oklch(0.609 0.126 221.723);
  --color-cyan-700: oklch(0.52 0.105 223.128);
  --color-cyan-800: oklch(0.45 0.085 224.283);
  --color-cyan-900: oklch(0.398 0.07 227.392);
  --color-cyan-950: oklch(0.302 0.056 229.695);

  --color-sky-50: oklch(0.977 0.013 236.62);
  --color-sky-100: oklch(0.951 0.026 236.824);
  --color-sky-200: oklch(0.901 0.058 230.902);
  --color-sky-300: oklch(0.828 0.111 230.318);
  --color-sky-400: oklch(0.746 0.16 232.661);
  --color-sky-500: oklch(0.685 0.169 237.323);
  --color-sky-600: oklch(0.588 0.158 241.966);
  --color-sky-700: oklch(0.5 0.134 242.749);
  --color-sky-800: oklch(0.443 0.11 240.79);
  --color-sky-900: oklch(0.391 0.09 240.876);
  --color-sky-950: oklch(0.293 0.066 243.157);

  --color-blue-50: oklch(0.97 0.014 254.604);
  --color-blue-100: oklch(0.932 0.032 255.585);
  --color-blue-200: oklch(0.882 0.059 254.128);
  --color-blue-300: oklch(0.809 0.105 251.813);
  --color-blue-400: oklch(0.707 0.165 254.624);
  --color-blue-500: oklch(0.623 0.214 259.815);
  --color-blue-600: oklch(0.546 0.245 262.881);
  --color-blue-700: oklch(0.488 0.243 264.376);
  --color-blue-800: oklch(0.424 0.199 265.638);
  --color-blue-900: oklch(0.379 0.146 265.522);
  --color-blue-950: oklch(0.282 0.091 267.935);

  --color-indigo-50: oklch(0.962 0.018 272.314);
  --color-indigo-100: oklch(0.93 0.034 272.788);
  --color-indigo-200: oklch(0.87 0.065 274.039);
  --color-indigo-300: oklch(0.785 0.115 274.713);
  --color-indigo-400: oklch(0.673 0.182 276.935);
  --color-indigo-500: oklch(0.585 0.233 277.117);
  --color-indigo-600: oklch(0.511 0.262 276.966);
  --color-indigo-700: oklch(0.457 0.24 277.023);
  --color-indigo-800: oklch(0.398 0.195 277.366);
  --color-indigo-900: oklch(0.359 0.144 278.697);
  --color-indigo-950: oklch(0.257 0.09 281.288);

  --color-violet-50: oklch(0.969 0.016 293.756);
  --color-violet-100: oklch(0.943 0.029 294.588);
  --color-violet-200: oklch(0.894 0.057 293.283);
  --color-violet-300: oklch(0.811 0.111 293.571);
  --color-violet-400: oklch(0.702 0.183 293.541);
  --color-violet-500: oklch(0.606 0.25 292.717);
  --color-violet-600: oklch(0.541 0.281 293.009);
  --color-violet-700: oklch(0.491 0.27 292.581);
  --color-violet-800: oklch(0.432 0.232 292.759);
  --color-violet-900: oklch(0.38 0.189 293.745);
  --color-violet-950: oklch(0.283 0.141 291.089);

  --color-purple-50: oklch(0.977 0.014 308.299);
  --color-purple-100: oklch(0.946 0.033 307.174);
  --color-purple-200: oklch(0.902 0.063 306.703);
  --color-purple-300: oklch(0.827 0.119 306.383);
  --color-purple-400: oklch(0.714 0.203 305.504);
  --color-purple-500: oklch(0.627 0.265 303.9);
  --color-purple-600: oklch(0.558 0.288 302.321);
  --color-purple-700: oklch(0.496 0.265 301.924);
  --color-purple-800: oklch(0.438 0.218 303.724);
  --color-purple-900: oklch(0.381 0.176 304.987);
  --color-purple-950: oklch(0.291 0.149 302.717);

  --color-fuchsia-50: oklch(0.977 0.017 320.058);
  --color-fuchsia-100: oklch(0.952 0.037 318.852);
  --color-fuchsia-200: oklch(0.903 0.076 319.62);
  --color-fuchsia-300: oklch(0.833 0.145 321.434);
  --color-fuchsia-400: oklch(0.74 0.238 322.16);
  --color-fuchsia-500: oklch(0.667 0.295 322.15);
  --color-fuchsia-600: oklch(0.591 0.293 322.896);
  --color-fuchsia-700: oklch(0.518 0.253 323.949);
  --color-fuchsia-800: oklch(0.452 0.211 324.591);
  --color-fuchsia-900: oklch(0.401 0.17 325.612);
  --color-fuchsia-950: oklch(0.293 0.136 325.661);

  --color-pink-50: oklch(0.971 0.014 343.198);
  --color-pink-100: oklch(0.948 0.028 342.258);
  --color-pink-200: oklch(0.899 0.061 343.231);
  --color-pink-300: oklch(0.823 0.12 346.018);
  --color-pink-400: oklch(0.718 0.202 349.761);
  --color-pink-500: oklch(0.656 0.241 354.308);
  --color-pink-600: oklch(0.592 0.249 0.584);
  --color-pink-700: oklch(0.525 0.223 3.958);
  --color-pink-800: oklch(0.459 0.187 3.815);
  --color-pink-900: oklch(0.408 0.153 2.432);
  --color-pink-950: oklch(0.284 0.109 3.907);

  --color-rose-50: oklch(0.969 0.015 12.422);
  --color-rose-100: oklch(0.941 0.03 12.58);
  --color-rose-200: oklch(0.892 0.058 10.001);
  --color-rose-300: oklch(0.81 0.117 11.638);
  --color-rose-400: oklch(0.712 0.194 13.428);
  --color-rose-500: oklch(0.645 0.246 16.439);
  --color-rose-600: oklch(0.586 0.253 17.585);
  --color-rose-700: oklch(0.514 0.222 16.935);
  --color-rose-800: oklch(0.455 0.188 13.697);
  --color-rose-900: oklch(0.41 0.159 10.272);
  --color-rose-950: oklch(0.271 0.105 12.094);

  --color-slate-50: oklch(0.984 0.003 247.858);
  --color-slate-100: oklch(0.968 0.007 247.896);
  --color-slate-200: oklch(0.929 0.013 255.508);
  --color-slate-300: oklch(0.869 0.022 252.894);
  --color-slate-400: oklch(0.704 0.04 256.788);
  --color-slate-500: oklch(0.554 0.046 257.417);
  --color-slate-600: oklch(0.446 0.043 257.281);
}
```

----------------------------------------

TITLE: Installing Latest TailwindCSS Version
DESCRIPTION: Command to update TailwindCSS to the latest version using npm.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-2/index.mdx#2025-04-22_snippet_16

LANGUAGE: sh
CODE:
```
npm install -D tailwindcss@latest
```

----------------------------------------

TITLE: CSS Nesting Input Example
DESCRIPTION: Demonstrates nested CSS syntax that Tailwind processes using Lightning CSS.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/compatibility.mdx#2025-04-22_snippet_2

LANGUAGE: css
CODE:
```
.typography {
  p {
    font-size: var(--text-base);
  }
  img {
    border-radius: var(--radius-lg);
  }
}
```

----------------------------------------

TITLE: Defining a Custom CSS Component Class with Tailwind Layers
DESCRIPTION: Illustrates how to create a custom CSS class (`.btn-primary`) using Tailwind's `@layer components` directive and CSS variables derived from the Tailwind theme. This approach is useful for simpler components where a full template partial might be overkill. The HTML shows how to apply this custom class.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/styling-with-utility-classes.mdx#_snippet_31

LANGUAGE: html
CODE:
```
<!-- [!code filename:HTML] -->
<button class="btn-primary">Save changes</button>
```

LANGUAGE: css
CODE:
```
/* [!code filename:CSS] */
@import "tailwindcss";

@layer components {
  .btn-primary {
    border-radius: calc(infinity * 1px);
    background-color: var(--color-violet-500);
    padding-inline: --spacing(5);
    padding-block: --spacing(2);
    font-weight: var(--font-weight-semibold);
    color: var(--color-white);
    box-shadow: var(--shadow-md);
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-violet-700);
      }
    }
  }
}
```

----------------------------------------

TITLE: Start-Aligned Content Grid Layout
DESCRIPTION: Shows how to use place-content-start to align grid items at the start of the container.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/place-content.mdx#2025-04-22_snippet_1

LANGUAGE: html
CODE:
```
<div class="grid h-48 grid-cols-2 place-content-start gap-4 ...">
  <div>01</div>
  <div>02</div>
  <div>03</div>
  <div>04</div>
</div>
```

----------------------------------------

TITLE: Customizing Font Family in Tailwind CSS Theme
DESCRIPTION: This CSS snippet shows how to customize the font family in Tailwind CSS theme, including setting font-feature-settings and font-variation-settings.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/font-family.mdx#2025-04-22_snippet_2

LANGUAGE: css
CODE:
```
@theme {
  --font-display: "Oswald", "sans-serif";
  --font-display--font-feature-settings: "cv02", "cv03", "cv04", "cv11";
  --font-display--font-variation-settings: "opsz" 32;
}
```

----------------------------------------

TITLE: Rendering Elements in a Loop with Tailwind CSS (Svelte)
DESCRIPTION: This Svelte snippet shows how to use the `#each` block to iterate over a list of contributors and render an image tag for each. The Tailwind CSS classes are applied once within the loop template, avoiding manual duplication of the class list.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/styling-with-utility-classes.mdx#_snippet_29

LANGUAGE: Svelte
CODE:
```
<div>
  <div class="flex items-center space-x-2 text-base">
    <h4 class="font-semibold text-slate-900">Contributors</h4>
    <span class="bg-slate-100 px-2 py-1 text-xs font-semibold text-slate-700 ...">204</span>
  </div>
  <div class="mt-3 flex -space-x-2 overflow-hidden">
    <!-- prettier-ignore -->
    <!-- [!code highlight:4] -->
    {#each contributors as user}
      <img class="inline-block h-12 w-12 rounded-full ring-2 ring-white" src={user.avatarUrl} alt={user.handle} />
    {/each}
  </div>
  <div class="mt-3 text-sm font-medium">
    <a href="#" class="text-blue-500">+ 198 others</a>
  </div>
</div>
```

----------------------------------------

TITLE: Creating a Composable Text Field with Catalyst in React
DESCRIPTION: Shows how to build a text field using Catalyst's composable component architecture. This approach mirrors HTML structure and allows for more flexible customization of individual elements.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/introducing-catalyst/index.mdx#2025-04-22_snippet_1

LANGUAGE: JSX
CODE:
```
import { Description, Field, Label } from "@/components/fieldset";
import { Input } from "@/components/input";

function Example() {
  return (
    <Field>
      <Label>Product name</Label>
      <Description>Use the name you'd like people to see in their cart.</Description>
      <Input name="product_name" />
    </Field>
  );
}
```

----------------------------------------

TITLE: Dark Mode Color Scheme Implementation in JSX
DESCRIPTION: Uses the TargetingSpecificStates component to demonstrate how to apply color scheme utilities specifically in dark mode, switching from light to dark scheme.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/color-scheme.mdx#2025-04-22_snippet_5

LANGUAGE: jsx
CODE:
```
<TargetingSpecificStates
  element="html"
  property="color-scheme"
  variant="dark"
  defaultClass="scheme-light"
  featuredClass="scheme-dark"
/>
```

----------------------------------------

TITLE: Percentage-based Max-Width Example
DESCRIPTION: HTML example demonstrating percentage-based maximum widths using Tailwind's fraction utilities.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/max-width.mdx#2025-04-22_snippet_2

LANGUAGE: html
CODE:
```
<div class="w-full max-w-9/10 ...">max-w-9/10</div>
<div class="w-full max-w-3/4 ...">max-w-3/4</div>
<div class="w-full max-w-1/2 ...">max-w-1/2</div>
<div class="w-full max-w-1/3 ...">max-w-1/3</div>
```

----------------------------------------

TITLE: Applying Hover Effect with Tailwind CSS
DESCRIPTION: Demonstrates how to use the 'hover' variant to style an element when the user hovers over it with the mouse cursor.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#2025-04-22_snippet_83

LANGUAGE: HTML
CODE:
```
<div class="bg-black hover:bg-white ...">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Defining Custom Color Palette in TailwindCSS
DESCRIPTION: Shows how to disable default colors and define a custom color palette using CSS variables in the @theme block. Uses the --color-* syntax to reset default colors and establishes new color definitions.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/colors.mdx#2025-04-22_snippet_11

LANGUAGE: css
CODE:
```
@import "tailwindcss";

@theme {
  --color-*: initial;
  --color-white: #fff;
  --color-purple: #3f3cbb;
  --color-midnight: #121063;
  --color-tahiti: #3ab7bf;
  --color-bermuda: #78dcca;
}
```

----------------------------------------

TITLE: Conditionally Applying Tailwind Classes in React to Avoid Conflicts
DESCRIPTION: Provides an example in React/JSX showing how to use conditional logic (a ternary operator) to apply only one of two potentially conflicting Tailwind classes (`grid` or `flex`) based on a component prop (`gridLayout`). This is a common pattern in component-based frameworks to prevent style conflicts.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/styling-with-utility-classes.mdx#_snippet_33

LANGUAGE: jsx
CODE:
```
// [!code filename:example.jsx]
// [!code word:gridLayout\ \?\ "grid"\ \:\ "flex"]
export function Example({ gridLayout }) {
  return <div className={gridLayout ? "grid" : "flex"}>{/* ... */}</div>;
}
```

----------------------------------------

TITLE: Container Queries in Tailwind CSS
DESCRIPTION: Container query breakpoints for component-level responsive design. Includes fine-grained size control from 3xs to 7xl with both min-width and max-width variants.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#2025-04-22_snippet_76

LANGUAGE: CSS
CODE:
```
@container (width >= 16rem) // @3xs
@container (width >= 18rem) // @2xs
@container (width >= 20rem) // @xs
@container (width >= 24rem) // @sm
@container (width >= 28rem) // @md
@container (width >= 32rem) // @lg
@container (width >= 36rem) // @xl
@container (width >= 42rem) // @2xl
@container (width >= 48rem) // @3xl
@container (width >= 56rem) // @4xl
@container (width >= 64rem) // @5xl
@container (width >= 72rem) // @6xl
@container (width >= 80rem) // @7xl
@container (width >= ...) // @min-[...]
@container (width < 16rem) // @max-3xs
@container (width < 18rem) // @max-2xs
@container (width < 20rem) // @max-xs
@container (width < 24rem) // @max-sm
@container (width < ...) // @max-[...]
```

----------------------------------------

TITLE: Dynamic Viewport Height Example
DESCRIPTION: HTML example showing usage of the new dynamic viewport height (dvh) utility class in Tailwind CSS.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-4/index.mdx#2025-04-22_snippet_3

LANGUAGE: html
CODE:
```
<div class="h-dvh">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Hover State Text Color Example - HTML
DESCRIPTION: Demonstrates how to apply text color changes on hover state with dark mode support.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/color.mdx#2025-04-22_snippet_3

LANGUAGE: html
CODE:
```
<p class="...">
  Oh I gotta get on that
  <a class="underline hover:text-blue-600 dark:hover:text-blue-400" href="https://en.wikipedia.org/wiki/Internet">internet</a>,
  I'm late on everything!
</p>
```

----------------------------------------

TITLE: Basic Flex Example in HTML
DESCRIPTION: Demonstrates basic flex layout with flex-1 utility to allow flex items to grow and shrink while ignoring initial size.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/flex.mdx#2025-04-22_snippet_0

LANGUAGE: html
CODE:
```
<div class="flex">
  <div class="w-14 flex-none ...">01</div>
  <div class="w-64 flex-1 ...">02</div>
  <div class="w-32 flex-1 ...">03</div>
</div>
```

----------------------------------------

TITLE: Basic Container Query Implementation
DESCRIPTION: Demonstrates basic usage of container queries with Tailwind's @container class and responsive variants.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/responsive-design.mdx#2025-04-22_snippet_12

LANGUAGE: html
CODE:
```
<div class="@container">
  <div class="flex flex-col @md:flex-row">
    <!-- ... -->
  </div>
</div>
```

----------------------------------------

TITLE: Using Flex Display Utility in Tailwind CSS
DESCRIPTION: HTML example showing how to use the flex utility to create a block-level flex container, enabling flexible box layout for arranging items like profile cards with images and text.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/display.mdx#2025-04-22_snippet_3

LANGUAGE: html
CODE:
```
<!-- [!code classes:flex] -->
<div class="flex items-center">
  <img src="path/to/image.jpg" />
  <div>
    <strong>Andrew Alfred</strong>
    <span>Technical advisor</span>
  </div>
</div>
```

----------------------------------------

TITLE: Breakpoint Range Example
DESCRIPTION: Shows how to target a specific breakpoint range using responsive and max-* variants.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/responsive-design.mdx#2025-04-22_snippet_5

LANGUAGE: html
CODE:
```
<div class="md:max-xl:flex">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Creating a Toggle Switch Component with React and HeadlessUI
DESCRIPTION: This snippet demonstrates how to create an accessible toggle switch component using React and HeadlessUI. It manages state with useState and applies conditional Tailwind CSS classes based on the toggle state, handling styling transitions and accessibility attributes automatically.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwind-ui-now-with-react-and-vue-support/index.mdx#2025-04-22_snippet_0

LANGUAGE: jsx
CODE:
```
import { useState } from "react";
import { Switch } from "@headlessui/react";

function classNames(...classes) {
  return classes.filter(Boolean).join(" ");
}

export default function Example() {
  const [enabled, setEnabled] = useState(false);

  return (
    <Switch
      checked={enabled}
      onChange={setEnabled}
      className={classNames(
        enabled ? "bg-indigo-600" : "bg-gray-200",
        "relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none",
      )}
    >
      <span className="sr-only">Use setting</span>
      <span
        aria-hidden="true"
        className={classNames(
          enabled ? "translate-x-5" : "translate-x-0",
          "pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out",
        )}
      />
    </Switch>
  );
}
```

----------------------------------------

TITLE: Setting Border Widths for Horizontal and Vertical Sides in Tailwind CSS
DESCRIPTION: Demonstrates the use of border-x and border-y-4 utilities to set border widths on two sides of an element simultaneously. Includes visual examples and corresponding HTML code.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/border-width.mdx#2025-04-22_snippet_4

LANGUAGE: HTML
CODE:
```
<div class="border-x-4 border-indigo-500 ..."></div>
<div class="border-y-4 border-indigo-500 ..."></div>
```

----------------------------------------

TITLE: Correct Static Class Name Usage in HTML
DESCRIPTION: Properly formatted class names that Tailwind can detect because they appear as complete strings in the source files, even when conditionally applied.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/detecting-classes-in-source-files.mdx#2025-04-22_snippet_2

LANGUAGE: html
CODE:
```
<div class="{{ error ? 'text-red-600' : 'text-green-600' }}"></div>
```

----------------------------------------

TITLE: Overriding Default Colors in Tailwind CSS
DESCRIPTION: This CSS example demonstrates how to override default Tailwind CSS colors by redefining color variables with new values using the @theme directive.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/colors.mdx#2025-04-22_snippet_9

LANGUAGE: css
CODE:
```
@import "tailwindcss";

@theme {
  --color-gray-50: oklch(0.984 0.003 247.858);
  --color-gray-100: oklch(0.968 0.007 247.896);
  --color-gray-200: oklch(0.929 0.013 255.508);
  --color-gray-300: oklch(0.869 0.022 252.894);
  --color-gray-400: oklch(0.704 0.04 256.788);
  --color-gray-500: oklch(0.554 0.046 257.417);
  --color-gray-600: oklch(0.446 0.043 257.281);
  --color-gray-700: oklch(0.372 0.044 257.287);
  --color-gray-800: oklch(0.279 0.041 260.031);
  --color-gray-900: oklch(0.208 0.042 265.755);
  --color-gray-950: oklch(0.129 0.042 264.695);
}
```

----------------------------------------

TITLE: Using Custom Breakpoints in HTML Markup
DESCRIPTION: Shows how to use custom breakpoints in HTML classes for responsive grid layouts. Demonstrates usage of xs and 3xl breakpoints.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/responsive-design.mdx#2025-04-22_snippet_8

LANGUAGE: html
CODE:
```
<div class="grid xs:grid-cols-2 3xl:grid-cols-6">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Absolute Positioning Example - HTML
DESCRIPTION: Example illustrating absolute positioning in TailwindCSS, showing how elements are positioned outside the normal document flow.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/position.mdx#2025-04-22_snippet_3

LANGUAGE: html
CODE:
```
<div class="static ...">
  <!-- Static parent -->
  <div class="static ..."><p>Static child</p></div>
  <div class="inline-block ..."><p>Static sibling</p></div>
  <!-- Static parent -->
  <div class="absolute ..."><p>Absolute child</p></div>
  <div class="inline-block ..."><p>Static sibling</p></div>
</div>
```

----------------------------------------

TITLE: Container Query Styling with Tailwind CSS
DESCRIPTION: Example showing how to use Tailwind's container query feature to style elements based on the width of a parent container rather than the viewport. The flex direction changes from column to row when the container reaches medium width.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#2025-04-22_snippet_35

LANGUAGE: html
CODE:
```
<div class="@container">
  <div class="flex flex-col @md:flex-row">
    <!-- ... -->
  </div>
</div>
```

----------------------------------------

TITLE: Responsive Card Layout Component
DESCRIPTION: Complete example of a responsive card component that changes from stacked to side-by-side layout at medium breakpoints using flex utilities.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/responsive-design.mdx#2025-04-22_snippet_2

LANGUAGE: html
CODE:
```
<div class="mx-auto max-w-md overflow-hidden rounded-xl bg-white shadow-md md:max-w-2xl">
  <div class="md:flex">
    <div class="md:shrink-0">
      <img
        class="h-48 w-full object-cover md:h-full md:w-48"
        src="/img/building.jpg"
        alt="Modern building architecture"
      />
    </div>
    <div class="p-8">
      <div class="text-sm font-semibold tracking-wide text-indigo-500 uppercase">Company retreats</div>
      <a href="#" class="mt-1 block text-lg leading-tight font-medium text-black hover:underline">
        Incredible accommodation for your team
      </a>
      <p class="mt-2 text-gray-500">
        Looking to take your team away on a retreat to enjoy awesome food and take in some sunshine? We have a list of
        places to do just that.
      </p>
    </div>
  </div>
</div>
```

----------------------------------------

TITLE: Styling Based on Parent State (group-hover)
DESCRIPTION: Illustrates how to use the `group-hover` variant to style a child element when a specific parent element is hovered. The parent needs the `group` class.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/styling-with-utility-classes.mdx#_snippet_22

LANGUAGE: HTML
CODE:
```
<!-- [!code filename:HTML] -->
<!-- [!code classes:group,group-hover:underline] -->
<a href="#" class="group rounded-lg p-8">
  <!-- ... -->
  <span class="group-hover:underline">Read more…</span>
</a>
```

LANGUAGE: CSS
CODE:
```
/* [!code filename:Simplified CSS] */
@media (hover: hover) {
  a:hover span {
    text-decoration-line: underline;
  }
}
```

----------------------------------------

TITLE: Form Input State Styling in TailwindCSS
DESCRIPTION: Collection of input state styling examples including disabled, enabled, checked, and validation states using TailwindCSS variants.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#2025-04-22_snippet_103

LANGUAGE: html
CODE:
```
<input class="disabled:opacity-75 ..." />
```

LANGUAGE: html
CODE:
```
<input class="enabled:hover:border-gray-400 disabled:opacity-75 ..." />
```

LANGUAGE: html
CODE:
```
<input type="checkbox" class="appearance-none checked:bg-blue-500 ..." />
```

LANGUAGE: html
CODE:
```
<input type="checkbox" class="appearance-none indeterminate:bg-gray-300 ..." />
```

LANGUAGE: html
CODE:
```
<input type="checkbox" class="default:outline-2 ..." />
```

LANGUAGE: html
CODE:
```
<input class="border optional:border-red-500 ..." />
```

LANGUAGE: html
CODE:
```
<input required class="border required:border-red-500 ..." />
```

LANGUAGE: html
CODE:
```
<input required class="border valid:border-green-500 ..." />
```

LANGUAGE: html
CODE:
```
<input required class="border invalid:border-red-500 ..." />
```

LANGUAGE: html
CODE:
```
<input required class="border user-valid:border-green-500" />
```

LANGUAGE: html
CODE:
```
<input required class="border user-invalid:border-red-500" />
```

LANGUAGE: html
CODE:
```
<input min="1" max="5" class="in-range:border-green-500 ..." />
```

LANGUAGE: html
CODE:
```
<input min="1" max="5" class="out-of-range:border-red-500 ..." />
```

LANGUAGE: html
CODE:
```
<input class="placeholder-shown:border-gray-500 ..." placeholder="<EMAIL>" />
```

LANGUAGE: html
CODE:
```
<details class="details-content:bg-gray-100 ...">
  <summary>Details</summary>
  This is a secret.
</details>
```

LANGUAGE: html
CODE:
```
<input class="autofill:bg-yellow-200 ..." />
```

LANGUAGE: html
CODE:
```
<input class="read-only:bg-gray-100 ..." />
```

----------------------------------------

TITLE: Styling Radio Buttons and Labels with Named Peer Classes in HTML
DESCRIPTION: This example shows how to use named peer classes to style radio buttons and their associated labels based on their checked state.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#2025-04-22_snippet_21

LANGUAGE: HTML
CODE:
```
<fieldset>
  <legend>Published status</legend>

  <input id="draft" class="peer/draft" type="radio" name="status" checked />
  <label for="draft" class="peer-checked/draft:text-sky-500">Draft</label>

  <input id="published" class="peer/published" type="radio" name="status" />
  <label for="published" class="peer-checked/published:text-sky-500">Published</label>
  <div class="hidden peer-checked/draft:block">Drafts are only visible to administrators.</div>
  <div class="hidden peer-checked/published:block">Your post will be publicly visible on your site.</div>
</fieldset>
```

----------------------------------------

TITLE: Sticky Header Implementation using Tailwind CSS
DESCRIPTION: Example showing how to create sticky section headers using Tailwind CSS classes. The sticky utility is combined with top-0 to fix the header at the top of the viewport when scrolling. Each section header remains sticky until its parent container scrolls out of view.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/position.mdx#2025-04-22_snippet_5

LANGUAGE: jsx
CODE:
```
<div className="px-3">
  <div className="relative mx-auto -my-px h-80 max-w-md overflow-auto bg-white shadow-lg ring-1 ring-gray-900/5 dark:bg-gray-800">
    <div className="relative">
      <div className="sticky top-0 flex items-center bg-gray-50/90 px-4 py-3 text-sm font-semibold text-gray-900 ring-1 ring-gray-900/10 backdrop-blur-sm dark:bg-gray-700/90 dark:text-gray-200 dark:ring-black/10">
        A
      </div>
      <!-- Additional content -->
    </div>
  </div>
</div>
```

LANGUAGE: html
CODE:
```
<div>
  <div>
    <div class="sticky top-0 ...">A</div>
    <div>
      <div>
        <img src="/img/andrew.jpg" />
        <strong>Andrew Alfred</strong>
      </div>
      <div>
        <img src="/img/aisha.jpg" />
        <strong>Aisha Houston</strong>
      </div>
      <!-- ... -->
    </div>
  </div>
  <div>
    <div class="sticky top-0">B</div>
    <div>
      <div>
        <img src="/img/bob.jpg" />
        <strong>Bob Alfred</strong>
      </div>
      <!-- ... -->
    </div>
  </div>
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Displaying Calendar Event Block with Tailwind CSS (HTML)
DESCRIPTION: An HTML `div` element representing a calendar event block, styled using Tailwind CSS. It uses grid positioning (`col-start-*`, `row-start-*`, `row-span-*`) to place the event within the calendar grid and span multiple time slots. Styling includes margin (`m-1`), flexbox layout (`flex flex-col`), rounded corners (`rounded-lg`), borders (`border border-*-*/*`), background colors with opacity (`bg-*-*/*`), and padding (`p-1`). Internal `span` elements display event details (time, title, location) with specific text styling (size, color, weight) adapted for light and dark modes.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/overflow.mdx#2025-04-22_snippet_12

LANGUAGE: html
CODE:
```
<div className="col-start-3 row-span-4 row-start-2 m-1 flex flex-col rounded-lg border border-blue-700/10 bg-blue-400/20 p-1 dark:border-sky-500 dark:bg-sky-600/50">
          <span className="text-xs text-blue-600 dark:text-sky-100">5 AM</span>
          <span className="text-xs font-medium text-blue-600 dark:text-sky-100">Flight to Vancouver</span>
          <span className="text-xs text-blue-600 dark:text-sky-100">Toronto YYZ</span>
        </div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-4 row-span-4 row-start-3 m-1 flex flex-col rounded-lg border border-purple-700/10 bg-purple-400/20 p-1 dark:border-fuchsia-500 dark:bg-fuchsia-600/50">
          <span className="text-xs text-purple-600 dark:text-fuchsia-100">6 AM</span>
          <span className="text-xs font-medium text-purple-600 dark:text-fuchsia-100">Breakfast</span>
          <span className="text-xs text-purple-600 dark:text-fuchsia-100">Mel's Diner</span>
        </div>
```

----------------------------------------

TITLE: Upgrading Tailwind CSS with the CLI
DESCRIPTION: Command to install the latest version of Tailwind CSS using the Tailwind CLI. This upgrades the project to Tailwind CSS v4.1.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4-1/index.mdx#2025-04-22_snippet_0

LANGUAGE: sh
CODE:
```
# [!code filename:Using the Tailwind CLI]
npm install tailwindcss@latest @tailwindcss/cli@latest
```

----------------------------------------

TITLE: Combining Multiple Tailwind Variants
DESCRIPTION: Demonstrates how to combine multiple Tailwind variants like dark mode, breakpoint, data attribute, and hover to apply styles under specific conditions.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/styling-with-utility-classes.mdx#_snippet_21

LANGUAGE: HTML
CODE:
```
<!-- [!code filename:HTML] -->
<!-- [!code classes:dark:lg:data-current:hover:bg-indigo-600] -->
<button class="dark:lg:data-current:hover:bg-indigo-600 ...">
  <!-- ... -->
</button>
```

LANGUAGE: CSS
CODE:
```
/* [!code filename:Simplified CSS] */
@media (prefers-color-scheme: dark) and (width >= 64rem) {
  button[data-current]:hover {
    background-color: var(--color-indigo-600);
  }
}
```

----------------------------------------

TITLE: Aligning Item to End with self-end (HTML)
DESCRIPTION: Use the `self-end` utility to align an item to the end of the container's cross axis, regardless of the container's `align-items` value. This is useful for positioning a single item differently from its siblings.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/align-self.mdx#_snippet_3

LANGUAGE: html
CODE:
```
<!-- [!code classes:self-end] -->
<div class="flex items-stretch ...">
  <div>01</div>
  <div class="self-end ...">02</div>
  <div>03</div>
</div>
```

----------------------------------------

TITLE: Incorrect Dynamic Class Construction in React
DESCRIPTION: Example of incorrect class name construction in a React component that uses props to dynamically build class names, which Tailwind cannot detect.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/detecting-classes-in-source-files.mdx#2025-04-22_snippet_3

LANGUAGE: jsx
CODE:
```
function Button({ color, children }) {
  return <button className={`bg-${color}-600 hover:bg-${color}-500 ...`}>{children}</button>;
}
```

----------------------------------------

TITLE: Applying Focus-Within Effect with Tailwind CSS
DESCRIPTION: Illustrates the use of the 'focus-within' variant to style an element when it or one of its descendants has focus.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#2025-04-22_snippet_85

LANGUAGE: HTML
CODE:
```
<div class="focus-within:shadow-lg ...">
  <input type="text" />
</div>
```

----------------------------------------

TITLE: Styling Placeholder Text with placeholder Variant in Tailwind CSS
DESCRIPTION: Shows how to customize the appearance of placeholder text in input fields using the 'placeholder' variant to change text style and color.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#2025-04-22_snippet_27

LANGUAGE: html
CODE:
```
<!-- [!code classes:placeholder:italic,placeholder:text-gray-500] -->
<input
  class="placeholder:text-gray-500 placeholder:italic ..."
  placeholder="Search for anything..."
  type="text"
  name="search"
/>
```

----------------------------------------

TITLE: Flex Items with Shrink Example
DESCRIPTION: HTML example showing how to allow flex items to shrink using the shrink utility class.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/flex-shrink.mdx#2025-04-22_snippet_1

LANGUAGE: html
CODE:
```
<div class="flex ...">
  <div class="h-14 w-14 flex-none ...">01</div>
  <div class="h-14 w-64 shrink ...">02</div>
  <div class="h-14 w-14 flex-none ...">03</div>
</div>
```

----------------------------------------

TITLE: Configuring Custom Colors with Opacity in Tailwind
DESCRIPTION: Configuration for custom colors with opacity support using closure syntax in tailwind.config.js.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-1-7/index.mdx#2025-04-22_snippet_4

LANGUAGE: javascript
CODE:
```
module.exports = {
  theme: {
    colors: {
      primary: ({ opacityVariable }) => `rgba(var(--color-primary), var(${variable}, 1))`,
    },
  },
};
```

----------------------------------------

TITLE: Styling First and Last List Items with Tailwind CSS in Svelte
DESCRIPTION: Example showing how to remove top/bottom padding for first and last items in a list using Tailwind's first: and last: variants. The code demonstrates a responsive list of people with images and contact information.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#2025-04-22_snippet_5

LANGUAGE: svelte
CODE:
```
<ul role="list">
  {#each people as person}
    <!-- Remove top/bottom padding when first/last child -->
    <li class="flex py-4 first:pt-0 last:pb-0">
      <img class="h-10 w-10 rounded-full" src={person.imageUrl} alt="" />
      <div class="ml-3 overflow-hidden">
        <p class="text-sm font-medium text-gray-900 dark:text-white">{person.name}</p>
        <p class="truncate text-sm text-gray-500 dark:text-gray-400">{person.email}</p>
      </div>
    </li>
  {/each}
</ul>
```

----------------------------------------

TITLE: Implementing Dark Mode Toggle with System Theme Support in JavaScript
DESCRIPTION: This JavaScript snippet shows how to implement a dark mode toggle that supports light mode, dark mode, and system theme preference using localStorage and the matchMedia API.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/dark-mode.mdx#2025-04-22_snippet_5

LANGUAGE: JavaScript
CODE:
```
// On page load or when changing themes, best to add inline in `head` to avoid FOUC
document.documentElement.classList.toggle(
  "dark",
  localStorage.theme === "dark" ||
    (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)
);

// Whenever the user explicitly chooses light mode
localStorage.theme = "light";

// Whenever the user explicitly chooses dark mode
localStorage.theme = "dark";

// Whenever the user explicitly chooses to respect the OS preference
localStorage.removeItem("theme");
```

----------------------------------------

TITLE: Mobile-First Correct Example
DESCRIPTION: Example showing correct mobile-first approach using unprefixed utility for mobile and override for larger screens.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/responsive-design.mdx#2025-04-22_snippet_4

LANGUAGE: html
CODE:
```
<div class="text-center sm:text-left"></div>
```

----------------------------------------

TITLE: Form Input State Styling using Tailwind CSS Variants
DESCRIPTION: Example showing how to use Tailwind CSS state variants like invalid:, disabled:, and focus: to style form inputs. The code demonstrates styling for disabled states, validation states, and focus states with color and border modifications.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#2025-04-22_snippet_8

LANGUAGE: jsx
CODE:
```
<div className="mx-auto max-w-md border-x border-x-gray-200 px-6 py-5 dark:border-x-gray-800 dark:bg-gray-950/10">
  <form>
    <div>
      <label htmlFor="username" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
        Username
      </label>
      <div className="mt-1">
        <input
          type="text"
          name="username"
          id="username"
          className="block w-full rounded-md border border-gray-300 px-3 py-2 placeholder-gray-400 shadow-sm invalid:border-pink-500 invalid:text-pink-600 focus:border-sky-500 focus:outline focus:outline-sky-500 focus:invalid:border-pink-500 focus:invalid:outline-pink-500 disabled:border-gray-200 disabled:bg-gray-50 disabled:text-gray-500 disabled:shadow-none sm:text-sm dark:disabled:border-gray-700 dark:disabled:bg-gray-800/20"
          defaultValue="tbone"
          disabled
        />
      </div>
    </div>
    <!-- Additional form elements -->
  </form>
</div>
```

LANGUAGE: html
CODE:
```
<input
  type="text"
  value="tbone"
  disabled
  class="invalid:border-pink-500 invalid:text-pink-600 focus:border-sky-500 focus:outline focus:outline-sky-500 focus:invalid:border-pink-500 focus:invalid:outline-pink-500 disabled:border-gray-200 disabled:bg-gray-50 disabled:text-gray-500 disabled:shadow-none dark:disabled:border-gray-700 dark:disabled:bg-gray-800/20 ..."
/>
```

----------------------------------------

TITLE: Applying Responsive Grid Columns in HTML with Tailwind CSS
DESCRIPTION: This HTML snippet shows how to apply responsive grid column classes in Tailwind CSS using breakpoint prefixes. The `grid-cols-2` class applies by default, and the `sm:grid-cols-3` class overrides it at the `sm` breakpoint and above, changing the grid to 3 columns. This requires a Tailwind CSS setup.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/styling-with-utility-classes.mdx#_snippet_10

LANGUAGE: HTML
CODE:
```
<!-- [!code classes:sm:grid-cols-3] -->
<div class="grid grid-cols-2 sm:grid-cols-3">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Using Data Attribute for Dark Mode in Tailwind CSS
DESCRIPTION: This CSS snippet shows how to configure Tailwind CSS to use a data attribute instead of a class for activating dark mode.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/dark-mode.mdx#2025-04-22_snippet_3

LANGUAGE: CSS
CODE:
```
@import "tailwindcss";

@custom-variant dark (&:where([data-theme=dark], [data-theme=dark] *));
```

----------------------------------------

TITLE: Basic Background Color Example
DESCRIPTION: Demonstrates the basic usage of background color utilities with three different colored buttons
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/background-color.mdx#2025-04-22_snippet_1

LANGUAGE: html
CODE:
```
<button class="bg-blue-500 ...">Button A</button>
<button class="bg-cyan-500 ...">Button B</button>
<button class="bg-pink-500 ...">Button C</button>
```

----------------------------------------

TITLE: Container Utility Example
DESCRIPTION: Examples of using Tailwind's container utility for responsive maximum widths with optional centering and padding.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/max-width.mdx#2025-04-22_snippet_4

LANGUAGE: html
CODE:
```
<div class="container">
  <!-- ... -->
</div>

<div class="container mx-auto px-4">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Centering Text with Tailwind CSS
DESCRIPTION: Illustrates how to use the 'text-center' utility class to center-align text within an HTML paragraph element.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/text-align.mdx#2025-04-22_snippet_3

LANGUAGE: HTML
CODE:
```
<p class="text-center">So I started to walk into the water...</p>
```

----------------------------------------

TITLE: Styling Hover State with Tailwind (HTML)
DESCRIPTION: Shows how to apply styles to an element's hover state using Tailwind CSS state variants in pure HTML. The `hover:` prefix is used before a utility class (e.g., `hover:bg-sky-700`) to apply that style only when the element is hovered, serving as the HTML equivalent of the React example.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/styling-with-utility-classes.mdx#_snippet_5

LANGUAGE: html
CODE:
```
<!-- [!code word:hover\:bg-sky-700] -->
<button class="bg-sky-500 hover:bg-sky-700 ...">Save changes</button>
```

----------------------------------------

TITLE: Vertical Padding Example in Tailwind CSS
DESCRIPTION: Shows how to use the 'py-8' utility to add vertical padding to an element.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/padding.mdx#2025-04-22_snippet_5

LANGUAGE: html
CODE:
```
<!-- [!code classes:py-8] -->
<div class="py-8 ...">py-8</div>
```

----------------------------------------

TITLE: Installing Latest Tailwind CSS via NPM
DESCRIPTION: Command to install the latest version of Tailwind CSS using npm package manager.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-4/index.mdx#2025-04-22_snippet_13

LANGUAGE: bash
CODE:
```
$ npm install tailwindcss@latest
```

----------------------------------------

TITLE: Responsive Column Layout with Tailwind CSS
DESCRIPTION: This snippet shows how to create a responsive column layout that changes from 2 columns to 3 columns and adjusts the gap at the 'sm' breakpoint. It uses responsive utilities to modify the layout for different screen sizes.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/columns.mdx#2025-04-22_snippet_4

LANGUAGE: html
CODE:
```
<div class="columns-2 gap-4 sm:columns-3 sm:gap-8 ...">
  <img class="aspect-3/2 ..." src="/img/mountains-1.jpg" />
  <img class="aspect-square ..." src="/img/mountains-2.jpg" />
  <img class="aspect-square ..." src="/img/mountains-3.jpg" />
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Creating Screen-Reader Only Content with Tailwind CSS
DESCRIPTION: These snippets demonstrate how to use the 'sr-only' class to hide content visually while keeping it accessible to screen readers, and how to use 'not-sr-only' to reverse this effect responsively.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/display.mdx#2025-04-22_snippet_10

LANGUAGE: html
CODE:
```
<a href="#">
  <svg><!-- ... --></svg>
  <span class="sr-only">Settings</span>
</a>
```

LANGUAGE: html
CODE:
```
<a href="#">
  <svg><!-- ... --></svg>
  <span class="sr-only sm:not-sr-only">Settings</span>
</a>
```

----------------------------------------

TITLE: Correct Static Class Mapping in React
DESCRIPTION: Properly formatted React component that maps props to complete class names, allowing Tailwind to detect and generate all necessary utilities.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/detecting-classes-in-source-files.mdx#2025-04-22_snippet_4

LANGUAGE: jsx
CODE:
```
function Button({ color, children }) {
  const colorVariants = {
    blue: "bg-blue-600 hover:bg-blue-500",
    red: "bg-red-600 hover:bg-red-500",
  };

  return <button className={`${colorVariants[color]} ...`}>{children}</button>;
}
```

----------------------------------------

TITLE: Scanning for Classes in JSX - JSX - Tailwind CSS
DESCRIPTION: Provides an example of a React (JSX) component using various Tailwind CSS classes. This snippet illustrates how Tailwind scans source files like `.jsx` to identify used class names (`px-4`, `py-2`, etc.) for CSS generation.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/styling-with-utility-classes.mdx#_snippet_20

LANGUAGE: JSX
CODE:
```
export default function Button({ size, children }) {
  let sizeClasses = {
    md: "px-4 py-2 rounded-md text-base",
    lg: "px-5 py-3 rounded-lg text-lg",
  }[size];

  return (
    <button type="button" className={`font-bold ${sizeClasses}`}>
      {children}
    </button>
  );
}
```

----------------------------------------

TITLE: Generated CSS for Tailwind CSS sm:grid-cols-3 Utility
DESCRIPTION: This CSS snippet shows the generated output for the Tailwind CSS `sm:grid-cols-3` utility class. It uses a standard CSS media query `@media (width >= 40rem)` to apply `grid-template-columns: repeat(3, minmax(0, 1fr))` when the viewport width is 40rem or greater. This illustrates how Tailwind's breakpoint prefixes translate to standard CSS.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/styling-with-utility-classes.mdx#_snippet_11

LANGUAGE: CSS
CODE:
```
/* [!code filename: Generated CSS] */
.sm\:grid-cols-3 {
  @media (width >= 40rem) {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}
```

----------------------------------------

TITLE: Basic Text Color Example - HTML
DESCRIPTION: Demonstrates basic text color utility usage with blue and sky color variants, including dark mode support.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/color.mdx#2025-04-22_snippet_1

LANGUAGE: html
CODE:
```
<p class="text-blue-600 dark:text-sky-400">The quick brown fox...</p>
```

----------------------------------------

TITLE: Hover State Background Color Example
DESCRIPTION: Demonstrates how to apply background color changes on hover using the hover modifier
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/background-color.mdx#2025-04-22_snippet_3

LANGUAGE: html
CODE:
```
<button class="bg-indigo-500 hover:bg-fuchsia-500 ...">Save changes</button>
```

----------------------------------------

TITLE: Responsive Background Position Example Grid in React/JSX
DESCRIPTION: This JSX code creates a responsive grid showcasing different background position utilities in Tailwind CSS. It implements a scrollable container on small screens and a grid layout on larger screens, with each example showing how a specific background position class affects the placement of an image within a fixed container.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/background-position.mdx#2025-04-22_snippet_4

LANGUAGE: jsx
CODE:
```
<div className="w-full">
  <div className="flex snap-x scroll-p-4 items-end overflow-x-scroll overflow-y-hidden p-8 pt-16 sm:grid sm:grid-cols-3 sm:gap-16">
    <div className="relative w-32 shrink-0 snap-start snap-always sm:w-auto">
      <p className="absolute inset-x-0 top-0 mb-3 -translate-y-8 text-center font-mono text-xs font-medium text-gray-500 dark:text-gray-400">
        bg-top-left
      </p>
      <div className="group relative mx-auto h-20 w-20 rounded-lg">
        <div className="relative z-10 h-full w-full rounded-md bg-[url(https://images.unsplash.com/photo-1554629947-334ff61d85dc?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&h=1000&q=90)] bg-[size:8rem] bg-top-left outline -outline-offset-1 outline-black/10"></div>
        <img
          className="absolute top-0 left-0 h-32 w-32 max-w-none overflow-hidden rounded-md opacity-0 sm:group-hover:opacity-25"
          src="https://images.unsplash.com/photo-1554629947-334ff61d85dc?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&h=1000&q=90"
        />
      </div>
    </div>
    <div className="relative w-32 shrink-0 snap-start snap-always sm:w-auto">
      <p className="absolute inset-x-0 top-0 mb-3 -translate-y-8 text-center font-mono text-xs font-medium text-gray-500 dark:text-gray-400">
        bg-top
      </p>
      <div className="group relative mx-auto h-20 w-20 rounded-lg">
        <div className="relative z-10 h-full w-full rounded-md bg-[url(https://images.unsplash.com/photo-1554629947-334ff61d85dc?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&h=1000&q=90)] bg-[size:8rem] bg-top outline -outline-offset-1 outline-black/10"></div>
        <img
          className="absolute top-0 -left-6 h-32 w-32 max-w-none overflow-hidden rounded-md opacity-0 sm:group-hover:opacity-25"
          src="https://images.unsplash.com/photo-1554629947-334ff61d85dc?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&h=1000&q=90"
        />
      </div>
    </div>
    <div className="relative w-32 shrink-0 snap-start snap-always sm:w-auto">
      <p className="absolute inset-x-0 top-0 mb-3 -translate-y-8 text-center font-mono text-xs font-medium text-gray-500 dark:text-gray-400">
        bg-top-right
      </p>
      <div className="group relative mx-auto h-20 w-20 rounded-lg">
        <div className="relative z-10 h-full w-full rounded-md bg-[url(https://images.unsplash.com/photo-1554629947-334ff61d85dc?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&h=1000&q=90)] bg-[size:8rem] bg-top-right outline -outline-offset-1 outline-black/10"></div>
        <img
          className="absolute top-0 right-0 h-32 w-32 max-w-none overflow-hidden rounded-md opacity-0 sm:group-hover:opacity-25"
          src="https://images.unsplash.com/photo-1554629947-334ff61d85dc?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&h=1000&q=90"
        />
      </div>
    </div>
    <div className="relative w-32 shrink-0 snap-start snap-always sm:w-auto">
      <p className="absolute inset-x-0 top-0 mb-3 -translate-y-8 text-center font-mono text-xs font-medium text-gray-500 dark:text-gray-400">
        bg-left
      </p>
      <div className="group relative mx-auto h-20 w-20 rounded-lg">
        <div className="relative z-10 h-full w-full rounded-md bg-[url(https://images.unsplash.com/photo-1554629947-334ff61d85dc?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&h=1000&q=90)] bg-[size:8rem] bg-left outline -outline-offset-1 outline-black/10"></div>
        <img
          className="absolute -top-6 left-0 h-32 w-32 max-w-none overflow-hidden rounded-md opacity-0 sm:group-hover:opacity-25"
          src="https://images.unsplash.com/photo-1554629947-334ff61d85dc?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&h=1000&q=90"
        />
      </div>
    </div>
    <div className="relative w-32 shrink-0 snap-start snap-always sm:w-auto">
      <p className="absolute inset-x-0 top-0 mb-3 -translate-y-8 text-center font-mono text-xs font-medium text-gray-500 dark:text-gray-400">
        bg-center
      </p>
      <div className="group relative mx-auto h-20 w-20 rounded-lg">
        <div className="relative z-10 h-full w-full rounded-md bg-[url(https://images.unsplash.com/photo-1554629947-334ff61d85dc?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&h=1000&q=90)] bg-[size:8rem] bg-center outline -outline-offset-1 outline-black/10"></div>
        <img
          className="absolute -top-6 -left-6 h-32 w-32 max-w-none overflow-hidden rounded-md opacity-0 sm:group-hover:opacity-25"
          src="https://images.unsplash.com/photo-1554629947-334ff61d85dc?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&h=1000&q=90"
        />
      </div>
    </div>
    <div className="relative w-32 shrink-0 snap-start snap-always sm:w-auto">
      <p className="absolute inset-x-0 top-0 mb-3 -translate-y-8 text-center font-mono text-xs font-medium text-gray-500 dark:text-gray-400">
        bg-right
      </p>
      <div className="group relative mx-auto h-20 w-20 rounded-lg">
        <div className="relative z-10 h-full w-full rounded-md bg-[url(https://images.unsplash.com/photo-1554629947-334ff61d85dc?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&h=1000&q=90)] bg-[size:8rem] bg-right outline -outline-offset-1 outline-black/10"></div>
        <img
          className="absolute -top-6 right-0 h-32 w-32 max-w-none overflow-hidden rounded-md opacity-0 sm:group-hover:opacity-25"
          src="https://images.unsplash.com/photo-1554629947-334ff61d85dc?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&h=1000&q=90"
        />
      </div>
    </div>
    <div className="relative w-32 shrink-0 snap-start snap-always sm:w-auto">
      <p className="absolute inset-x-0 top-0 mb-3 -translate-y-8 text-center font-mono text-xs font-medium text-gray-500 dark:text-gray-400">
        bg-bottom-left
      </p>
      <div className="group relative mx-auto h-20 w-20 rounded-lg">
        <div className="relative z-10 h-full w-full rounded-md bg-[url(https://images.unsplash.com/photo-1554629947-334ff61d85dc?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&h=1000&q=90)] bg-[size:8rem] bg-bottom-left outline -outline-offset-1 outline-black/10"></div>
        <img
          className="absolute -top-12 left-0 h-32 w-32 max-w-none overflow-hidden rounded-md opacity-0 sm:group-hover:opacity-25"
          src="https://images.unsplash.com/photo-1554629947-334ff61d85dc?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&h=1000&q=90"
        />
      </div>
    </div>
    <div className="relative w-32 shrink-0 snap-start snap-always sm:w-auto">
      <p className="absolute inset-x-0 top-0 mb-3 -translate-y-8 text-center font-mono text-xs font-medium text-gray-500 dark:text-gray-400">
        bg-bottom
      </p>
      <div className="group relative mx-auto h-20 w-20 rounded-lg">
        <div className="relative z-10 h-full w-full rounded-md bg-[url(https://images.unsplash.com/photo-1554629947-334ff61d85dc?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&h=1000&q=90)] bg-[size:8rem] bg-bottom outline -outline-offset-1 outline-black/10"></div>
        <img
          className="absolute -top-12 -left-6 h-32 w-32 max-w-none overflow-hidden rounded-md opacity-0 sm:group-hover:opacity-25"
          src="https://images.unsplash.com/photo-1554629947-334ff61d85dc?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&h=1000&q=90"
        />
      </div>
    </div>
    <div className="relative -mx-8 w-48 shrink-0 snap-start snap-always sm:w-auto">
      <p className="absolute inset-x-0 top-0 mb-3 -translate-y-8 text-center font-mono text-xs font-medium text-gray-500 dark:text-gray-400">
        bg-bottom-right
      </p>
      <div className="group relative mx-auto h-20 w-20 rounded-lg">
        <div className="relative z-10 h-full w-full rounded-md bg-[url(https://images.unsplash.com/photo-1554629947-334ff61d85dc?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&h=1000&q=90)] bg-[size:8rem] bg-bottom-right outline -outline-offset-1 outline-black/10"></div>
        <img
          className="absolute -top-12 right-0 h-32 w-32 max-w-none overflow-hidden rounded-md opacity-0 sm:group-hover:opacity-25"
          src="https://images.unsplash.com/photo-1554629947-334ff61d85dc?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&h=1000&q=90"
        />
      </div>
    </div>
  </div>
</div>
```

----------------------------------------

TITLE: Using Arbitrary Properties with Tailwind CSS
DESCRIPTION: Demonstrates how to use arbitrary CSS properties with Tailwind CSS modifiers. This feature allows adding custom CSS properties directly in class names that can be combined with state modifiers like hover.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3/index.mdx#2025-04-22_snippet_12

LANGUAGE: html
CODE:
```
<div class="[mask-type:luminance] hover:[mask-type:alpha]">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Setting Column Gap with Tailwind CSS
DESCRIPTION: This snippet demonstrates how to use the gap-<width> utility to specify the width between columns in a multi-column layout. It creates a 3-column layout with an 8-unit gap between columns.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/columns.mdx#2025-04-22_snippet_3

LANGUAGE: html
CODE:
```
<div class="columns-3 gap-8 ...">
  <img class="aspect-3/2 ..." src="/img/mountains-1.jpg" />
  <img class="aspect-square ..." src="/img/mountains-2.jpg" />
  <img class="aspect-square ..." src="/img/mountains-3.jpg" />
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Using the New Line-Height Shorthand in Tailwind CSS
DESCRIPTION: This HTML snippet demonstrates the new line-height shorthand syntax in Tailwind CSS, allowing you to set both font-size and line-height with a single utility class. It shows the transition from the old `text-lg leading-7` syntax to the new `text-lg/7` syntax.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-3/index.mdx#_snippet_30

LANGUAGE: HTML
CODE:
```
<p class="text-lg leading-7 ..."><!-- [!code --] --><p class="text-lg/7 ..."><!-- [!code ++] -->
  So I started to walk into the water. I won't lie to you boys, I was terrified. But I pressed on, and as I made my way
  past the breakers a strange calm came over me. I don't know if it was divine intervention or the kinship of all living
  things but I tell you Jerry at that moment, I <em>was</em> a marine biologist.
</p>
```

----------------------------------------

TITLE: Configuring Prettier for Tailwind CSS
DESCRIPTION: JSON configuration for .prettierrc file that enables the Tailwind CSS plugin in Prettier, which is necessary for automatic class sorting to work.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/automatic-class-sorting-with-prettier/index.mdx#2025-04-22_snippet_2

LANGUAGE: json
CODE:
```
{
  "plugins": ["prettier-plugin-tailwindcss"]
}
```

----------------------------------------

TITLE: React Combobox Implementation
DESCRIPTION: Example of implementing a filtered combobox component using Headless UI in React. Shows basic string comparison filtering with a list of people names.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/headless-ui-v1-5/index.mdx#2025-04-22_snippet_0

LANGUAGE: jsx
CODE:
```
import { useState } from 'react'
import { Combobox } from '@headlessui/react'

const people = [
  'Wade Cooper',
  'Arlene McCoy',
  'Devon Webb',
  'Tom Cook',
  'Tanya Fox',
  'Hellen Schmidt',
]

function MyCombobox() {
  const [selectedPerson, setSelectedPerson] = useState(people[0])
  const [query, setQuery] = useState('')

  const filteredPeople =
    query === ''
      ? people
      : people.filter((person) => {
          return person.toLowerCase().includes(query.toLowerCase())
        })

  return (
    <Combobox value={selectedPerson} onChange={setSelectedPerson}>
      <Combobox.Input onChange={(event) => setQuery(event.target.value)} />
      <Combobox.Options>
        {filteredPeople.map((person) => (
          <Combobox.Option key={person} value={person}>
            {person}
          </Combobox.Option>
        ))}
      </Combobox.Options>
    </Combobox>
  )
}
```

----------------------------------------

TITLE: Displaying, Collapsing, and Hiding Table Rows with Tailwind CSS in React JSX
DESCRIPTION: This React (JSX) code demonstrates three tables: one showing all rows, one hiding a row using the Tailwind 'collapse' class, and one hiding a row with the 'hidden' class. It requires Tailwind CSS and React as dependencies. Key parameters include CSS classes 'collapse' and 'hidden' which affect row visibility while impacting table layout differently. This example shows how to dynamically toggle row visibility in a React component using Tailwind's display utilities and is intended for UI demonstrations or documentation.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/visibility.mdx#2025-04-22_snippet_3

LANGUAGE: jsx
CODE:
```
<div className=\"py-8\">
  <div className=\"mb-3 pl-4 text-sm font-medium text-gray-500 dark:text-gray-400\">Showing all rows</div>
  <table className=\"w-full border-collapse border-y border-gray-400 bg-white text-sm dark:border-gray-500 dark:bg-gray-800\">
    <thead className=\"bg-gray-50 dark:bg-gray-700\">
      <tr>
        <th className=\"border border-gray-300 px-4 py-3 text-left font-semibold text-gray-900 first:border-l-0 last:border-r-0 dark:border-gray-600 dark:text-gray-200\">Invoice #</th>
        <th className=\"border border-gray-300 px-4 py-3 text-left font-semibold text-gray-900 first:border-l-0 last:border-r-0 dark:border-gray-600 dark:text-gray-200\">Client</th>
        <th className=\"border border-gray-300 px-4 py-3 text-right font-semibold text-gray-900 first:border-l-0 last:border-r-0 dark:border-gray-600 dark:text-gray-200\">Amount</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td className=\"border border-gray-300 px-4 py-3 text-gray-500 first:border-l-0 last:border-r-0 dark:border-gray-700 dark:text-gray-400\">#100</td>
        <td className=\"border border-gray-300 px-4 py-3 text-gray-500 first:border-l-0 last:border-r-0 dark:border-gray-700 dark:text-gray-400\">Pendant Publishing</td>
        <td className=\"border border-gray-300 px-4 py-3 text-right text-gray-500 tabular-nums first:border-l-0 last:border-r-0 dark:border-gray-700 dark:text-gray-400\">$2,000.00</td>
      </tr>
      <tr>
        <td className=\"border border-gray-300 px-4 py-3 text-gray-500 first:border-l-0 last:border-r-0 dark:border-gray-700 dark:text-gray-400\">#101</td>
        <td className=\"border border-gray-300 px-4 py-3 text-gray-500 first:border-l-0 last:border-r-0 dark:border-gray-700 dark:text-gray-400\">Kruger Industrial Smoothing</td>
        <td className=\"border border-gray-300 px-4 py-3 text-right text-gray-500 tabular-nums first:border-l-0 last:border-r-0 dark:border-gray-700 dark:text-gray-400\">$545.00</td>
      </tr>
      <tr>
        <td className=\"border border-gray-300 px-4 py-3 text-gray-500 first:border-l-0 last:border-r-0 dark:border-gray-700 dark:text-gray-400\">#102</td>
        <td className=\"border border-gray-300 px-4 py-3 text-gray-500 first:border-l-0 last:border-r-0 dark:border-gray-700 dark:text-gray-400\">J. Peterman</td>
        <td className=\"border border-gray-300 px-4 py-3 text-right text-gray-500 tabular-nums first:border-l-0 last:border-r-0 dark:border-gray-700 dark:text-gray-400\">$10,000.25</td>
      </tr>
    </tbody>
  </table>
  <div className=\"mt-10 mb-3 pl-4 text-sm font-medium text-gray-500 dark:text-gray-400\">Hiding a row using <code className=\"text-xs text-gray-700 dark:text-gray-300\">`collapse`</code></div>
  <table className=\"w-full border-collapse border-y border-gray-400 bg-white text-sm dark:border-gray-500 dark:bg-gray-800\">
    <thead className=\"bg-gray-50 dark:bg-gray-700\">
      <tr>
        <th className=\"border border-gray-300 px-4 py-3 text-left font-semibold text-gray-900 first:border-l-0 last:border-r-0 dark:border-gray-600 dark:text-gray-200\">Invoice #</th>
        <th className=\"border border-gray-300 px-4 py-3 text-left font-semibold text-gray-900 first:border-l-0 last:border-r-0 dark:border-gray-600 dark:text-gray-200\">Client</th>
        <th className=\"border border-gray-300 px-4 py-3 text-right font-semibold text-gray-900 first:border-l-0 last:border-r-0 dark:border-gray-600 dark:text-gray-200\">Amount</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td className=\"border border-gray-300 px-4 py-3 text-gray-500 first:border-l-0 last:border-r-0 dark:border-gray-700 dark:text-gray-400\">#100</td>
        <td className=\"border border-gray-300 px-4 py-3 text-gray-500 first:border-l-0 last:border-r-0 dark:border-gray-700 dark:text-gray-400\">Pendant Publishing</td>
        <td className=\"border border-gray-300 px-4 py-3 text-right text-gray-500 tabular-nums first:border-l-0 last:border-r-0 dark:border-gray-700 dark:text-gray-400\">$2,000.00</td>
      </tr>
      <tr className=\"collapse\">
        <td className=\"border border-gray-300 px-4 py-3 text-gray-500 first:border-l-0 last:border-r-0 dark:border-gray-700 dark:text-gray-400\">#101</td>
        <td className=\"border border-gray-300 px-4 py-3 text-gray-500 first:border-l-0 last:border-r-0 dark:border-gray-700 dark:text-gray-400\">Kruger Industrial Smoothing</td>
        <td className=\"border border-gray-300 px-4 py-3 text-right text-gray-500 tabular-nums first:border-l-0 last:border-r-0 dark:border-gray-700 dark:text-gray-400\">$545.00</td>
      </tr>
      <tr>
        <td className=\"border border-gray-300 px-4 py-3 text-gray-500 first:border-l-0 last:border-r-0 dark:border-gray-700 dark:text-gray-400\">#102</td>
        <td className=\"border border-gray-300 px-4 py-3 text-gray-500 first:border-l-0 last:border-r-0 dark:border-gray-700 dark:text-gray-400\">J. Peterman</td>
        <td className=\"border border-gray-300 px-4 py-3 text-right text-gray-500 tabular-nums first:border-l-0 last:border-r-0 dark:border-gray-700 dark:text-gray-400\">$10,000.25</td>
      </tr>
    </tbody>
  </table>
  <div className=\"mt-10 mb-3 pl-4 text-sm font-medium text-gray-500 dark:text-gray-400\">Hiding a row using <code className=\"text-xs text-gray-700 dark:text-gray-300\">`hidden`</code></div>
  <table className=\"w-full border-collapse border-y border-gray-400 bg-white text-sm dark:border-gray-500 dark:bg-gray-800\">
    <thead className=\"bg-gray-50 dark:bg-gray-700\">
      <tr>
        <th className=\"border border-gray-300 px-4 py-3 text-left font-semibold text-gray-900 first:border-l-0 last:border-r-0 dark:border-gray-600 dark:text-gray-200\">Invoice #</th>
        <th className=\"border border-gray-300 px-4 py-3 text-left font-semibold text-gray-900 first:border-l-0 last:border-r-0 dark:border-gray-600 dark:text-gray-200\">Client</th>
        <th className=\"border border-gray-300 px-4 py-3 text-right font-semibold text-gray-900 first:border-l-0 last:border-r-0 dark:border-gray-600 dark:text-gray-200\">Amount</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td className=\"border border-gray-300 px-4 py-3 text-gray-500 first:border-l-0 last:border-r-0 dark:border-gray-700 dark:text-gray-400\">#100</td>
        <td className=\"border border-gray-300 px-4 py-3 text-gray-500 first:border-l-0 last:border-r-0 dark:border-gray-700 dark:text-gray-400\">Pendant Publishing</td>
        <td className=\"border border-gray-300 px-4 py-3 text-right text-gray-500 tabular-nums first:border-l-0 last:border-r-0 dark:border-gray-700 dark:text-gray-400\">$2,000.00</td>
      </tr>
      <tr className=\"hidden\">
        <td className=\"border border-gray-300 px-4 py-3 text-gray-500 first:border-l-0 last:border-r-0 dark:border-gray-700 dark:text-gray-400\">#101</td>
        <td className=\"border border-gray-300 px-4 py-3 text-gray-500 first:border-l-0 last:border-r-0 dark:border-gray-700 dark:text-gray-400\">Kruger Industrial Smoothing</td>
        <td className=\"border border-gray-300 px-4 py-3 text-right text-gray-500 tabular-nums first:border-l-0 last:border-r-0 dark:border-gray-700 dark:text-gray-400\">$545.00</td>
      </tr>
      <tr>
        <td className=\"border border-gray-300 px-4 py-3 text-gray-500 first:border-l-0 last:border-r-0 dark:border-gray-700 dark:text-gray-400\">#102</td>
        <td className=\"border border-gray-300 px-4 py-3 text-gray-500 first:border-l-0 last:border-r-0 dark:border-gray-700 dark:text-gray-400\">J. Peterman</td>
        <td className=\"border border-gray-300 px-4 py-3 text-right text-gray-500 tabular-nums first:border-l-0 last:border-r-0 dark:border-gray-700 dark:text-gray-400\">$10,000.25</td>
      </tr>
    </tbody>
  </table>
</div>
```

----------------------------------------

TITLE: Animating with CSS Variables in React using Motion Library
DESCRIPTION: Shows how to use CSS variables generated by Tailwind CSS v4 for animations in React applications using the Motion library.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/upgrade-guide.mdx#2025-04-22_snippet_20

LANGUAGE: JSX
CODE:
```
<motion.div animate={{ backgroundColor: "var(--color-blue-500)" }} />
```

----------------------------------------

TITLE: Height & Size Utility Reference - Tailwind CSS
DESCRIPTION: Provides a comprehensive list of Tailwind CSS classes for controlling element height and setting both width and height (`size` utilities), along with the CSS properties they apply. It includes utilities for fixed sizes, percentages, viewport units, and custom values.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/height.mdx#_snippet_0

LANGUAGE: Mapping
CODE:
```
[
  ["h-<number>", "height: calc(var(--spacing) * <number>);"],
  ["h-<fraction>", "height: calc(<fraction> * 100%);"],
  ["h-auto", "height: auto;"],
  ["h-px", "height: 1px;"],
  ["h-full", "height: 100%;"],
  ["h-screen", "height: 100vh;"],
  ["h-dvh", "height: 100dvh;"],
  ["h-dvw", "height: 100dvw;"],
  ["h-lvh", "height: 100lvh;"],
  ["h-lvw", "height: 100lvw;"],
  ["h-svh", "height: 100svh;"],
  ["h-svw", "height: 100svw;"],
  ["h-min", "height: min-content;"],
  ["h-max", "height: max-content;"],
  ["h-fit", "height: fit-content;"],
  ["h-lh", "height: 1lh;"],
  ["h-(<custom-property>)", "height: var(<custom-property>);"],
  ["h-[<value>]", "height: <value>;"],
  ["size-<number>", "width: calc(var(--spacing) * <number>);\nheight: calc(var(--spacing) * <number>);"],
  ["size-<fraction>", "width: calc(<fraction> * 100%);\nheight: calc(<fraction> * 100%);"],
  ["size-auto", "width: auto;\nheight: auto;"],
  ["size-px", "width: 1px;\nheight: 1px;"],
  ["size-full", "width: 100%;\nheight: 100%;"],
  ["size-dvw", "width: 100dvw;\nheight: 100dvw;"],
  ["size-dvh", "width: 100dvh;\nheight: 100dvh;"],
  ["size-lvw", "width: 100lvw;\nheight: 100lvw;"],
  ["size-lvh", "width: 100lvh;\nheight: 100lvh;"],
  ["size-svw", "width: 100svw;\nheight: 100svw;"],
  ["size-svh", "width: 100svh;\nheight: 100svh;"],
  ["size-min", "width: min-content;\nheight: min-content;"],
  ["size-max", "width: max-content;\nheight: max-content;"],
  ["size-fit", "width: fit-content;\nheight: fit-content;"],
  ["size-(<custom-property>)", "width: var(<custom-property>);\nheight: var(<custom-property>);"],
  ["size-[<value>]", "width: <value>;\nheight: <value>;"]
]
```

----------------------------------------

TITLE: Rendering Basic Border Width Examples in Tailwind CSS
DESCRIPTION: Demonstrates the use of border and border-<number> utilities to set border widths for all sides of an element. Includes visual examples and corresponding HTML code.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/border-width.mdx#2025-04-22_snippet_2

LANGUAGE: HTML
CODE:
```
<div class="border border-indigo-600 ..."></div>
<div class="border-2 border-indigo-600 ..."></div>
<div class="border-4 border-indigo-600 ..."></div>
<div class="border-8 border-indigo-600 ..."></div>
```

----------------------------------------

TITLE: Implementing Group-Based Hover Effects with Svelte
DESCRIPTION: This Svelte code snippet demonstrates implementing group-based hover effects using Tailwind CSS. Dependencies include Tailwind CSS and a working Svelte environment. Key parameters like 'group/item' and 'group/edit' manipulate visibility and text color transitions on hover. The input is structured as an array of people objects, and the expected output is a styled list with hover effects. Constraints include adequate style classes and proper event listeners.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#2025-04-22_snippet_16

LANGUAGE: svelte
CODE:
```
<ul role="list">
  {#each people as person}
    <li class="group/item ...">
      <!-- ... -->
      <a class="group/edit invisible group-hover/item:visible ..." href="tel:{person.phone}">
        <span class="group-hover/edit:text-gray-700 ...">Call</span>
        <svg class="group-hover/edit:translate-x-0.5 group-hover/edit:text-gray-500 ..."><!-- ... --></svg>
      </a>
    </li>
  {/each}
</ul>
```

----------------------------------------

TITLE: Truncating Text Example
DESCRIPTION: HTML example showing how to truncate text with an ellipsis using Tailwind's truncate utility class.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/text-overflow.mdx#2025-04-22_snippet_1

LANGUAGE: html
CODE:
```
<p class="truncate">The longest word in any of the major...</p>
```

----------------------------------------

TITLE: Implementing Color Scheme and Motion Preferences in Tailwind CSS
DESCRIPTION: This snippet shows how Tailwind CSS implements utility classes for color scheme and motion preferences using media queries.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#2025-04-22_snippet_78

LANGUAGE: CSS
CODE:
```
@media (prefers-color-scheme: dark)
```

LANGUAGE: CSS
CODE:
```
@media (prefers-reduced-motion: no-preference)
```

LANGUAGE: CSS
CODE:
```
@media (prefers-reduced-motion: reduce)
```

----------------------------------------

TITLE: Focus Indicator Implementation
DESCRIPTION: Demonstrates implementation of improved focus indicators with outline utilities for better accessibility.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-1-9/index.mdx#2025-04-22_snippet_2

LANGUAGE: html
CODE:
```
<button class="focus:shadow-outline focus:outline-none ...">
  <!-- ... -->
</button>
```

LANGUAGE: html
CODE:
```
<!-- Use `outline-white` on dark backgrounds -->
<div class="bg-gray-900">
  <button class="focus:outline-white ...">
    <!-- ... -->
  </button>
</div>

<!-- Use `outline-black` on light backgrounds -->
<div class="bg-white">
  <button class="focus:outline-black ...">
    <!-- ... -->
  </button>
</div>
```

----------------------------------------

TITLE: Using Calc() with Arbitrary Values - HTML - Tailwind CSS
DESCRIPTION: Demonstrates incorporating CSS functions like `calc()` within arbitrary values. The `max-h-[calc(100dvh-(--spacing(6)))]` class sets a dynamic maximum height based on viewport height and a CSS variable.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/styling-with-utility-classes.mdx#_snippet_18

LANGUAGE: HTML
CODE:
```
<div class="max-h-[calc(100dvh-(--spacing(6)))]">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Applying Dark Mode Styles in HTML with Tailwind CSS
DESCRIPTION: Demonstrates how to use Tailwind CSS `dark:` prefixed utility classes alongside regular classes to apply different styles based on the user's color scheme preference. Shows examples for background color and text color.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/styling-with-utility-classes.mdx#_snippet_12

LANGUAGE: html
CODE:
```
<!-- [!code word:dark\:bg-gray-800] -->
<!-- prettier-ignore -->
<div class="bg-white dark:bg-gray-800 rounded-lg px-6 py-8 ring shadow-xl ring-gray-900/5">
  <div>
    <span class="inline-flex items-center justify-center rounded-md bg-indigo-500 p-2 shadow-lg">
      <svg
        class="h-6 w-6 text-white"

        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
        aria-hidden="true"
      >
        <!-- ... -->
      </svg>
    </span>
  </div>
  <!-- prettier-ignore -->
  <!-- [!code word:dark\:text-white] -->
  <h3 class="text-gray-900 dark:text-white mt-5 text-base font-medium tracking-tight ">Writes upside-down</h3>
  <!-- prettier-ignore -->
  <!-- [!code word:dark\:text-gray-400] -->
  <p class="text-gray-500 dark:text-gray-400 mt-2 text-sm ">
    The Zero Gravity Pen can be used to write in any orientation, including upside-down. It even works in outer space.
  </p>
</div>
```

----------------------------------------

TITLE: CSS Modules with Variables Example
DESCRIPTION: Shows alternative approach using CSS variables instead of @apply in CSS modules.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/compatibility.mdx#2025-04-22_snippet_6

LANGUAGE: css
CODE:
```
button {
  background: var(--color-blue-500);
}
```

----------------------------------------

TITLE: Text Wrapping with wrap-break-word Example
DESCRIPTION: Demonstrates the usage of wrap-break-word utility class to allow line breaks between letters in a word.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/overflow-wrap.mdx#2025-04-22_snippet_1

LANGUAGE: html
CODE:
```
<p class="wrap-break-word">The longest word in any of the major...</p>
```

----------------------------------------

TITLE: Configuring Tailwind CSS with CSS Variables
DESCRIPTION: Demonstrates the new CSS-based configuration approach for Tailwind CSS, showing how to import the framework and configure custom colors and fonts using CSS variables.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/2023-07-18-tailwind-connect-2023-recap/index.mdx#2025-04-22_snippet_1

LANGUAGE: css
CODE:
```
@import "tailwindcss";
@import "./fonts" layer(base);

:theme {
  --colors-neon-pink: oklch(71.7% 0.25 360);
  --colors-neon-lime: oklch(91.5% 0.258 129);
  --colors-neon-cyan: oklch(91.3% 0.139 195.8);

  --font-family-sans: "Inter", sans-serif;
  --font-family-display: "Satoshi", sans-serif;
}
```

----------------------------------------

TITLE: Styling Hover State with Tailwind (React)
DESCRIPTION: Shows how to apply styles to an element's hover state using Tailwind CSS state variants within a React/JSX context. The `hover:` prefix is used before a utility class (e.g., `hover:bg-sky-700`) to apply that style only when the element is hovered.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/styling-with-utility-classes.mdx#_snippet_4

LANGUAGE: javascript
CODE:
```
<div className="grid place-items-center">
  <button className="rounded-full bg-sky-500 px-5 py-2 text-sm leading-5 font-semibold text-white hover:bg-sky-700">
    Save changes
  </button>
</div>
```

----------------------------------------

TITLE: Background Color Opacity Example
DESCRIPTION: Shows how to control background color opacity using the opacity modifier with different percentages
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/background-color.mdx#2025-04-22_snippet_2

LANGUAGE: html
CODE:
```
<button class="bg-sky-500/100 ..."></button>
<button class="bg-sky-500/75 ..."></button>
<button class="bg-sky-500/50 ..."></button>
```

----------------------------------------

TITLE: Logical Properties for Margins in HTML with Tailwind CSS
DESCRIPTION: Demonstrates the use of ms-8 and me-8 utility classes for setting margin-inline-start and margin-inline-end in both LTR and RTL contexts.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/margin.mdx#2025-04-22_snippet_6

LANGUAGE: HTML
CODE:
```
<div>
  <div dir="ltr">
    <div class="ms-8 ...">ms-8</div>
    <div class="me-8 ...">me-8</div>
  </div>
  <div dir="rtl">
    <div class="ms-8 ...">ms-8</div>
    <div class="me-8 ...">me-8</div>
  </div>
</div>
```

----------------------------------------

TITLE: Implementing Content-Box Sizing in HTML with Tailwind CSS
DESCRIPTION: This example demonstrates using the 'box-content' utility class to exclude borders and padding from the element's size calculation. The content area maintains the specified dimensions (32×32) while the total rendered size increases to accommodate borders and padding.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/box-sizing.mdx#2025-04-22_snippet_1

LANGUAGE: html
CODE:
```
<div class="box-content size-32 border-4 p-4 ...">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Overriding Default Transitions HTML
DESCRIPTION: Illustrates how to override the configured default transition duration and timing function by applying explicit `duration-` and `ease-` utilities. This provides flexibility for transitions that deviate from the global defaults.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v2/index.mdx#_snippet_28

LANGUAGE: html
CODE:
```
<button class="transition duration-300 ease-out ...">We're back baby</button>
```

----------------------------------------

TITLE: Using motion-safe Variant for Opt-in Transitions
DESCRIPTION: Example showing how to explicitly opt-in to motion effects using the motion-safe variant, ensuring transitions only apply for users who haven't requested reduced motion.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-1-6/index.mdx#2025-04-22_snippet_3

LANGUAGE: html
CODE:
```
<div class="duration-150 ease-in-out motion-safe:transition ... ..."></div>
```

----------------------------------------

TITLE: Using property-only checks with Tailwind CSS @supports variants
DESCRIPTION: This example shows a shorthand syntax for checking if a CSS property is supported, without specifying a value. It tests for backdrop-filter support to create a more visually appealing transparent background with blur.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#2025-04-22_snippet_48

LANGUAGE: html
CODE:
```
<!-- [!code classes:supports-backdrop-filter:bg-black/25,supports-backdrop-filter:backdrop-blur] -->
<div class="bg-black/75 supports-backdrop-filter:bg-black/25 supports-backdrop-filter:backdrop-blur ...">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Aligning Item to Start using self-start (HTML)
DESCRIPTION: Use the `self-start` utility to align an item to the start of the container's cross axis, despite the container's `align-items` value.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/align-self.mdx#_snippet_1

LANGUAGE: html
CODE:
```
<!-- [!code classes:self-start] -->
<div class="flex items-stretch ...">
  <div>01</div>
  <div class="self-start ...">02</div>
  <div>03</div>
</div>
```

----------------------------------------

TITLE: Rendering Percentage Width Example with Example Component (JSX)
DESCRIPTION: Uses the `Example` and `Figure` components to render a visual demonstration of percentage-based width utilities (`w-1/2`, `w-2/5`, `w-full`, etc.). The inner JSX uses flex containers (`flex`) to arrange `div` elements styled with different `w-<fraction>` classes, showing how they occupy portions of their parent container. Responsive classes (`sm:flex`) hide some rows on smaller screens.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/width.mdx#2025-04-22_snippet_5

LANGUAGE: jsx
CODE:
```
<Figure>

<Example>
  {
    <div className="space-y-4 font-mono text-xs font-bold text-white">
      <div className="flex gap-x-4">
        <div className="w-1/2 rounded-lg bg-violet-500 px-4 py-2 text-center">w-1/2</div>
        <div className="w-1/2 rounded-lg bg-violet-500 px-4 py-2 text-center">w-1/2</div>
      </div>
      <div className="flex gap-x-4">
        <div className="w-2/5 rounded-lg bg-violet-500 px-4 py-2 text-center">w-2/5</div>
        <div className="w-3/5 rounded-lg bg-violet-500 px-4 py-2 text-center">w-3/5</div>
      </div>
      <div className="flex gap-x-4">
        <div className="w-1/3 rounded-lg bg-violet-500 px-4 py-2 text-center">w-1/3</div>
        <div className="w-2/3 rounded-lg bg-violet-500 px-4 py-2 text-center">w-2/3</div>
      </div>
      <div className="hidden gap-x-4 sm:flex">
        <div className="w-1/4 rounded-lg bg-violet-500 px-4 py-2 text-center">w-1/4</div>
        <div className="w-3/4 rounded-lg bg-violet-500 px-4 py-2 text-center">w-3/4</div>
      </div>
      <div className="hidden gap-x-4 sm:flex">
        <div className="w-1/5 rounded-lg bg-violet-500 px-4 py-2 text-center">w-1/5</div>
        <div className="w-4/5 rounded-lg bg-violet-500 px-4 py-2 text-center">w-4/5</div>
      </div>
      <div className="hidden gap-x-4 sm:flex">
        <div className="w-1/6 rounded-lg bg-violet-500 px-4 py-2 text-center">w-1/6</div>
        <div className="w-5/6 rounded-lg bg-violet-500 px-4 py-2 text-center">w-5/6</div>
      </div>
      <div className="w-full rounded-lg bg-violet-500 px-4 py-2 text-center font-mono text-white">w-full</div>
    </div>
  }
</Example>
```

----------------------------------------

TITLE: Styling Email Input and Validation Message with Peer Classes in HTML
DESCRIPTION: This snippet demonstrates how to use the 'peer' class and 'peer-invalid' variant to show a validation message for an email input field.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#2025-04-22_snippet_20

LANGUAGE: HTML
CODE:
```
<form>
  <label class="block">
    <span class="...">Email</span>
    <input type="email" class="peer ..." />
    <p class="invisible peer-invalid:visible ...">Please provide a valid email address.</p>
  </label>
</form>
```

----------------------------------------

TITLE: Demonstrating place-items-center in Tailwind CSS
DESCRIPTION: Shows how to use the place-items-center utility class to center grid items within their grid areas on both axes.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/place-items.mdx#2025-04-22_snippet_4

LANGUAGE: html
CODE:
```
<div class="grid h-56 grid-cols-3 place-items-center gap-4 ...">
  <div>01</div>
  <div>02</div>
  <div>03</div>
  <div>04</div>
  <div>05</div>
  <div>06</div>
</div>
```

----------------------------------------

TITLE: Data Attribute Styling with Tailwind CSS
DESCRIPTION: Demonstrates how to use data-* variants to conditionally style elements based on data attributes. Shows both basic usage and generated CSS output.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-2/index.mdx#2025-04-22_snippet_8

LANGUAGE: html
CODE:
```
<div data-size="large" class="data-[size=large]:p-8">
  <!-- ... -->
</div>
```

LANGUAGE: css
CODE:
```
.data-\[size\=large\]\:p-8[data-size="large"] {
  padding: 2rem;
}
```

----------------------------------------

TITLE: Importing Default Tailwind CSS Structure
DESCRIPTION: Shows the base Tailwind CSS import structure with layer definitions for theme, base, components, and utilities
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/theme.mdx#2025-04-22_snippet_8

LANGUAGE: css
CODE:
```
@layer theme, base, components, utilities;

@import "./theme.css" layer(theme);
@import "./preflight.css" layer(base);
@import "./utilities.css" layer(utilities);
```

----------------------------------------

TITLE: Using Inline Flex Display Utility in Tailwind CSS
DESCRIPTION: HTML example demonstrating the inline-flex utility which creates an inline flex container that flows with text, useful for inline elements that need flex properties like aligning images with text.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/display.mdx#2025-04-22_snippet_4

LANGUAGE: html
CODE:
```
<!-- [!code classes:inline-flex] -->
<p>
  Today I spent most of the day researching ways to ...
  <span class="inline-flex items-baseline">
    <img src="/img/kramer.jpg" class="mx-1 size-5 self-center rounded-full" />
    <span>Kramer</span>
  </span>
  keeps telling me there is no way to make it work, that ...
</p>
```

----------------------------------------

TITLE: Configuring Custom Color Palette JavaScript
DESCRIPTION: Illustrates how to import and use the `tailwindcss/colors` module within `tailwind.config.js` to define a custom color palette for the theme. This allows developers to curate specific color shades from the extended palette. Requires a `tailwind.config.js` file.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v2/index.mdx#_snippet_1

LANGUAGE: JavaScript
CODE:
```
const colors = require("tailwindcss/colors");

module.exports = {
  theme: {
    colors: {
      gray: colors.trueGray,
      indigo: colors.indigo,
      red: colors.rose,
      yellow: colors.amber,
    },
  },
};
```

----------------------------------------

TITLE: Resizing Image to Cover Container using Tailwind CSS
DESCRIPTION: This snippet demonstrates how to use the 'object-cover' utility class to resize an image's content to cover its container. The image will fill the entire container while maintaining its aspect ratio.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/object-fit.mdx#2025-04-22_snippet_0

LANGUAGE: HTML
CODE:
```
<img class="h-48 w-96 object-cover ..." src="/img/mountains.jpg" />
```

----------------------------------------

TITLE: Container Query Examples
DESCRIPTION: Demonstrates the new built-in container query support including min, max and range queries.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4/index.mdx#2025-04-22_snippet_11

LANGUAGE: html
CODE:
```
<div class="@container">
  <div class="grid grid-cols-1 @sm:grid-cols-3 @lg:grid-cols-4">
    <!-- ... -->
  </div>
</div>
```

LANGUAGE: html
CODE:
```
<div class="@container">
  <div class="grid grid-cols-3 @max-md:grid-cols-1">
    <!-- ... -->
  </div>
</div>
```

LANGUAGE: html
CODE:
```
<div class="@container">
  <div class="flex @min-md:@max-xl:hidden">
    <!-- ... -->
  </div>
</div>
```

----------------------------------------

TITLE: Overriding Default Breakpoint
DESCRIPTION: Shows how to override a default theme variable value for breakpoints
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/theme.mdx#2025-04-22_snippet_12

LANGUAGE: css
CODE:
```
@import "tailwindcss";

@theme {
  --breakpoint-sm: 30rem;
}
```

----------------------------------------

TITLE: Implementing Border-Box Sizing in HTML with Tailwind CSS
DESCRIPTION: This example shows how to use the 'box-border' utility class to include borders and padding in the element's total size calculation. The element will maintain its defined dimensions (32×32) while the content area adjusts accordingly.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/box-sizing.mdx#2025-04-22_snippet_0

LANGUAGE: html
CODE:
```
<div class="box-border size-32 border-4 p-4 ...">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Basic Line Clamp Example in HTML
DESCRIPTION: Demonstrates how to use the line-clamp-3 utility class to truncate text after three lines in an article layout.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/line-clamp.mdx#2025-04-22_snippet_1

LANGUAGE: html
CODE:
```
<article>
  <time>Mar 10, 2020</time>
  <h2>Boost your conversion rate</h2>
  <p class="line-clamp-3">
    Nulla dolor velit adipisicing duis excepteur esse in duis nostrud occaecat mollit incididunt deserunt sunt. Ut ut
    sunt laborum ex occaecat eu tempor labore enim adipisicing minim ad. Est in quis eu dolore occaecat excepteur fugiat
    dolore nisi aliqua fugiat enim ut cillum. Labore enim duis nostrud eu. Est ut eiusmod consequat irure quis deserunt
    ex. Enim laboris dolor magna pariatur. Dolor et ad sint voluptate sunt elit mollit officia ad enim sit consectetur
    enim.
  </p>
  <div>
    <img src="/img/lindsay.jpg" />
    Lindsay Walton
  </div>
</article>
```

----------------------------------------

TITLE: Controlling Grid Column Start and End Positions in HTML with Tailwind CSS
DESCRIPTION: This HTML example shows how to use Tailwind CSS classes to control the starting and ending positions of grid items. It uses classes like 'col-start-1', 'col-end-3', and 'col-span-2' to precisely position elements within a grid layout.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/grid-column.mdx#2025-04-22_snippet_2

LANGUAGE: html
CODE:
```
<div class="grid grid-cols-6 gap-4">
  <div class="col-span-4 col-start-2 ...">01</div>
  <div class="col-start-1 col-end-3 ...">02</div>
  <div class="col-span-2 col-end-7 ...">03</div>
  <div class="col-start-1 col-end-7 ...">04</div>
</div>
```

----------------------------------------

TITLE: Adding Horizontal Space Between Flex Children in HTML with Tailwind CSS
DESCRIPTION: This snippet demonstrates how to use the 'space-x-4' utility class to add horizontal spacing between flex child elements.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/margin.mdx#2025-04-22_snippet_7

LANGUAGE: html
CODE:
```
<div class="flex space-x-4 ...">
  <div>01</div>
  <div>02</div>
  <div>03</div>
</div>
```

----------------------------------------

TITLE: Logical Property Padding Examples in Tailwind CSS
DESCRIPTION: Illustrates the usage of logical property padding utilities 'ps-8' and 'pe-8' in both left-to-right and right-to-left text directions.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/padding.mdx#2025-04-22_snippet_6

LANGUAGE: html
CODE:
```
<!-- [!code classes:ps-8,pe-8] -->
<!-- [!code word:dir="ltr"] -->
<!-- [!code word:dir="rtl"] -->
<div>
  <div dir="ltr">
    <div class="ps-8 ...">ps-8</div>
    <div class="pe-8 ...">pe-8</div>
  </div>
  <div dir="rtl">
    <div class="ps-8 ...">ps-8</div>
    <div class="pe-8 ...">pe-8</div>
  </div>
</div>
```

----------------------------------------

TITLE: Customizing Theme Variables with @theme Directive in CSS
DESCRIPTION: Demonstrates how to customize Tailwind theme variables including fonts, breakpoints, colors, and animation timing functions using the @theme directive.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/adding-custom-styles.mdx#2025-04-22_snippet_0

LANGUAGE: css
CODE:
```
@theme {
  --font-display: "Satoshi", "sans-serif";

  --breakpoint-3xl: 120rem;

  --color-avocado-100: oklch(0.99 0 0);
  --color-avocado-200: oklch(0.98 0.04 113.22);
  --color-avocado-300: oklch(0.94 0.11 115.03);
  --color-avocado-400: oklch(0.92 0.19 114.08);
  --color-avocado-500: oklch(0.84 0.18 117.33);
  --color-avocado-600: oklch(0.53 0.12 118.34);

  --ease-fluid: cubic-bezier(0.3, 0, 0, 1);
  --ease-snappy: cubic-bezier(0.2, 0, 0, 1);

  /* ... */
}
```

----------------------------------------

TITLE: Layout and Spacing Variables
DESCRIPTION: Defines spacing units and breakpoint values for responsive design, along with container width presets.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/theme.mdx#2025-04-22_snippet_20

LANGUAGE: css
CODE:
```
--spacing: 0.25rem;

--breakpoint-sm: 40rem;
--breakpoint-md: 48rem;
--breakpoint-lg: 64rem;
--breakpoint-xl: 80rem;
--breakpoint-2xl: 96rem;

--container-3xs: 16rem;
[...more container definitions...]
```

----------------------------------------

TITLE: Pulse Animation Example
DESCRIPTION: Example of a skeleton loader using the animate-pulse utility for loading state indication.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/animation.mdx#2025-04-22_snippet_3

LANGUAGE: html
CODE:
```
<div class="mx-auto w-full max-w-sm rounded-md border border-blue-300 p-4">
  <div class="flex animate-pulse space-x-4">
    <div class="size-10 rounded-full bg-gray-200"></div>
    <div class="flex-1 space-y-6 py-1">
      <div class="h-2 rounded bg-gray-200"></div>
      <div class="space-y-3">
        <div class="grid grid-cols-3 gap-4">
          <div class="col-span-2 h-2 rounded bg-gray-200"></div>
          <div class="col-span-1 h-2 rounded bg-gray-200"></div>
        </div>
        <div class="h-2 rounded bg-gray-200"></div>
      </div>
    </div>
  </div>
</div>
```

----------------------------------------

TITLE: Creating Simple Custom Utilities in TailwindCSS
DESCRIPTION: Shows how to create basic custom utilities using the @utility directive. Includes both HTML usage examples and CSS implementation.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/adding-custom-styles.mdx#2025-04-22_snippet_4

LANGUAGE: css
CODE:
```
@utility content-auto {
  content-visibility: auto;
}
```

LANGUAGE: html
CODE:
```
<div class="content-auto">
  <!-- ... -->
</div>
```

LANGUAGE: html
CODE:
```
<div class="hover:content-auto">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Arbitrary Container Query Values in TailwindCSS
DESCRIPTION: Demonstrates using arbitrary values for container queries using the @[...] syntax.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-2/index.mdx#2025-04-22_snippet_14

LANGUAGE: html
CODE:
```
<div class="@container">
  <div class="block @[618px]:flex">
    <!-- ... -->
  </div>
</div>
```

----------------------------------------

TITLE: Implementing Custom CSS with @layer in Tailwind
DESCRIPTION: Demonstrates how to add custom CSS using Tailwind's layer system for base styles and component classes.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/adding-custom-styles.mdx#2025-04-22_snippet_2

LANGUAGE: css
CODE:
```
@layer base {
  h1 {
    font-size: var(--text-2xl);
  }

  h2 {
    font-size: var(--text-xl);
  }
}

@layer components {
  .card {
    background-color: var(--color-white);
    border-radius: var(--rounded-lg);
    padding: var(--spacing-6);
    box-shadow: var(--shadow-xl);
  }
}
```

----------------------------------------

TITLE: Applying Styles Based on Data Attributes in HTML
DESCRIPTION: Demonstrates how to use the data-* variant in Tailwind CSS to conditionally apply styles based on the presence of data attributes or specific attribute values.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#2025-04-22_snippet_59

LANGUAGE: html
CODE:
```
<div data-active class="border border-gray-300 data-active:border-purple-500">
  <!-- ... -->
</div>

<div data-size="large" class="data-[size=large]:p-8">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Applying Multiple Filters - HTML - Tailwind CSS
DESCRIPTION: Demonstrates applying multiple Tailwind CSS filter utility classes (`blur-sm` and `grayscale`) to a single HTML element. These classes compose together to apply both effects simultaneously.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/styling-with-utility-classes.mdx#_snippet_14

LANGUAGE: HTML
CODE:
```
<div class="blur-sm grayscale">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Styling with Peer-Has Variant
DESCRIPTION: Shows how to use the peer class and peer-has-checked variant to hide an SVG icon when a checkbox is checked.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#2025-04-22_snippet_12

LANGUAGE: html
CODE:
```
<div>
  <label class="peer ...">
    <input type="checkbox" name="todo[1]" checked />
    Create a to do list
  </label>
  <svg class="peer-has-checked:hidden ..."><!-- ... --></svg>
</div>
```

----------------------------------------

TITLE: Sticky Header Table with TailwindCSS
DESCRIPTION: Implementation of a table with sticky headers using TailwindCSS utilities. Features border-spacing-0, sticky positioning, and responsive design with hidden columns at different breakpoints.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-1/index.mdx#2025-04-22_snippet_11

LANGUAGE: jsx
CODE:
```
<div className="isolate h-72 overflow-auto rounded-xl">
  <table className="min-w-full border-separate border-spacing-0">
    <thead className="bg-gray-50">
      <tr>
        <th scope="col" className="bg-opacity-75 sticky top-0 z-10 border-b border-gray-300 bg-gray-50 py-3.5 pr-3 pl-4 text-left text-sm font-semibold text-gray-900 backdrop-blur backdrop-filter sm:pl-6 lg:pl-8">
          <>Name</>
        </th>
        <th scope="col" className="bg-opacity-75 sticky top-0 z-10 hidden border-b border-gray-300 bg-gray-50 px-3 py-3.5 text-left text-sm font-semibold text-gray-900 backdrop-blur backdrop-filter lg:table-cell">
          <>Email</>
        </th>
        <th scope="col" className="bg-opacity-75 sticky top-0 z-10 border-b border-gray-300 bg-gray-50 px-3 py-3.5 text-left text-sm font-semibold text-gray-900 backdrop-blur backdrop-filter">
          <>Role</>
        </th>
      </tr>
    </thead>
    <tbody className="bg-white">
      <!-- Table body content -->
    </tbody>
  </table>
</div>
```

----------------------------------------

TITLE: Applying Conditional Styling with ARIA Checked Attribute in HTML
DESCRIPTION: This snippet demonstrates how to use the aria-checked:bg-sky-700 class to apply a background color when the aria-checked attribute is set to true.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#2025-04-22_snippet_53

LANGUAGE: html
CODE:
```
<div aria-checked="true" class="bg-gray-600 aria-checked:bg-sky-700">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Applying Hover State in Tailwind CSS
DESCRIPTION: Demonstrates how to apply a background color change on hover using Tailwind CSS classes.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#2025-04-22_snippet_0

LANGUAGE: html
CODE:
```
<button class="bg-sky-500 hover:bg-sky-700 ...">Save changes</button>
```

----------------------------------------

TITLE: Explicitly Registering External Sources in CSS
DESCRIPTION: CSS configuration to explicitly register external sources for Tailwind to scan, useful for including libraries or dependencies that might be ignored by default.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/detecting-classes-in-source-files.mdx#2025-04-22_snippet_6

LANGUAGE: css
CODE:
```
@import "tailwindcss";
@source "../node_modules/@acmecorp/ui-lib";
```

----------------------------------------

TITLE: Basic Snap-End Image Gallery HTML Structure
DESCRIPTION: HTML structure showing how to implement a basic image gallery with snap-end scrolling functionality using Tailwind CSS classes. Demonstrates the core snap-x container and snap-end child elements pattern.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/scroll-snap-align.mdx#2025-04-22_snippet_4

LANGUAGE: html
CODE:
```
<div class="snap-x ...">
  <div class="snap-end ...">
    <img src="/img/vacation-01.jpg" />
  </div>
  <div class="snap-end ...">
    <img src="/img/vacation-02.jpg" />
  </div>
  <div class="snap-end ...">
    <img src="/img/vacation-03.jpg" />
  </div>
  <div class="snap-end ...">
    <img src="/img/vacation-04.jpg" />
  </div>
  <div class="snap-end ...">
    <img src="/img/vacation-05.jpg" />
  </div>
  <div class="snap-end ...">
    <img src="/img/vacation-06.jpg" />
  </div>
</div>
```

----------------------------------------

TITLE: Using At-Rules in Arbitrary Variants with Tailwind CSS
DESCRIPTION: Shows how to incorporate at-rules like @media or @supports within arbitrary variants for conditional styling.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#2025-04-22_snippet_71

LANGUAGE: html
CODE:
```
<div class="flex [@supports(display:grid)]:grid">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Styling Radio Button Parent with :has() in HTML
DESCRIPTION: Example showing how to style a label element based on the checked state of its radio button input using Tailwind CSS :has() variant classes.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-4/index.mdx#2025-04-22_snippet_4

LANGUAGE: html
CODE:
```
<label class="has-[:checked]:bg-indigo-50 has-[:checked]:text-indigo-900 has-[:checked]:ring-indigo-500 ...">
  <svg fill="currentColor">
    <!-- ... -->
  </svg>
  Google Pay
  <input type="radio" class="accent-indigo-500 ..." />
</label>
```

----------------------------------------

TITLE: Using Blur Filter Classes in HTML
DESCRIPTION: Example showing how to apply different blur filter classes to images in HTML. The classes range from no blur (blur-none) to heavy blur (blur-2xl), demonstrating the visual effect of each blur level.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/filter-blur.mdx#2025-04-22_snippet_0

LANGUAGE: html
CODE:
```
<!-- [!code classes:blur-none,blur-sm,blur-lg,blur-2xl] -->
<img class="blur-none" src="/img/mountains.jpg" />
<img class="blur-sm" src="/img/mountains.jpg" />
<img class="blur-lg" src="/img/mountains.jpg" />
<img class="blur-2xl" src="/img/mountains.jpg" />
```

----------------------------------------

TITLE: Using Arbitrary Values in Tailwind Classes
DESCRIPTION: Shows how to use arbitrary values in Tailwind classes for precise styling, including pixel values, colors, and custom properties.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/adding-custom-styles.mdx#2025-04-22_snippet_1

LANGUAGE: html
CODE:
```
<div class="top-[117px] lg:top-[344px]">
  <!-- ... -->
</div>
```

LANGUAGE: html
CODE:
```
<div class="bg-[#bada55] text-[22px] before:content-['Festivus']">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Using Custom Theme Classes
DESCRIPTION: HTML example showing how to use custom theme classes including breakpoints, fonts, and colors.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4-alpha/index.mdx#2025-04-22_snippet_4

LANGUAGE: html
CODE:
```
<div class="max-w-lg 3xl:max-w-xl">
  <h1 class="font-display text-4xl">
    Data to <span class="text-neon-cyan">enrich</span> your online business
  </h1>
</div>
```

----------------------------------------

TITLE: Default Border Color Configuration in CSS
DESCRIPTION: Shows how to maintain v3 border color behavior in v4 using CSS variables.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/upgrade-guide.mdx#2025-04-22_snippet_12

LANGUAGE: css
CODE:
```
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }
}
```

----------------------------------------

TITLE: Basic Opacity Example
DESCRIPTION: Example demonstrating the use of different opacity utility classes in Tailwind CSS to create buttons with varying levels of transparency.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/opacity.mdx#2025-04-22_snippet_1

LANGUAGE: html
CODE:
```
<button class="bg-indigo-500 opacity-100 ..."></button>
<button class="bg-indigo-500 opacity-75 ..."></button>
<button class="bg-indigo-500 opacity-50 ..."></button>
<button class="bg-indigo-500 opacity-25 ..."></button>
```

----------------------------------------

TITLE: Applying Focus Ring Offset Utility HTML
DESCRIPTION: Extends the basic ring example by adding the `focus:ring-offset-2` utility. This creates a visual gap between the element's border and the ring, producing a "halo" effect when the element is focused, enhancing accessibility and visibility.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v2/index.mdx#_snippet_9

LANGUAGE: HTML
CODE:
```
<button class="focus:ring-opacity-50 focus:ring-2 focus:ring-blue-300 focus:ring-offset-2 focus:outline-none ...">
```

----------------------------------------

TITLE: Basic Example of min-width Utility in HTML
DESCRIPTION: Demonstrates the use of min-w-<number> utilities to set fixed minimum widths based on the spacing scale in Tailwind CSS.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/min-width.mdx#2025-04-22_snippet_1

LANGUAGE: HTML
CODE:
```
<div class="w-20 ...">
  <div class="min-w-80 ...">min-w-80</div>
  <div class="min-w-64 ...">min-w-64</div>
  <div class="min-w-48 ...">min-w-48</div>
  <div class="min-w-40 ...">min-w-40</div>
  <div class="min-w-32 ...">min-w-32</div>
  <div class="min-w-24 ...">min-w-24</div>
</div>
```

----------------------------------------

TITLE: Starting and Ending Grid Lines with Tailwind CSS (HTML)
DESCRIPTION: Shows how to use Tailwind CSS row-start and row-end utilities to position elements at specific grid lines. The example includes elements starting and ending at different row lines.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/grid-row.mdx#2025-04-22_snippet_2

LANGUAGE: html
CODE:
```
<div class="grid grid-flow-col grid-rows-3 gap-4">
  <div class="row-span-2 row-start-2 ...">01</div>
  <div class="row-span-2 row-end-3 ...">02</div>
  <div class="row-start-1 row-end-4 ...">03</div>
</div>
```

----------------------------------------

TITLE: Using Arbitrary Values in HTML
DESCRIPTION: Shows how to use arbitrary values directly in HTML classes for precise styling control. This example demonstrates setting a specific top position value.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-1/index.mdx#2025-04-22_snippet_15

LANGUAGE: html
CODE:
```
<div class="top-[117px]">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Adding Custom Colors to Tailwind CSS Theme
DESCRIPTION: This CSS snippet shows how to add custom colors to a Tailwind CSS project using the @theme directive. It defines new color variables that can be used throughout the project.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/colors.mdx#2025-04-22_snippet_8

LANGUAGE: css
CODE:
```
@import "tailwindcss";

@theme {
  --color-midnight: #121063;
  --color-tahiti: #3ab7bf;
  --color-bermuda: #78dcca;
}
```

----------------------------------------

TITLE: Applying Simultaneous Width/Height Utilities in HTML
DESCRIPTION: HTML code snippet demonstrating the use of Tailwind's `size-*` utility classes (`size-16`, `size-20`, `size-24`, `size-32`, `size-40`). These classes set both the `width` and `height` properties of the `div` elements simultaneously, typically based on the spacing scale. The comment indicates which classes are being demonstrated.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/width.mdx#2025-04-22_snippet_12

LANGUAGE: html
CODE:
```
<!-- [!code classes:size-16,size-20,size-24,size-32,size-40] -->
<div class="size-16 ...">size-16</div>
<div class="size-20 ...">size-20</div>
<div class="size-24 ...">size-24</div>
<div class="size-32 ...">size-32</div>
<div class="size-40 ...">size-40</div>
```

----------------------------------------

TITLE: Creating a Custom Dropdown with Headless UI in React
DESCRIPTION: This code snippet demonstrates how to create a custom dropdown menu using the @headlessui/react library. It showcases the Menu component with full keyboard navigation support, ARIA attribute management, and Tailwind CSS styling.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/headless-ui-unstyled-accessible-ui-components/index.mdx#2025-04-22_snippet_0

LANGUAGE: jsx
CODE:
```
import { Menu } from "@headlessui/react";

function MyDropdown() {
  return (
    <Menu as="div" className="relative">
      <Menu.Button className="rounded bg-blue-600 px-4 py-2 text-white ...">Options</Menu.Button>
      <Menu.Items className="absolute right-0 mt-1">
        <Menu.Item>
          {({ active }) => (
            <a className={`${active && "bg-blue-500 text-white"} ...`} href="/account-settings">
              Account settings
            </a>
          )}
        </Menu.Item>
        <Menu.Item>
          {({ active }) => (
            <a className={`${active && "bg-blue-500 text-white"} ...`} href="/documentation">
              Documentation
            </a>
          )}
        </Menu.Item>
        <Menu.Item disabled>
          <span className="opacity-75 ...">Invite a friend (coming soon!)</span>
        </Menu.Item>
      </Menu.Items>
    </Menu>
  );
}
```

----------------------------------------

TITLE: Demonstrating place-items-stretch in Tailwind CSS
DESCRIPTION: Illustrates the use of the place-items-stretch utility class to stretch grid items to fill their grid areas on both axes.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/place-items.mdx#2025-04-22_snippet_5

LANGUAGE: html
CODE:
```
<div class="grid h-56 grid-cols-3 place-items-stretch gap-4 ...">
  <div>01</div>
  <div>02</div>
  <div>03</div>
  <div>04</div>
  <div>05</div>
  <div>06</div>
</div>
```

----------------------------------------

TITLE: Space Between Content Grid Layout
DESCRIPTION: Shows how to use place-content-between to create equal spacing between grid items.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/place-content.mdx#2025-04-22_snippet_3

LANGUAGE: html
CODE:
```
<div class="grid h-48 grid-cols-2 place-content-between gap-4 ...">
  <div>01</div>
  <div>02</div>
  <div>03</div>
  <div>04</div>
</div>
```

----------------------------------------

TITLE: Using Arbitrary Grid Columns - HTML - Tailwind CSS
DESCRIPTION: Shows how to define a custom grid column layout using arbitrary values. The `grid-cols-[24rem_2.5rem_minmax(0,1fr)]` class sets a specific, non-standard grid template.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/styling-with-utility-classes.mdx#_snippet_17

LANGUAGE: HTML
CODE:
```
<div class="grid grid-cols-[24rem_2.5rem_minmax(0,1fr)]">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Custom Tailwind Import Configuration
DESCRIPTION: Advanced import configuration for starting from scratch without the default theme.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4-alpha/index.mdx#2025-04-22_snippet_6

LANGUAGE: css
CODE:
```
@import "tailwindcss/preflight" layer(base);
@import "tailwindcss/utilities" layer(utilities);

@theme {
  --color-gray-50: #f8fafc;
  --color-gray-100: #f1f5f9;
  --color-gray-200: #e2e8f0;
  /* ... */
  --color-green-800: #3f6212;
  --color-green-900: #365314;
  --color-green-950: #1a2e05;
}
```

----------------------------------------

TITLE: Forced Colors Mode Variant Usage in HTML with Tailwind CSS
DESCRIPTION: Shows how to use the new forced-colors variant to adjust styles specifically for forced colors mode, particularly useful for custom form controls.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-4/index.mdx#2025-04-22_snippet_11

LANGUAGE: html
CODE:
```
<form>
  <input type="checkbox" class="appearance-none forced-colors:appearance-auto ..." />
</form>
```

----------------------------------------

TITLE: Applying Dark Mode Responsive Hover Styles HTML
DESCRIPTION: Illustrates the combination of multiple variants: `lg:` (breakpoint), `dark:` (dark mode), and `hover:` (state) (`lg:dark:hover:bg-gray-50`) to apply styles that are conditional on all three factors. Requires `darkMode` enabled and necessary variants.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v2/index.mdx#_snippet_6

LANGUAGE: HTML
CODE:
```
<button class="lg:dark:bg-white lg:dark:hover:bg-gray-50 ...">
```

----------------------------------------

TITLE: Matching Full Viewport Height - Tailwind CSS HTML
DESCRIPTION: Shows how the `h-screen` utility can be applied to an element to make its height equal to 100% of the standard viewport height (`100vh`).
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/height.mdx#_snippet_3

LANGUAGE: html
CODE:
```
<!-- [!code classes:h-screen] -->
<div class="h-screen">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Using Group Hover Utility HTML
DESCRIPTION: Provides an example of the `group-hover` utility. In v2.0, `group-hover` and `focus-within` are enabled by default for the same properties that support `hover` and `focus`, making interactive group styling easier.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v2/index.mdx#_snippet_24

LANGUAGE: html
CODE:
```
<div class="group ...">
  <span class="group-hover:text-blue-600 ...">Da ba dee da ba daa</span>
</div>
```

----------------------------------------

TITLE: Creating Sticky Time Labels in Calendar Grid with Tailwind CSS (HTML)
DESCRIPTION: An HTML `div` element representing a time label (e.g., '1 PM') positioned in the first column (`col-start-1`) of a specific row (`row-start-*`) in a calendar grid. It uses Tailwind CSS classes for styling: `sticky left-0` makes it stick to the left side during horizontal scroll, `border-r` adds a right border, `bg-white dark:bg-gray-800` sets background colors for light/dark modes, `p-1.5` adds padding, `text-right` aligns text, and other classes style the text (size, weight, color, uppercase). This pattern is repeated for different time slots.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/overflow.mdx#2025-04-22_snippet_11

LANGUAGE: html
CODE:
```
<div className="sticky left-0 col-start-1 row-start-10 border-r border-gray-100 bg-white p-1.5 text-right text-xs font-medium text-gray-400 uppercase dark:border-gray-200/5 dark:bg-gray-800">
          1 PM
        </div>
```

LANGUAGE: html
CODE:
```
<div className="sticky left-0 col-start-1 row-start-11 border-r border-gray-100 bg-white p-1.5 text-right text-xs font-medium text-gray-400 uppercase dark:border-gray-200/5 dark:bg-gray-800">
          2 PM
        </div>
```

LANGUAGE: html
CODE:
```
<div className="sticky left-0 col-start-1 row-start-12 border-r border-gray-100 bg-white p-1.5 text-right text-xs font-medium text-gray-400 uppercase dark:border-gray-200/5 dark:bg-gray-800">
          3 PM
        </div>
```

LANGUAGE: html
CODE:
```
<div className="sticky left-0 col-start-1 row-start-13 border-r border-gray-100 bg-white p-1.5 text-right text-xs font-medium text-gray-400 uppercase dark:border-gray-200/5 dark:bg-gray-800">
          4 PM
        </div>
```

LANGUAGE: html
CODE:
```
<div className="sticky left-0 col-start-1 row-start-14 border-r border-gray-100 bg-white p-1.5 text-right text-xs font-medium text-gray-400 uppercase dark:border-gray-200/5 dark:bg-gray-800">
          5 PM
        </div>
```

LANGUAGE: html
CODE:
```
<div className="sticky left-0 col-start-1 row-start-15 border-r border-gray-100 bg-white p-1.5 text-right text-xs font-medium text-gray-400 uppercase dark:border-gray-200/5 dark:bg-gray-800">
          6 PM
        </div>
```

LANGUAGE: html
CODE:
```
<div className="sticky left-0 col-start-1 row-start-16 border-r border-gray-100 bg-white p-1.5 text-right text-xs font-medium text-gray-400 uppercase dark:border-gray-200/5 dark:bg-gray-800">
          7 PM
        </div>
```

LANGUAGE: html
CODE:
```
<div className="sticky left-0 col-start-1 row-start-17 border-r border-gray-100 bg-white p-1.5 text-right text-xs font-medium text-gray-400 uppercase dark:border-gray-200/5 dark:bg-gray-800">
          8 PM
        </div>
```

----------------------------------------

TITLE: Styling Heroicons with Tailwind CSS Classes
DESCRIPTION: An example of an SVG icon from Heroicons styled with Tailwind CSS utility classes. The example shows how to set the icon size and color using Tailwind's h-6, w-6, and text-indigo-500 classes, demonstrating how these icons can be easily integrated with Tailwind projects.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/introducing-heroicons/index.mdx#2025-04-22_snippet_0

LANGUAGE: html
CODE:
```
<svg
  class="h-6 w-6 text-indigo-500"
  xmlns="http://www.w3.org/2000/svg"
  fill="none"
  viewBox="0 0 24 24"
  stroke="currentColor"
>
  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />
</svg>
```

----------------------------------------

TITLE: Extending Theme with Custom Font
DESCRIPTION: Example of extending the default theme by adding a new custom font variable
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/theme.mdx#2025-04-22_snippet_10

LANGUAGE: css
CODE:
```
@import "tailwindcss";

@theme {
  --font-script: Great Vibes, cursive;
}
```

----------------------------------------

TITLE: Defining Tailwind CSS Gap Utility in Grid Layout
DESCRIPTION: Demonstrates the use of gap-4 utility class in a grid layout with two columns and four items. This example shows how to apply gap between both rows and columns.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/gap.mdx#2025-04-22_snippet_1

LANGUAGE: HTML
CODE:
```
<div class="grid grid-cols-2 gap-4">
  <div>01</div>
  <div>02</div>
  <div>03</div>
  <div>04</div>
</div>
```

----------------------------------------

TITLE: Configuring Screen Breakpoints in Tailwind
DESCRIPTION: Example of configuring simple screen breakpoints in Tailwind configuration for max-width and dynamic breakpoint support.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-2/index.mdx#2025-04-22_snippet_10

LANGUAGE: javascript
CODE:
```
module.exports = {
  theme: {
    screens: {
      sm: "640px",
      md: "768px",
      lg: "1024px",
      xl: "1280px",
      "2xl": "1536px",
    },
  },
};
```

----------------------------------------

TITLE: Hiding Content that Overflows in Tailwind CSS
DESCRIPTION: An example of using the overflow-hidden utility to clip any content within an element that overflows the bounds of that element. This is demonstrated with a profile card where the image is clipped at the card boundaries.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/overflow.mdx#2025-04-22_snippet_2

LANGUAGE: HTML
CODE:
```
<div class="overflow-hidden ...">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Updating PostCSS Configuration
DESCRIPTION: Changes required in PostCSS configuration when upgrading to v4, including removing postcss-import and autoprefixer, and using the new @tailwindcss/postcss package.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/upgrade-guide.mdx#2025-04-22_snippet_1

LANGUAGE: javascript
CODE:
```
export default {
  plugins: {
    "@tailwindcss/postcss": {},
  },
};
```

----------------------------------------

TITLE: Reduced Motion Accessibility with Tailwind CSS
DESCRIPTION: Example of respecting user preferences for reduced motion using Tailwind's motion-reduce variant. The code hides a spinning animation when the user has requested reduced motion, providing a more accessible experience.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#2025-04-22_snippet_37

LANGUAGE: html
CODE:
```
<button type="button" class="bg-indigo-500 ..." disabled>
  <svg class="animate-spin motion-reduce:hidden ..." viewBox="0 0 24 24"><!-- ... --></svg>
  Processing...
</button>
```

----------------------------------------

TITLE: Styling Focus State with Tailwind CSS
DESCRIPTION: Shows how to use the 'focus' variant to style an element when it has focus.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#2025-04-22_snippet_84

LANGUAGE: HTML
CODE:
```
<input class="border-gray-300 focus:border-blue-400 ..." />
```

----------------------------------------

TITLE: Applying Underline Text Decoration in HTML
DESCRIPTION: Shows how to use the underline utility class to add an underline to text elements.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/text-decoration-line.mdx#2025-04-22_snippet_1

LANGUAGE: html
CODE:
```
<!-- [!code classes:underline] -->
<p class="underline">The quick brown fox...</p>
```

----------------------------------------

TITLE: Interactive Button Example with React/JSX
DESCRIPTION: Complex example showing buttons with different transition durations implemented using React components and Tailwind CSS classes.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/transition-duration.mdx#2025-04-22_snippet_2

LANGUAGE: jsx
CODE:
```
<div className="flex flex-col justify-around gap-8 text-sm leading-6 font-bold text-white sm:flex-row sm:gap-0">
  <div className="flex shrink-0 flex-col items-center">
    <p className="mb-3 text-center font-mono text-xs font-medium text-gray-500 dark:text-gray-400">duration-150</p>
    <button className="rounded-md bg-violet-500 px-4 py-2 text-sm font-semibold text-white duration-150 ease-in-out hover:scale-125">
      Button A
    </button>
  </div>
  <div className="flex shrink-0 flex-col items-center">
    <p className="mb-3 text-center font-mono text-xs font-medium text-gray-500 dark:text-gray-400">duration-300</p>
    <button className="rounded-md bg-violet-500 px-4 py-2 text-sm font-semibold text-white duration-300 ease-in-out hover:scale-125">
      Button B
    </button>
  </div>
  <div className="flex shrink-0 flex-col items-center">
    <p className="mb-3 text-center font-mono text-xs font-medium text-gray-500 dark:text-gray-400">duration-700</p>
    <button className="rounded-md bg-violet-500 px-4 py-2 text-sm font-semibold text-white duration-700 ease-in-out hover:scale-125">
      Button C
    </button>
  </div>
</div>
```

----------------------------------------

TITLE: Applying Fixed Height Utilities - Tailwind CSS HTML
DESCRIPTION: Shows how to use `h-<number>` utilities (like `h-96`, `h-80`, etc.) to set the height of elements using predefined values from the Tailwind spacing scale. These classes apply a fixed height based on `calc(var(--spacing) * <number>)`.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/height.mdx#_snippet_1

LANGUAGE: html
CODE:
```
<!-- [!code classes:h-96,h-80,h-64,h-48,h-40,h-32,h-24] -->
<div class="h-96 ...">h-96</div>
<div class="h-80 ...">h-80</div>
<div class="h-64 ...">h-64</div>
<div class="h-48 ...">h-48</div>
<div class="h-40 ...">h-40</div>
<div class="h-32 ...">h-32</div>
<div class="h-24 ...">h-24</div>
```

----------------------------------------

TITLE: Demonstrating Custom Width Values (JSX)
DESCRIPTION: Uses the custom `UsingACustomValue` React component to explain and potentially demonstrate how to apply arbitrary width values in Tailwind CSS. The component is configured with props `utility="w"`, `value="5px"`, `name="width"`, and `variable="width"`, indicating it will show how to achieve `width: 5px;` likely using the `w-[5px]` syntax.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/width.mdx#2025-04-22_snippet_13

LANGUAGE: jsx
CODE:
```
<UsingACustomValue utility="w" value="5px" name="width" variable="width" />
```

----------------------------------------

TITLE: Defining Grid Column Spanning in HTML with Tailwind CSS
DESCRIPTION: This HTML snippet demonstrates how to use Tailwind CSS classes to create a grid layout with columns spanning multiple grid cells. It uses classes like 'grid', 'grid-cols-3', and 'col-span-2' to control the layout.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/grid-column.mdx#2025-04-22_snippet_1

LANGUAGE: html
CODE:
```
<div class="grid grid-cols-3 gap-4">
  <div class="...">01</div>
  <div class="...">02</div>
  <div class="...">03</div>
  <div class="col-span-2 ...">04</div>
  <div class="...">05</div>
  <div class="...">06</div>
  <div class="col-span-2 ...">07</div>
</div>
```

----------------------------------------

TITLE: Items Start Example
DESCRIPTION: HTML example demonstrating the items-start utility to align flex items to the start of the container's cross axis.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/align-items.mdx#2025-04-22_snippet_2

LANGUAGE: html
CODE:
```
<div class="flex items-start ...">
  <div class="py-4">01</div>
  <div class="py-12">02</div>
  <div class="py-8">03</div>
</div>
```

----------------------------------------

TITLE: Individual Border Sides Example
DESCRIPTION: Example showing how to apply border colors to individual sides of an element.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/border-color.mdx#2025-04-22_snippet_3

LANGUAGE: html
CODE:
```
<div class="border-4 border-indigo-200 border-t-indigo-500 ..."></div>
<div class="border-4 border-indigo-200 border-r-indigo-500 ..."></div>
<div class="border-4 border-indigo-200 border-b-indigo-500 ..."></div>
<div class="border-4 border-indigo-200 border-l-indigo-500 ..."></div>
```

----------------------------------------

TITLE: Composing * Variant with Data Attributes in React
DESCRIPTION: Shows how to combine the * variant with data attributes for conditional styling in a React component structure. Useful for complex component layouts.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-4/index.mdx#2025-04-22_snippet_7

LANGUAGE: jsx
CODE:
```
function Field({ children }) {
  return (
    <div className="data-[slot=description]:*:mt-4 ...">
      {children}
    </div>
  )
}

function Description({ children }) {
  return (
    <p data-slot="description" ...>{children}</p>
  )
}

function Example() {
  return (
    <Field>
      <Label>First name</Label>
      <Input />
      <Description>Please tell me you know your own name.</Description>
    </Field>
  )
}
```

----------------------------------------

TITLE: Containing Image Within Container using Tailwind CSS
DESCRIPTION: This example shows how to use the 'object-contain' utility class to resize an image's content to stay contained within its container. The entire image will be visible while preserving its aspect ratio.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/object-fit.mdx#2025-04-22_snippet_1

LANGUAGE: HTML
CODE:
```
<img class="h-48 w-96 object-contain ..." src="/img/mountains.jpg" />
```

----------------------------------------

TITLE: Installing Tailwind CSS v3.3
DESCRIPTION: Command to install the latest version of Tailwind CSS using npm. This command updates the project's dev dependencies with the newest Tailwind CSS release.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-3/index.mdx#_snippet_0

LANGUAGE: sh
CODE:
```
npm install -D tailwindcss@latest
```

----------------------------------------

TITLE: Stacking Tailwind CSS Variants
DESCRIPTION: Demonstrates how to stack multiple Tailwind CSS variants to target specific situations, such as dark mode, medium breakpoint, and hover state.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#2025-04-22_snippet_3

LANGUAGE: html
CODE:
```
<button class="dark:md:hover:bg-fuchsia-600 ...">Save changes</button>
```

----------------------------------------

TITLE: Creating Custom Variants with addVariant in Tailwind Config
DESCRIPTION: Demonstrates how to create a custom variant using the addVariant API in the Tailwind configuration file. This example creates a 'third' variant that targets the third child element.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-1/index.mdx#2025-04-22_snippet_14

LANGUAGE: javascript
CODE:
```
const plugin = require("tailwindcss/plugin");

module.exports = {
  // ...
  plugins: [
    plugin(function ({ addVariant }) {
      addVariant("third", "&:nth-child(3)");
    }),
  ],
};
```

----------------------------------------

TITLE: Using Custom Color Utility Class in HTML
DESCRIPTION: Demonstrates how to use the generated utility class from a custom color theme variable.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/theme.mdx#2025-04-22_snippet_1

LANGUAGE: html
CODE:
```
<div class="bg-mint-500">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Vue Component Using Script Setup Syntax
DESCRIPTION: Example of a Vue component using the new <script setup> syntax with Headless UI Listbox component integration. Demonstrates simplified component registration and reduced boilerplate compared to traditional Vue component syntax.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/2022-05-23-headless-ui-v1-6-tailwind-ui-team-management/index.mdx#2025-04-22_snippet_5

LANGUAGE: html
CODE:
```
<template>
  <Listbox as="div" v-model="selected">
    <!-- ... -->
  </Listbox>
</template>

<script setup>
  import { ref } from "vue";
  import { Listbox, ListboxButton, ListboxLabel, ListboxOption, ListboxOptions } from "@headlessui/vue";
  import { CheckIcon, SelectorIcon } from "@heroicons/vue/solid";

  const people = [
    { id: 1, name: "Wade Cooper" },
    // ...
  ];

  const selected = ref(people[3]);
</script>
```

----------------------------------------

TITLE: HTML Line-Clamp Implementation with Tailwind CSS
DESCRIPTION: This HTML snippet demonstrates how to use the `line-clamp-3` class in Tailwind CSS to truncate text to a maximum of three lines. It includes styling for the article, time, heading, and paragraph elements.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-3/index.mdx#_snippet_28

LANGUAGE: HTML
CODE:
```
<article>
  <div>
    <time datetime="2020-03-16" class="block text-sm/6 text-gray-600">Mar 10, 2020</time>
    <h2 class="mt-2 text-lg font-semibold text-gray-900">Boost your conversion rate</h2>
    >
    <p class="mt-4 line-clamp-3 text-sm/6 text-gray-600">
      Nulla dolor velit adipisicing duis excepteur esse in duis nostrud occaecat mollit incididunt deserunt sunt. Ut ut
      sunt laborum ex occaecat eu tempor labore enim adipisicing minim ad. Est in quis eu dolore occaecat excepteur
      fugiat dolore nisi aliqua fugiat enim ut cillum. Labore enim duis nostrud eu. Est ut eiusmod consequat irure quis
      deserunt ex. Enim laboris dolor magna pariatur. Dolor et ad sint voluptate sunt elit mollit officia ad enim sit
      consectetur enim.
    </p>
  </div>
  <div class="mt-4 flex gap-x-2.5 text-sm leading-6 font-semibold text-gray-900">
    <img src="..." class="h-6 w-6 flex-none rounded-full bg-gray-50" />
    Lindsay Walton
  </div>
</article>
```

----------------------------------------

TITLE: HTML Example with LTR/RTL and Logical Properties
DESCRIPTION: This HTML snippet demonstrates the use of logical properties in Tailwind CSS for handling layout in both LTR and RTL directions. It shows how `ms-3` (margin-inline-start) can replace the need for separate `ltr:ml-3` and `rtl:mr-3` classes.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-3/index.mdx#_snippet_6

LANGUAGE: HTML
CODE:
```
<div class="group flex items-center">
  <img class="h-12 w-12 shrink-0 rounded-full" src="..." alt="" />
  <div class="ltr:ml-3 rtl:mr-3"><!-- [!code --] -->
    <div class="ms-3"><!-- [!code ++] -->
      <p
        class="text-sm font-medium text-slate-700 group-hover:text-slate-900"
        dark-class="text-sm font-medium text-slate-300 group-hover:text-white"
      >
        ...
      </p>
      <p
        class="text-sm font-medium text-slate-500 group-hover:text-slate-700"
        dark-class="text-sm font-medium text-slate-500 group-hover:text-slate-300"
      >
        ...
      </p>
    </div>
  </div>
</div>
```

----------------------------------------

TITLE: Defining Default Color Palette in OKLCH for Tailwind CSS
DESCRIPTION: This CSS snippet defines the default color palette for Tailwind CSS using OKLCH color values. It includes a wide range of colors such as red, orange, amber, yellow, lime, green, emerald, teal, cyan, sky, blue, indigo, violet, purple, fuchsia, pink, rose, slate, and gray. Each color has multiple shades ranging from 50 to 950.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/colors.mdx#2025-04-22_snippet_13

LANGUAGE: CSS
CODE:
```
@theme {
  --color-red-50: oklch(0.971 0.013 17.38);
  --color-red-100: oklch(0.936 0.032 17.717);
  --color-red-200: oklch(0.885 0.062 18.334);
  --color-red-300: oklch(0.808 0.114 19.571);
  --color-red-400: oklch(0.704 0.191 22.216);
  --color-red-500: oklch(0.637 0.237 25.331);
  --color-red-600: oklch(0.577 0.245 27.325);
  --color-red-700: oklch(0.505 0.213 27.518);
  --color-red-800: oklch(0.444 0.177 26.899);
  --color-red-900: oklch(0.396 0.141 25.723);
  --color-red-950: oklch(0.258 0.092 26.042);

  --color-orange-50: oklch(0.98 0.016 73.684);
  --color-orange-100: oklch(0.954 0.038 75.164);
  --color-orange-200: oklch(0.901 0.076 70.697);
  --color-orange-300: oklch(0.837 0.128 66.29);
  --color-orange-400: oklch(0.75 0.183 55.934);
  --color-orange-500: oklch(0.705 0.213 47.604);
  --color-orange-600: oklch(0.646 0.222 41.116);
  --color-orange-700: oklch(0.553 0.195 38.402);
  --color-orange-800: oklch(0.47 0.157 37.304);
  --color-orange-900: oklch(0.408 0.123 38.172);
  --color-orange-950: oklch(0.266 0.079 36.259);

  --color-amber-50: oklch(0.987 0.022 95.277);
  --color-amber-100: oklch(0.962 0.059 95.617);
  --color-amber-200: oklch(0.924 0.12 95.746);
  --color-amber-300: oklch(0.879 0.169 91.605);
  --color-amber-400: oklch(0.828 0.189 84.429);
  --color-amber-500: oklch(0.769 0.188 70.08);
  --color-amber-600: oklch(0.666 0.179 58.318);
  --color-amber-700: oklch(0.555 0.163 48.998);
  --color-amber-800: oklch(0.473 0.137 46.201);
  --color-amber-900: oklch(0.414 0.112 45.904);
  --color-amber-950: oklch(0.279 0.077 45.635);

  --color-yellow-50: oklch(0.987 0.026 102.212);
  --color-yellow-100: oklch(0.973 0.071 103.193);
  --color-yellow-200: oklch(0.945 0.129 101.54);
  --color-yellow-300: oklch(0.905 0.182 98.111);
  --color-yellow-400: oklch(0.852 0.199 91.936);
  --color-yellow-500: oklch(0.795 0.184 86.047);
  --color-yellow-600: oklch(0.681 0.162 75.834);
  --color-yellow-700: oklch(0.554 0.135 66.442);
  --color-yellow-800: oklch(0.476 0.114 61.907);
  --color-yellow-900: oklch(0.421 0.095 57.708);
  --color-yellow-950: oklch(0.286 0.066 53.813);

  --color-lime-50: oklch(0.986 0.031 120.757);
  --color-lime-100: oklch(0.967 0.067 122.328);
  --color-lime-200: oklch(0.938 0.127 124.321);
  --color-lime-300: oklch(0.897 0.196 126.665);
  --color-lime-400: oklch(0.841 0.238 128.85);
  --color-lime-500: oklch(0.768 0.233 130.85);
  --color-lime-600: oklch(0.648 0.2 131.684);
  --color-lime-700: oklch(0.532 0.157 131.589);
  --color-lime-800: oklch(0.453 0.124 130.933);
  --color-lime-900: oklch(0.405 0.101 131.063);
  --color-lime-950: oklch(0.274 0.072 132.109);

  --color-green-50: oklch(0.982 0.018 155.826);
  --color-green-100: oklch(0.962 0.044 156.743);
  --color-green-200: oklch(0.925 0.084 155.995);
  --color-green-300: oklch(0.871 0.15 154.449);
  --color-green-400: oklch(0.792 0.209 151.711);
  --color-green-500: oklch(0.723 0.219 149.579);
  --color-green-600: oklch(0.627 0.194 149.214);
  --color-green-700: oklch(0.527 0.154 150.069);
  --color-green-800: oklch(0.448 0.119 151.328);
  --color-green-900: oklch(0.393 0.095 152.535);
  --color-green-950: oklch(0.266 0.065 152.934);

  --color-emerald-50: oklch(0.979 0.021 166.113);
  --color-emerald-100: oklch(0.95 0.052 163.051);
  --color-emerald-200: oklch(0.905 0.093 164.15);
  --color-emerald-300: oklch(0.845 0.143 164.978);
  --color-emerald-400: oklch(0.765 0.177 163.223);
  --color-emerald-500: oklch(0.696 0.17 162.48);
  --color-emerald-600: oklch(0.596 0.145 163.225);
  --color-emerald-700: oklch(0.508 0.118 165.612);
  --color-emerald-800: oklch(0.432 0.095 166.913);
  --color-emerald-900: oklch(0.378 0.077 168.94);
  --color-emerald-950: oklch(0.262 0.051 172.552);

  --color-teal-50: oklch(0.984 0.014 180.72);
  --color-teal-100: oklch(0.953 0.051 180.801);
  --color-teal-200: oklch(0.91 0.096 180.426);
  --color-teal-300: oklch(0.855 0.138 181.071);
  --color-teal-400: oklch(0.777 0.152 181.912);
  --color-teal-500: oklch(0.704 0.14 182.503);
  --color-teal-600: oklch(0.6 0.118 184.704);
  --color-teal-700: oklch(0.511 0.096 186.391);
  --color-teal-800: oklch(0.437 0.078 188.216);
  --color-teal-900: oklch(0.386 0.063 188.416);
  --color-teal-950: oklch(0.277 0.046 192.524);

  --color-cyan-50: oklch(0.984 0.019 200.873);
  --color-cyan-100: oklch(0.956 0.045 203.388);
  --color-cyan-200: oklch(0.917 0.08 205.041);
  --color-cyan-300: oklch(0.865 0.127 207.078);
  --color-cyan-400: oklch(0.789 0.154 211.53);
  --color-cyan-500: oklch(0.715 0.143 215.221);
  --color-cyan-600: oklch(0.609 0.126 221.723);
  --color-cyan-700: oklch(0.52 0.105 223.128);
  --color-cyan-800: oklch(0.45 0.085 224.283);
  --color-cyan-900: oklch(0.398 0.07 227.392);
  --color-cyan-950: oklch(0.302 0.056 229.695);

  --color-sky-50: oklch(0.977 0.013 236.62);
  --color-sky-100: oklch(0.951 0.026 236.824);
  --color-sky-200: oklch(0.901 0.058 230.902);
  --color-sky-300: oklch(0.828 0.111 230.318);
  --color-sky-400: oklch(0.746 0.16 232.661);
  --color-sky-500: oklch(0.685 0.169 237.323);
  --color-sky-600: oklch(0.588 0.158 241.966);
  --color-sky-700: oklch(0.5 0.134 242.749);
  --color-sky-800: oklch(0.443 0.11 240.79);
  --color-sky-900: oklch(0.391 0.09 240.876);
  --color-sky-950: oklch(0.293 0.066 243.157);

  --color-blue-50: oklch(0.97 0.014 254.604);
  --color-blue-100: oklch(0.932 0.032 255.585);
  --color-blue-200: oklch(0.882 0.059 254.128);
  --color-blue-300: oklch(0.809 0.105 251.813);
  --color-blue-400: oklch(0.707 0.165 254.624);
  --color-blue-500: oklch(0.623 0.214 259.815);
  --color-blue-600: oklch(0.546 0.245 262.881);
  --color-blue-700: oklch(0.488 0.243 264.376);
  --color-blue-800: oklch(0.424 0.199 265.638);
  --color-blue-900: oklch(0.379 0.146 265.522);
  --color-blue-950: oklch(0.282 0.091 267.935);

  --color-indigo-50: oklch(0.962 0.018 272.314);
  --color-indigo-100: oklch(0.93 0.034 272.788);
  --color-indigo-200: oklch(0.87 0.065 274.039);
  --color-indigo-300: oklch(0.785 0.115 274.713);
  --color-indigo-400: oklch(0.673 0.182 276.935);
  --color-indigo-500: oklch(0.585 0.233 277.117);
  --color-indigo-600: oklch(0.511 0.262 276.966);
  --color-indigo-700: oklch(0.457 0.24 277.023);
  --color-indigo-800: oklch(0.398 0.195 277.366);
  --color-indigo-900: oklch(0.359 0.144 278.697);
  --color-indigo-950: oklch(0.257 0.09 281.288);

  --color-violet-50: oklch(0.969 0.016 293.756);
  --color-violet-100: oklch(0.943 0.029 294.588);
  --color-violet-200: oklch(0.894 0.057 293.283);
  --color-violet-300: oklch(0.811 0.111 293.571);
  --color-violet-400: oklch(0.702 0.183 293.541);
  --color-violet-500: oklch(0.606 0.25 292.717);
  --color-violet-600: oklch(0.541 0.281 293.009);
  --color-violet-700: oklch(0.491 0.27 292.581);
  --color-violet-800: oklch(0.432 0.232 292.759);
  --color-violet-900: oklch(0.38 0.189 293.745);
  --color-violet-950: oklch(0.283 0.141 291.089);

  --color-purple-50: oklch(0.977 0.014 308.299);
  --color-purple-100: oklch(0.946 0.033 307.174);
  --color-purple-200: oklch(0.902 0.063 306.703);
  --color-purple-300: oklch(0.827 0.119 306.383);
  --color-purple-400: oklch(0.714 0.203 305.504);
  --color-purple-500: oklch(0.627 0.265 303.9);
  --color-purple-600: oklch(0.558 0.288 302.321);
  --color-purple-700: oklch(0.496 0.265 301.924);
  --color-purple-800: oklch(0.438 0.218 303.724);
  --color-purple-900: oklch(0.381 0.176 304.987);
  --color-purple-950: oklch(0.291 0.149 302.717);

  --color-fuchsia-50: oklch(0.977 0.017 320.058);
  --color-fuchsia-100: oklch(0.952 0.037 318.852);
  --color-fuchsia-200: oklch(0.903 0.076 319.62);
  --color-fuchsia-300: oklch(0.833 0.145 321.434);
  --color-fuchsia-400: oklch(0.74 0.238 322.16);
  --color-fuchsia-500: oklch(0.667 0.295 322.15);
  --color-fuchsia-600: oklch(0.591 0.293 322.896);
  --color-fuchsia-700: oklch(0.518 0.253 323.949);
  --color-fuchsia-800: oklch(0.452 0.211 324.591);
  --color-fuchsia-900: oklch(0.401 0.17 325.612);
  --color-fuchsia-950: oklch(0.293 0.136 325.661);

  --color-pink-50: oklch(0.971 0.014 343.198);
  --color-pink-100: oklch(0.948 0.028 342.258);
  --color-pink-200: oklch(0.899 0.061 343.231);
  --color-pink-300: oklch(0.823 0.12 346.018);
  --color-pink-400: oklch(0.718 0.202 349.761);
  --color-pink-500: oklch(0.656 0.241 354.308);
  --color-pink-600: oklch(0.592 0.249 0.584);
  --color-pink-700: oklch(0.525 0.223 3.958);
  --color-pink-800: oklch(0.459 0.187 3.815);
  --color-pink-900: oklch(0.408 0.153 2.432);
  --color-pink-950: oklch(0.284 0.109 3.907);

  --color-rose-50: oklch(0.969 0.015 12.422);
  --color-rose-100: oklch(0.941 0.03 12.58);
  --color-rose-200: oklch(0.892 0.058 10.001);
  --color-rose-300: oklch(0.81 0.117 11.638);
  --color-rose-400: oklch(0.712 0.194 13.428);
  --color-rose-500: oklch(0.645 0.246 16.439);
  --color-rose-600: oklch(0.586 0.253 17.585);
  --color-rose-700: oklch(0.514 0.222 16.935);
  --color-rose-800: oklch(0.455 0.188 13.697);
  --color-rose-900: oklch(0.41 0.159 10.272);
  --color-rose-950: oklch(0.271 0.105 12.094);

  --color-slate-50: oklch(0.984 0.003 247.858);
  --color-slate-100: oklch(0.968 0.007 247.896);
  --color-slate-200: oklch(0.929 0.013 255.508);
  --color-slate-300: oklch(0.869 0.022 252.894);
  --color-slate-400: oklch(0.704 0.04 256.788);
  --color-slate-500: oklch(0.554 0.046 257.417);
  --color-slate-600: oklch(0.446 0.043 257.281);
  --color-slate-700: oklch(0.372 0.044 257.287);
  --color-slate-800: oklch(0.279 0.041 260.031);
  --color-slate-900: oklch(0.208 0.042 265.755);
  --color-slate-950: oklch(0.129 0.042 264.695);

  --color-gray-50: oklch(0.985 0.002 247.839);
  --color-gray-100: oklch(0.967 0.003 264.542);
  --color-gray-200: oklch(0.928 0.006 264.531);
  --color-gray-300: oklch(0.872 0.01 258.338);
  --color-gray-400: oklch(0.707 0.022 261.325);
}
```

----------------------------------------

TITLE: Relative Positioning Example - HTML
DESCRIPTION: Example showing relative positioning in TailwindCSS where an absolute child element is positioned relative to its parent.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/position.mdx#2025-04-22_snippet_2

LANGUAGE: html
CODE:
```
<div class="relative ...">
  <p>Relative parent</p>
  <div class="absolute bottom-0 left-0 ...">
    <p>Absolute child</p>
  </div>
</div>
```

----------------------------------------

TITLE: Using Arbitrary Variants in HTML
DESCRIPTION: Demonstrates the new arbitrary variants feature that allows creating custom variants directly in HTML markup. Shows targeting the third child element.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-1/index.mdx#2025-04-22_snippet_16

LANGUAGE: html
CODE:
```
<div class="[&:nth-child(3)]:py-0">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Generating a TypeScript Config File
DESCRIPTION: This shell command generates a TypeScript config file for Tailwind CSS. The `--ts` flag tells the `tailwindcss init` command to create the configuration file using TypeScript syntax.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-3/index.mdx#_snippet_4

LANGUAGE: Shell
CODE:
```
npx tailwindcss init --ts
```

----------------------------------------

TITLE: Responsive Object-Fit Design with Tailwind CSS
DESCRIPTION: This example demonstrates how to apply different object-fit properties responsively using Tailwind CSS. The image will use 'object-contain' by default and switch to 'object-cover' on medium-sized screens and above.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/object-fit.mdx#2025-04-22_snippet_5

LANGUAGE: HTML
CODE:
```
<img class="object-contain md:object-cover" src="/img/mountains.jpg" />
```

----------------------------------------

TITLE: Items Stretch Example
DESCRIPTION: HTML example showing how to use the items-stretch utility to make flex items fill the container's cross axis.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/align-items.mdx#2025-04-22_snippet_1

LANGUAGE: html
CODE:
```
<div class="flex items-stretch ...">
  <div class="py-4">01</div>
  <div class="py-12">02</div>
  <div class="py-8">03</div>
</div>
```

----------------------------------------

TITLE: Implementing Safe Content Alignment in HTML
DESCRIPTION: Demonstrates the usage of safe alignment utilities that automatically adjust alignment to prevent bi-directional overflow in containers
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4-1/index.mdx#2025-04-22_snippet_15

LANGUAGE: html
CODE:
```
<ul class="flex justify-center gap-2 ...">
  <li>Sales</li>
  <li>Marketing</li>
  <li>SEO</li>
  <!-- ... -->
</ul>
```

LANGUAGE: html
CODE:
```
<ul class="flex justify-center-safe gap-2 ...">
  <li>Sales</li>
  <li>Marketing</li>
  <li>SEO</li>
  <!-- ... -->
</ul>
```

----------------------------------------

TITLE: Demonstrating place-items-end in Tailwind CSS
DESCRIPTION: Illustrates the use of the place-items-end utility class to position grid items at the end of their grid areas on both axes.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/place-items.mdx#2025-04-22_snippet_3

LANGUAGE: html
CODE:
```
<div class="grid h-56 grid-cols-3 place-items-end gap-4 ...">
  <div>01</div>
  <div>02</div>
  <div>03</div>
  <div>04</div>
  <div>05</div>
  <div>06</div>
</div>
```

----------------------------------------

TITLE: Stretch Content Grid Layout
DESCRIPTION: Demonstrates using place-content-stretch to stretch grid items across their grid areas.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/place-content.mdx#2025-04-22_snippet_6

LANGUAGE: html
CODE:
```
<div class="grid h-48 grid-cols-2 place-content-stretch gap-4 ...">
  <div>01</div>
  <div>02</div>
  <div>03</div>
  <div>04</div>
</div>
```

----------------------------------------

TITLE: Start Alignment with justify-start
DESCRIPTION: Example showing how to align flex items to the start of the container using justify-start utility class.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/justify-content.mdx#2025-04-22_snippet_0

LANGUAGE: html
CODE:
```
<div class="flex justify-start ...">
  <div>01</div>
  <div>02</div>
  <div>03</div>
</div>
```

----------------------------------------

TITLE: Flex Wrap Normal Example
DESCRIPTION: HTML example showing the flex-wrap utility class that allows flex items to wrap normally.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/flex-wrap.mdx#2025-04-22_snippet_2

LANGUAGE: html
CODE:
```
<div class="flex flex-wrap">
  <div>01</div>
  <div>02</div>
  <div>03</div>
</div>
```

----------------------------------------

TITLE: Applying Dark Mode Responsive Styles HTML
DESCRIPTION: Shows how to combine the `dark:` variant with responsive prefixes like `lg:` (`lg:dark:bg-black`) to apply dark mode styles that are also scoped to a specific breakpoint. Requires `darkMode` enabled and the responsive variant.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v2/index.mdx#_snippet_5

LANGUAGE: HTML
CODE:
```
<div class="lg:bg-white lg:dark:bg-black ...">
```

----------------------------------------

TITLE: Creating Calendar Grid Cell Borders with Tailwind CSS (HTML)
DESCRIPTION: An empty HTML `div` element styled with Tailwind CSS utility classes to function as a visual grid cell divider. It uses `col-start-*` and `row-start-*` for grid placement, `border-r` and `border-b` for right and bottom borders, and specific border colors for light (`border-gray-100`) and dark (`dark:border-gray-200/5`) modes. This pattern is repeated numerous times to form the grid lines.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/overflow.mdx#2025-04-22_snippet_10

LANGUAGE: html
CODE:
```
<div className="col-start-2 row-start-9 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-3 row-start-9 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-4 row-start-9 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-5 row-start-9 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-6 row-start-9 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-7 row-start-9 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-8 row-start-9 border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-2 row-start-10 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-3 row-start-10 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-4 row-start-10 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-5 row-start-10 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-6 row-start-10 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-7 row-start-10 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-8 row-start-10 border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-2 row-start-11 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-3 row-start-11 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-4 row-start-11 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-5 row-start-11 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-6 row-start-11 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-7 row-start-11 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-8 row-start-11 border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-2 row-start-12 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-3 row-start-12 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-4 row-start-12 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-5 row-start-12 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-6 row-start-12 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-7 row-start-12 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-8 row-start-12 border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-2 row-start-13 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-3 row-start-13 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-4 row-start-13 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-5 row-start-13 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-6 row-start-13 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-7 row-start-13 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-8 row-start-13 border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-2 row-start-14 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-3 row-start-14 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-4 row-start-14 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-5 row-start-14 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-6 row-start-14 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-7 row-start-14 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-8 row-start-14 border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-2 row-start-15 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-3 row-start-15 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-4 row-start-15 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-5 row-start-15 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-6 row-start-15 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-7 row-start-15 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-8 row-start-15 border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-2 row-start-16 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-3 row-start-16 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-4 row-start-16 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-5 row-start-16 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-6 row-start-16 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-7 row-start-16 border-r border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-8 row-start-16 border-b border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-2 row-start-17 border-r border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-3 row-start-17 border-r border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-4 row-start-17 border-r border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-5 row-start-17 border-r border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-6 row-start-17 border-r border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-7 row-start-17 border-r border-gray-100 dark:border-gray-200/5"></div>
```

LANGUAGE: html
CODE:
```
<div className="col-start-8 row-start-17"></div>
```

----------------------------------------

TITLE: Styling Last Child Element with Tailwind CSS in Svelte
DESCRIPTION: Illustrates the use of the 'last' variant to style an element if it's the last child in a Svelte component.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#2025-04-22_snippet_91

LANGUAGE: Svelte
CODE:
```
<ul>
  {#each people as person}
    <li class="py-4 last:pb-0 ...">
      <!-- ... -->
    </li>
  {/each}
</ul>
```

----------------------------------------

TITLE: Applying Dark Mode Styles HTML
DESCRIPTION: Provides an example of using the `dark:` variant prefix on utility classes (`dark:bg-black`, `dark:text-white`, `dark:text-gray-300`) to apply styles only when dark mode is enabled. Requires `darkMode: "media"` or `"class"` configured in `tailwind.config.js`.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v2/index.mdx#_snippet_3

LANGUAGE: HTML
CODE:
```
<div class="bg-white dark:bg-black">
  <h1 class="text-gray-900 dark:text-white">Dark mode</h1>
  <p class="text-gray-500 dark:text-gray-300">The feature you've all been waiting for.</p>
</div>
```

----------------------------------------

TITLE: Adding Forms Plugin to Config JS
DESCRIPTION: Shows how to include the official `@tailwindcss/forms` plugin in your `tailwind.config.js` file. Adding the plugin is a prerequisite for utilizing the utility-friendly form styling normalization provided by Tailwind v2.0.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v2/index.mdx#_snippet_12

LANGUAGE: js
CODE:
```
module.exports = {
  // ...
  plugins: [require("@tailwindcss/forms")],
};
```

----------------------------------------

TITLE: Single-Side Margin Examples in HTML with Tailwind CSS
DESCRIPTION: Shows how to add margin to individual sides of an element using mt-6, mr-4, mb-8, and ml-2 utility classes.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/margin.mdx#2025-04-22_snippet_2

LANGUAGE: HTML
CODE:
```
<div class="mt-6 ...">mt-6</div>
<div class="mr-4 ...">mr-4</div>
<div class="mb-8 ...">mb-8</div>
<div class="ml-2 ...">ml-2</div>
```

----------------------------------------

TITLE: Using * Variant for Child Element Styling in HTML
DESCRIPTION: Demonstrates how to use the new * variant to style direct children elements with utility classes. This feature allows targeting all direct children from the parent element.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-4/index.mdx#2025-04-22_snippet_6

LANGUAGE: html
CODE:
```
<div>
  <h2>Categories:<h2>
  <ul class="*:rounded-full *:border *:border-sky-100 *:bg-sky-50 *:px-2 *:py-0.5 dark:text-sky-300 dark:*:border-sky-500/15 dark:*:bg-sky-500/10 ...">
    <li>Sales</li>
    <li>Marketing</li>
    <li>SEO</li>
    <!-- ... -->
  </ul>
</div>
```

----------------------------------------

TITLE: Utility Classes HTML Example
DESCRIPTION: Shows recommended way to use Tailwind's utility classes for styling buttons.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/compatibility.mdx#2025-04-22_snippet_4

LANGUAGE: html
CODE:
```
<button class="bg-indigo-500 hover:bg-indigo-600 ...">
  <!-- ... -->
</button>
```

----------------------------------------

TITLE: Matching Dynamic Viewport Height - Tailwind CSS HTML
DESCRIPTION: Explains how the `h-dvh` utility sets an element's height to 100% of the dynamic viewport height (`100dvh`), which adjusts based on the visibility of browser UI elements like address bars.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/height.mdx#_snippet_4

LANGUAGE: html
CODE:
```
<!-- [!code classes:h-dvh] -->
<div class="h-dvh">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Basic Border Radius Example in TailwindCSS
DESCRIPTION: Demonstrates the usage of basic border radius utilities from smallest (sm) to largest (xl) sizes. Each class applies consistent rounding to all corners of an element.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/border-radius.mdx#2025-04-22_snippet_0

LANGUAGE: html
CODE:
```
<div class="rounded-sm ..."></div>
<div class="rounded-md ..."></div>
<div class="rounded-lg ..."></div>
<div class="rounded-xl ..."></div>
```

----------------------------------------

TITLE: Positioning Dropdown with Anchor Prop (React/JSX)
DESCRIPTION: Shows how to use the new `anchor` prop on a Headless UI `MenuItems` component to control its position relative to the anchor element, leveraging integrated Floating UI capabilities. It also demonstrates using CSS variables like `--anchor-gap` and `--anchor-padding` for fine-tuning.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/headless-ui-v2/index.mdx#_snippet_1

LANGUAGE: jsx
CODE:
```
import { Menu, MenuButton, MenuItem, MenuItems } from "@headlessui/react";

function Example() {
  return (
    <Menu>
      <MenuButton>Options</MenuButton>
      <MenuItems
        // [!code highlight:3]
        anchor="bottom start"
        className="[--anchor-gap:8px] [--anchor-padding:8px]"
      >
        <MenuItem>
          <button>Edit</button>
        </MenuItem>
        <MenuItem>
          <button>Duplicate</button>
        </MenuItem>
        <hr />
        <MenuItem>
          <button>Archive</button>
        </MenuItem>
        <MenuItem>
          <button>Delete</button>
        </MenuItem>
      </MenuItems>
    </Menu>
  );
}
```

----------------------------------------

TITLE: Basic Margin Example in HTML with Tailwind CSS
DESCRIPTION: Demonstrates the use of the m-8 utility class to add margin on all sides of an element.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/margin.mdx#2025-04-22_snippet_1

LANGUAGE: HTML
CODE:
```
<div class="m-8 ...">m-8</div>
```

----------------------------------------

TITLE: Focus-Visible Variant Usage
DESCRIPTION: Example of using the new focus-visible variant for keyboard-only focus styles.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-1-5/index.mdx#2025-04-22_snippet_6

LANGUAGE: html
CODE:
```
<button class="focus-visible:shadow-outline focus-visible:outline-none ...">Click me</button>
```

----------------------------------------

TITLE: Using the line-clamp Utility in HTML
DESCRIPTION: Example of applying the line-clamp-3 utility class to a paragraph to truncate the text after 3 lines. The utility automatically adds an ellipsis to indicate truncation.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/multi-line-truncation-with-tailwindcss-line-clamp/index.mdx#2025-04-22_snippet_2

LANGUAGE: html
CODE:
```
<p class="line-clamp-3">
  Here's a block of text from a blog post that isn't conveniently three lines long like you designed for originally.
  It's probably like 6 lines on mobile or even on desktop depending on how you have things laid out. Truly a big pain in
  the derriere, and not the sort of thing you expected to be wasting your time trying to deal with at 4:45pm on a Friday
  am I right? You've got tickets to SmackDown and you heard there's gonna be a dark match with that local guy from two
  towns over that your cousin went to high school with before the show starts, and you're gonna miss it if you're not
  there early.
</p>
```

----------------------------------------

TITLE: Applying Viewport Width Utility in HTML
DESCRIPTION: HTML code snippet demonstrating the `w-screen` utility class. This class sets the element's width to 100% of the viewport width (`100vw`). The surrounding text mentions alternatives like `w-lvw`, `w-svw`, and `w-dvw` for large, small, and dynamic viewport widths respectively.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/width.mdx#2025-04-22_snippet_9

LANGUAGE: html
CODE:
```
<!-- [!code classes:w-screen] -->
<div class="w-screen">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Space Between Items with justify-between
DESCRIPTION: Example showing how to create equal spacing between flex items using justify-between utility.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/justify-content.mdx#2025-04-22_snippet_3

LANGUAGE: html
CODE:
```
<div class="flex justify-between ...">
  <div>01</div>
  <div>02</div>
  <div>03</div>
</div>
```

----------------------------------------

TITLE: Applying Percentage Height Utilities - Tailwind CSS HTML
DESCRIPTION: Illustrates the use of `h-full` and `h-<fraction>` utilities (like `h-9/10`, `h-3/4`, `h-1/2`, `h-1/3`) to set element heights relative to their parent container as a percentage.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/height.mdx#_snippet_2

LANGUAGE: html
CODE:
```
<!-- [!code classes:h-9/10,h-3/4,h-1/2,h-1/3,h-full] -->
<div class="h-full ...">h-full</div>
<div class="h-9/10 ...">h-9/10</div>
<div class="h-3/4 ...">h-3/4</div>
<div class="h-1/2 ...">h-1/2</div>
<div class="h-1/3 ...">h-1/3</div>
```

----------------------------------------

TITLE: Basic Font Weight Example - HTML
DESCRIPTION: Demonstrates the basic usage of TailwindCSS font weight utility classes with different weight values.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/font-weight.mdx#2025-04-22_snippet_1

LANGUAGE: html
CODE:
```
<p class="font-light ...">The quick brown fox ...</p>
<p class="font-normal ...">The quick brown fox ...</p>
<p class="font-medium ...">The quick brown fox ...</p>
<p class="font-semibold ...">The quick brown fox ...</p>
<p class="font-bold ...">The quick brown fox ...</p>
```

----------------------------------------

TITLE: Single Breakpoint Example
DESCRIPTION: Demonstrates how to target a single breakpoint by combining responsive and max-* variants.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/responsive-design.mdx#2025-04-22_snippet_6

LANGUAGE: html
CODE:
```
<div class="md:max-lg:flex">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Implementing Subgrid Layout System
DESCRIPTION: Shows how to use the new subgrid feature to inherit grid properties from parent elements and maintain alignment across nested components.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-4/index.mdx#2025-04-22_snippet_9

LANGUAGE: html
CODE:
```
<div class="grid grid-cols-4 gap-4">
  <!-- ... -->
  <div class="col-span-3 grid grid-cols-subgrid gap-4">
    <div class="col-start-2">06</div>
  </div>
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Applying RTL and LTR Variants for Multi-Directional Layouts in HTML
DESCRIPTION: Shows how to use the rtl and ltr variants in Tailwind CSS to conditionally add styles for right-to-left and left-to-right layouts.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#2025-04-22_snippet_62

LANGUAGE: html
CODE:
```
<div class="group flex items-center">
  <img class="h-12 w-12 shrink-0 rounded-full" src="..." alt="" />
  <div class="ltr:ml-3 rtl:mr-3">
    <p class="text-gray-700 group-hover:text-gray-900 ...">...</p>
    <p class="text-gray-500 group-hover:text-gray-700 ...">...</p>
  </div>
</div>
```

----------------------------------------

TITLE: Customizing Box Shadow in Tailwind CSS Theme (JSX)
DESCRIPTION: Illustrates how to customize the box shadow utility in the Tailwind CSS theme. This example adds a new '3xl' shadow with a specific value to the theme configuration.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/box-shadow.mdx#2025-04-22_snippet_13

LANGUAGE: jsx
CODE:
```
<CustomizingYourTheme
  utility="shadow"
  themeKey="shadow"
  name="box shadow"
  customName="3xl"
  customValue="0 35px 35px rgba(0, 0, 0, 0.25)"
/>
```

----------------------------------------

TITLE: Stretching Image to Fit Container using Tailwind CSS
DESCRIPTION: This snippet illustrates the use of the 'object-fill' utility class to stretch an image's content to fit its container. The image will fill the entire container, potentially changing its aspect ratio.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/object-fit.mdx#2025-04-22_snippet_2

LANGUAGE: HTML
CODE:
```
<img class="h-48 w-96 object-fill ..." src="/img/mountains.jpg" />
```

----------------------------------------

TITLE: Using Tailwind CSS Color Scale Classes in HTML
DESCRIPTION: Demonstrates the use of Tailwind CSS background color classes across the 11 steps of the sky color palette, from lightest (50) to darkest (950).
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/colors.mdx#2025-04-22_snippet_0

LANGUAGE: html
CODE:
```
<!-- [!code classes:bg-sky-50,bg-sky-100,bg-sky-200,bg-sky-300,bg-sky-400,bg-sky-500,bg-sky-600,bg-sky-700,bg-sky-800,bg-sky-900,bg-sky-950] -->
<div>
  <div class="bg-sky-50"></div>
  <div class="bg-sky-100"></div>
  <div class="bg-sky-200"></div>
  <div class="bg-sky-300"></div>
  <div class="bg-sky-400"></div>
  <div class="bg-sky-500"></div>
  <div class="bg-sky-600"></div>
  <div class="bg-sky-700"></div>
  <div class="bg-sky-800"></div>
  <div class="bg-sky-900"></div>
  <div class="bg-sky-950"></div>
</div>
```

----------------------------------------

TITLE: End Alignment with justify-end
DESCRIPTION: Examples showing both regular and safe end alignment using justify-end and justify-end-safe utilities.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/justify-content.mdx#2025-04-22_snippet_2

LANGUAGE: html
CODE:
```
<div class="flex justify-end ...">
  <div>01</div>
  <div>02</div>
  <div>03</div>
  <div>03</div>
</div>
```

LANGUAGE: html
CODE:
```
<div class="flex justify-end-safe ...">
  <div>01</div>
  <div>02</div>
  <div>03</div>
  <div>03</div>
</div>
```

----------------------------------------

TITLE: Text Color Opacity Examples - HTML
DESCRIPTION: Shows how to control text color opacity using the color opacity modifier with different percentage values.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/color.mdx#2025-04-22_snippet_2

LANGUAGE: html
CODE:
```
<p class="text-blue-600/100 dark:text-sky-400/100">The quick brown fox...</p>
<p class="text-blue-600/75 dark:text-sky-400/75">The quick brown fox...</p>
<p class="text-blue-600/50 dark:text-sky-400/50">The quick brown fox...</p>
<p class="text-blue-600/25 dark:text-sky-400/25">The quick brown fox...</p>
```

----------------------------------------

TITLE: Creating Pill Buttons with Tailwind CSS
DESCRIPTION: This snippet shows how to create pill-shaped buttons using the 'rounded-full' utility class which applies a large border radius to create a fully rounded appearance on all corners.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/border-radius.mdx#2025-04-22_snippet_4

LANGUAGE: html
CODE:
```
<button class="rounded-full ...">Save Changes</button>
```

----------------------------------------

TITLE: Applying Active State Styling with Tailwind CSS
DESCRIPTION: Shows how to use the 'active' variant to style an element when it is being pressed.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#2025-04-22_snippet_87

LANGUAGE: HTML
CODE:
```
<button class="bg-blue-500 active:bg-blue-600 ...">Submit</button>
```

----------------------------------------

TITLE: Accessing CSS Variable Values in JavaScript for Tailwind CSS v4
DESCRIPTION: Demonstrates how to access resolved CSS variable values in JavaScript using getComputedStyle for Tailwind CSS v4 theme variables.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/upgrade-guide.mdx#2025-04-22_snippet_21

LANGUAGE: JavaScript
CODE:
```
let styles = getComputedStyle(document.documentElement);
let shadow = styles.getPropertyValue("--shadow-xl");
```

----------------------------------------

TITLE: Implementing not-* Variant in Tailwind CSS
DESCRIPTION: Shows usage of the new not-* variant for CSS :not() pseudo-class and media query negation.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4/index.mdx#2025-04-22_snippet_17

LANGUAGE: html
CODE:
```
<div class="not-hover:opacity-75">
  <!-- ... -->
</div>
```

LANGUAGE: css
CODE:
```
.not-hover\:opacity-75:not(*:hover) {
  opacity: 75%;
}

@media not (hover: hover) {
  .not-hover\:opacity-75 {
    opacity: 75%;
  }
}
```

----------------------------------------

TITLE: React Team List Component with Headless UI Integration
DESCRIPTION: A React component example showing a list of team members with dropdown menus, using Headless UI for interactions and transitions. The component demonstrates the balance between functionality and flexibility, featuring mapped data, transitions, and interactive elements.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/building-react-and-vue-support-for-tailwind-ui/index.mdx#2025-04-22_snippet_4

LANGUAGE: jsx
CODE:
```
import { Menu, Transition } from "@headlessui/react";
import { DotsVerticalIcon } from "@heroicons/react/solid";
import { Fragment } from "react";

const people = [
  {
    name: "Calvin Hawkins",
    email: "<EMAIL>",
    image:
      "https://images.unsplash.com/photo-1491528323818-fdd1faba62cc?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80",
  },
  {
    name: "Kristen Ramos",
    email: "<EMAIL>",
    image:
      "https://images.unsplash.com/photo-1550525811-e5869dd03032?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80",
  },
  {
    name: "Ted Fox",
    email: "<EMAIL>",
    image:
      "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80",
  },
];

export default function Example() {
  return (
    <ul className="divide-y divide-gray-200">
      {people.map((person) => (
        <li key={person.email} className="flex py-4">
          <img className="h-10 w-10 rounded-full" src={person.image.src} alt="" />
          <div className="ml-3">
            <p className="text-sm font-medium text-gray-900">{person.name}</p>
            <p className="text-sm text-gray-500">{person.email}</p>
          </div>
          <Menu as="div" className="relative ml-3 inline-block text-left">
            {({ open }) => (
              <>
                <div>
                  <Menu.Button className="flex items-center rounded-full bg-gray-100 text-gray-400 hover:text-gray-600 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:ring-offset-gray-100 focus:outline-none">
                    <span className="sr-only">Open options</span>
                    <DotsVerticalIcon className="h-5 w-5" aria-hidden="true" />
                  </Menu.Button>
                </div>

                <Transition
                  show={open}
                  as={Fragment}
                  enter="transition ease-out duration-100"
                  enterFrom="transform opacity-0 scale-95"
                  enterTo="transform opacity-100 scale-100"
                  leave="transition ease-in duration-75"
                  leaveFrom="transform opacity-100 scale-100"
                  leaveTo="transform opacity-0 scale-95"
                >
                  <Menu.Items
                    static
                    className="ring-opacity-5 absolute right-0 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black focus:outline-none"
                  >
                    <div className="py-1">
                      <Menu.Item>
                        {({ active }) => (
                          <a
                            href="#"
                            className={classNames(
                              active ? "bg-gray-100 text-gray-900" : "text-gray-700",
                              "block px-4 py-2 text-sm",
                            )}
                          >
                            View details
                          </a>
                        )}
                      </Menu.Item>
                      <Menu.Item>
                        {({ active }) => (
                          <a
                            href="#"
                            className={classNames(
                              active ? "bg-gray-100 text-gray-900" : "text-gray-700",
                              "block px-4 py-2 text-sm",
                            )}
                          >
                            Send message
                          </a>
                        )}
                      </Menu.Item>
                    </div>
                  </Menu.Items>
                </Transition>
              </>
            )}
          </Menu>
        </li>
      ))}
    </ul>
  );
}
```

----------------------------------------

TITLE: Enabling Dark Mode Media Strategy JavaScript
DESCRIPTION: Shows how to set the `darkMode` option to `"media"` in `tailwind.config.js`. This configures Tailwind CSS to apply dark mode styles based on the user's operating system preference (`prefers-color-scheme: dark`). Requires a `tailwind.config.js` file.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v2/index.mdx#_snippet_2

LANGUAGE: JavaScript
CODE:
```
module.exports = {
  darkMode: "media",
  // ...
};
```

----------------------------------------

TITLE: Adding Hover State Text Decoration in HTML
DESCRIPTION: Shows how to apply text decoration on hover using the hover: modifier with Tailwind's decoration utilities.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/text-decoration-line.mdx#2025-04-22_snippet_5

LANGUAGE: html
CODE:
```
<!-- [!code classes:hover:underline] -->
<p>The <a href="..." class="no-underline hover:underline ...">quick brown fox</a> jumps over the lazy dog.</p>
```

----------------------------------------

TITLE: Styling Checkbox with Forms Plugin HTML
DESCRIPTION: Demonstrates how to style a checkbox input element using standard Tailwind utility classes after applying the `@tailwindcss/forms` plugin. The plugin normalizes form controls, making them easy to style directly with utilities for appearance, focus states, and checked states.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v2/index.mdx#_snippet_11

LANGUAGE: html
CODE:
```
<input
  type="checkbox"
  class="focus:ring-opacity-50 h-4 w-4 rounded border-gray-300 text-indigo-500 focus:border-indigo-300 focus:ring-2 focus:ring-indigo-200"
/>
```

----------------------------------------

TITLE: Defining Font Size Utility Classes in HTML
DESCRIPTION: Demonstrates the usage of Tailwind CSS font size utility classes in HTML elements.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/font-size.mdx#2025-04-22_snippet_1

LANGUAGE: html
CODE:
```
<p class="text-sm ...">The quick brown fox ...</p>
<p class="text-base ...">The quick brown fox ...</p>
<p class="text-lg ...">The quick brown fox ...</p>
<p class="text-xl ...">The quick brown fox ...</p>
<p class="text-2xl ...">The quick brown fox ...</p>
```

----------------------------------------

TITLE: Basic Line Height Example
DESCRIPTION: HTML example showing how to use combined font-size and line-height utilities with different spacing values.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/line-height.mdx#2025-04-22_snippet_1

LANGUAGE: html
CODE:
```
<p class="text-base/6 ...">So I started to walk into the water...</p>
<p class="text-base/7 ...">So I started to walk into the water...</p>
<p class="text-base/8 ...">So I started to walk into the water...</p>
```

----------------------------------------

TITLE: Adjusting Opacity with Tailwind CSS Classes
DESCRIPTION: This snippet demonstrates how to adjust the opacity of background colors using Tailwind CSS classes. It shows a range of opacity values from 10% to 100% applied to a sky-500 background color.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/colors.mdx#2025-04-22_snippet_2

LANGUAGE: html
CODE:
```
<div>
  <div class="bg-sky-500/10"></div>
  <div class="bg-sky-500/20"></div>
  <div class="bg-sky-500/30"></div>
  <div class="bg-sky-500/40"></div>
  <div class="bg-sky-500/50"></div>
  <div class="bg-sky-500/60"></div>
  <div class="bg-sky-500/70"></div>
  <div class="bg-sky-500/80"></div>
  <div class="bg-sky-500/90"></div>
  <div class="bg-sky-500/100"></div>
</div>
```

----------------------------------------

TITLE: Enhanced Color Variant Mapping in React
DESCRIPTION: Extended example of mapping color props to different combinations of utility classes, demonstrating how to handle different color shades and text contrast.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/detecting-classes-in-source-files.mdx#2025-04-22_snippet_5

LANGUAGE: jsx
CODE:
```
function Button({ color, children }) {
  const colorVariants = {
    blue: "bg-blue-600 hover:bg-blue-500 text-white",
    red: "bg-red-500 hover:bg-red-400 text-white",
    yellow: "bg-yellow-300 hover:bg-yellow-400 text-black",
  };

  return <button className={`${colorVariants[color]} ...`}>{children}</button>;
}
```

----------------------------------------

TITLE: Installing Latest Tailwind CSS via npm (Shell)
DESCRIPTION: Command to install the latest version of the `tailwindcss` package as a dev dependency using npm. This is the standard way to upgrade or install Tailwind CSS for a project. Requires Node.js and npm.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-2-2/index.mdx#_snippet_0

LANGUAGE: sh
CODE:
```
npm install -D tailwindcss@latest
```

----------------------------------------

TITLE: Uppercase Text Example
DESCRIPTION: Example showing how to use the uppercase utility class to transform text to uppercase.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/text-transform.mdx#2025-04-22_snippet_1

LANGUAGE: html
CODE:
```
<!-- [!code classes:uppercase] -->
<p class="uppercase">The quick brown fox ...</p>
```

----------------------------------------

TITLE: Max-width Container Queries Example
DESCRIPTION: Shows how to use max-width container queries for styles that apply below specific container sizes.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/responsive-design.mdx#2025-04-22_snippet_13

LANGUAGE: html
CODE:
```
<div class="@container">
  <div class="flex flex-row @max-md:flex-col">
    <!-- ... -->
  </div>
</div>
```

----------------------------------------

TITLE: Loading JavaScript Config File in Tailwind CSS v4
DESCRIPTION: Demonstrates how to explicitly load a JavaScript config file in Tailwind CSS v4 using the @config directive, as automatic detection is no longer supported.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/upgrade-guide.mdx#2025-04-22_snippet_19

LANGUAGE: CSS
CODE:
```
@config "../../tailwind.config.js";
```

----------------------------------------

TITLE: Implementing Custom Variants in TailwindCSS
DESCRIPTION: Shows how to create custom variants using the @custom-variant directive with both simple and complex implementations.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/adding-custom-styles.mdx#2025-04-22_snippet_7

LANGUAGE: css
CODE:
```
@custom-variant theme-midnight {
  &:where([data-theme="midnight"] *) {
    @slot;
  }
}
```

LANGUAGE: html
CODE:
```
<html data-theme="midnight">
  <button class="theme-midnight:bg-black ..."></button>
</html>
```

----------------------------------------

TITLE: Implementing Grid Item Layout with React and Tailwind CSS
DESCRIPTION: A React component example showing a grid layout with various justify-self behaviors, including stretched items and custom styling with Tailwind CSS classes.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/justify-self.mdx#2025-04-22_snippet_6

LANGUAGE: jsx
CODE:
```
<div className="grid auto-rows-fr grid-cols-3 justify-items-stretch gap-4 text-center font-mono text-sm leading-6 font-bold text-white">
  <div className="grid grid-cols-1">
    <Stripes border className="col-start-1 row-start-1 rounded-lg" />
    <div className="col-start-1 row-start-1 size-14 rounded-lg bg-fuchsia-300 p-4 dark:bg-fuchsia-800 dark:text-fuchsia-400">
      01
    </div>
  </div>
  <div className="bg-stripes-fuchsia grid w-full justify-self-stretch rounded-lg">
    <div className="justify-self-stretch rounded-lg bg-fuchsia-500 p-4">02</div>
  </div>
  <div className="grid grid-cols-1">
    <Stripes border className="col-start-1 row-start-1 rounded-lg" />
    <div className="col-start-1 row-start-1 size-14 rounded-lg bg-fuchsia-300 p-4 dark:bg-fuchsia-800 dark:text-fuchsia-400">
      03
    </div>
  </div>
  <div className="grid grid-cols-1">
    <Stripes border className="col-start-1 row-start-1 rounded-lg" />
    <div className="col-start-1 row-start-1 size-14 rounded-lg bg-fuchsia-300 p-4 dark:bg-fuchsia-800 dark:text-fuchsia-400">
      04
    </div>
  </div>
  <div className="grid grid-cols-1">
    <Stripes border className="col-start-1 row-start-1 rounded-lg" />
    <div className="col-start-1 row-start-1 size-14 rounded-lg bg-fuchsia-300 p-4 dark:bg-fuchsia-800 dark:text-fuchsia-400">
      05
    </div>
  </div>
  <div className="grid grid-cols-1">
    <Stripes border className="col-start-1 row-start-1 rounded-lg" />
    <div className="col-start-1 row-start-1 size-14 rounded-lg bg-fuchsia-300 p-4 dark:bg-fuchsia-800 dark:text-fuchsia-400">
      06
    </div>
  </div>
</div>
```

----------------------------------------

TITLE: Side-Specific Border Radius in TailwindCSS
DESCRIPTION: Shows how to apply border radius to specific sides of an element using directional utilities like top, right, bottom, and left.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/border-radius.mdx#2025-04-22_snippet_1

LANGUAGE: html
CODE:
```
<div class="rounded-t-lg ..."></div>
<div class="rounded-r-lg ..."></div>
<div class="rounded-b-lg ..."></div>
<div class="rounded-l-lg ..."></div>
```

----------------------------------------

TITLE: Styling Keyboard Focus with Tailwind CSS
DESCRIPTION: Demonstrates the use of the 'focus-visible' variant to style an element when it has been focused using the keyboard.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#2025-04-22_snippet_86

LANGUAGE: HTML
CODE:
```
<button class="focus-visible:outline-2 ...">Submit</button>
```

----------------------------------------

TITLE: Directional Padding Examples in Tailwind CSS
DESCRIPTION: Shows how to use directional padding utilities like 'pt-6', 'pr-4', 'pb-8', and 'pl-2' to add padding to specific sides of elements.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/padding.mdx#2025-04-22_snippet_3

LANGUAGE: html
CODE:
```
<!-- [!code classes:pt-6,pr-4,pb-8,pl-2] -->
<div class="pt-6 ...">pt-6</div>
<div class="pr-4 ...">pr-4</div>
<div class="pb-8 ...">pb-8</div>
<div class="pl-2 ...">pl-2</div>
```

----------------------------------------

TITLE: Implementing Payment Method Selection with :has() Variant in Tailwind CSS
DESCRIPTION: HTML markup showing how to use the has-checked variant to style radio button labels based on their checked state. The code demonstrates styling payment method options with different visual states for selected and unselected options, including dark mode support.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#2025-04-22_snippet_9

LANGUAGE: html
CODE:
```
<label
  class="has-checked:bg-indigo-50 has-checked:text-indigo-900 has-checked:ring-indigo-200 dark:has-checked:bg-indigo-950 dark:has-checked:text-indigo-200 dark:has-checked:ring-indigo-900 ..."
>
  <svg fill="currentColor">
    <!-- ... -->
  </svg>
  Google Pay

```

----------------------------------------

TITLE: Basic Background Image Example
DESCRIPTION: Example showing how to set a background image using Tailwind's bg-[url()] utility.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/background-image.mdx#2025-04-22_snippet_1

LANGUAGE: html
CODE:
```
<div class="bg-[url(/img/mountains.jpg)] ..."></div>
```

----------------------------------------

TITLE: Resetting Width with w-auto Utility in HTML
DESCRIPTION: HTML code snippet demonstrating the use of `w-auto` for resetting an element's width, often in a responsive context. Here, the `div` is `w-full` by default but becomes `w-auto` (its natural width) on medium screens and larger (`md:w-auto`).
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/width.mdx#2025-04-22_snippet_10

LANGUAGE: html
CODE:
```
<!-- [!code classes:w-full,w-auto] -->
<div class="w-full md:w-auto">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Hiding Outlines with Tailwind CSS
DESCRIPTION: Shows how to use the 'outline-hidden' utility to hide the default browser outline on focused elements while preserving the outline in forced colors mode.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/outline-style.mdx#2025-04-22_snippet_2

LANGUAGE: HTML
CODE:
```
<input class="focus:border-indigo-600 focus:outline-hidden ..." type="text" />
```

----------------------------------------

TITLE: Applying Basic Box Shadows in HTML with Tailwind CSS
DESCRIPTION: Demonstrates the usage of Tailwind CSS shadow utilities to apply different sized outer box shadows to elements. The example shows three different shadow sizes: md, lg, and xl.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/box-shadow.mdx#2025-04-22_snippet_1

LANGUAGE: html
CODE:
```
<div class="shadow-md ..."></div>
<div class="shadow-lg ..."></div>
<div class="shadow-xl ..."></div>
```

----------------------------------------

TITLE: Containing Background Image Without Cropping
DESCRIPTION: Demonstrates using bg-contain utility to scale the background image to fit within the container without cropping or stretching.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/background-size.mdx#2025-04-22_snippet_1

LANGUAGE: html
CODE:
```
<div class="bg-[url(/img/mountains.jpg)] bg-contain bg-center"></div>
```

----------------------------------------

TITLE: Applying Border Widths to Individual Sides in Tailwind CSS
DESCRIPTION: Shows how to use utilities like border-r and border-t-4 to set border widths for specific sides of an element. Includes visual examples and corresponding HTML code.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/border-width.mdx#2025-04-22_snippet_3

LANGUAGE: HTML
CODE:
```
<div class="border-t-4 border-indigo-500 ..."></div>
<div class="border-r-4 border-indigo-500 ..."></div>
<div class="border-b-4 border-indigo-500 ..."></div>
<div class="border-l-4 border-indigo-500 ..."></div>
```

----------------------------------------

TITLE: Using Tailwind Typography Classes Example
DESCRIPTION: Demonstrates how to apply the prose classes from @tailwindcss/typography plugin to format HTML content with proper typography styling.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-typography/index.mdx#2025-04-22_snippet_1

LANGUAGE: html
CODE:
```
<article class="prose lg:prose-xl">
  <h1>Garlic bread with cheese: What the science tells us</h1>
  <p>
    For years parents have espoused the health benefits of eating garlic bread with cheese to their children, with the
    food earning such an iconic status in our culture that kids will often dress up as warm, cheesy loaf for Halloween.
  </p>
  <p>
    But a recent study shows that the celebrated appetizer may be linked to a series of rabies cases springing up around
    the country.
  </p>
  <!-- ... -->
</article>
```

----------------------------------------

TITLE: Basic Container Query Usage in TailwindCSS
DESCRIPTION: Demonstrates the basic syntax for using container queries with TailwindCSS, using the new @ prefix to differentiate from media queries.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-2/index.mdx#2025-04-22_snippet_12

LANGUAGE: html
CODE:
```
<div class="@container">
  <div class="block @lg:flex">
    <!-- ... -->
  </div>
</div>
```

----------------------------------------

TITLE: Colored Inset Shadows in Tailwind CSS
DESCRIPTION: Demonstration of applying colored inset shadows with opacity modifiers. Shows how to use color utilities like inset-shadow-indigo-500 with optional opacity values.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/box-shadow.mdx#2025-04-22_snippet_5

LANGUAGE: html
CODE:
```
<div class="inset-shadow-sm inset-shadow-indigo-500 ..."></div>
<div class="inset-shadow-sm inset-shadow-indigo-500/50 ..."></div>
```

----------------------------------------

TITLE: Updating Tailwind CSS Imports
DESCRIPTION: New way to import Tailwind CSS in v4 using standard CSS import syntax instead of @tailwind directives.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/upgrade-guide.mdx#2025-04-22_snippet_4

LANGUAGE: css
CODE:
```
@import "tailwindcss";
```

----------------------------------------

TITLE: RTL/LTR Layout Support in Tailwind CSS
DESCRIPTION: Shows implementation of right-to-left and left-to-right layout support using rtl and ltr modifiers for multi-directional layouts.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3/index.mdx#2025-04-22_snippet_10

LANGUAGE: html
CODE:
```
<div class="group flex items-center">
  <img class="h-12 w-12 shrink-0 rounded-full" src="..." alt="" />
  >
  <div class="ltr:ml-3 rtl:mr-3">
    <p class="text-sm font-medium text-slate-700 group-hover:text-slate-900">...</p>
    <p class="text-sm font-medium text-slate-500 group-hover:text-slate-700">...</p>
  </div>
</div>
```

----------------------------------------

TITLE: Configuring PostCSS for Tailwind JIT
DESCRIPTION: PostCSS configuration setup for enabling the Tailwind CSS JIT compiler. This configuration adds the @tailwindcss/jit and autoprefixer plugins to the PostCSS processing pipeline.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/just-in-time-the-next-generation-of-tailwind-css/index.mdx#2025-04-22_snippet_1

LANGUAGE: javascript
CODE:
```
// postcss.config.js
module.exports = {
  plugins: {
    "@tailwindcss/jit": {},
    autoprefixer: {},
  },
};
```

----------------------------------------

TITLE: Using CSS Variables Instead of theme() Function in Tailwind CSS v4
DESCRIPTION: Demonstrates the recommended approach of using CSS variables instead of the theme() function for accessing theme values in Tailwind CSS v4.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/upgrade-guide.mdx#2025-04-22_snippet_17

LANGUAGE: CSS
CODE:
```
.my-class {
  background-color: theme(colors.red.500);
  background-color: var(--color-red-500);
}
```

----------------------------------------

TITLE: Lowercase Text Example
DESCRIPTION: Example showing how to use the lowercase utility class to transform text to lowercase.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/text-transform.mdx#2025-04-22_snippet_2

LANGUAGE: html
CODE:
```
<!-- [!code classes:lowercase] -->
<p class="lowercase">The quick brown fox ...</p>
```

----------------------------------------

TITLE: Justifying Grid Items to End with Tailwind CSS
DESCRIPTION: Shows how to use 'justify-items-end' and 'justify-items-end-safe' utilities to align grid items to the end of their inline axis. The safe variant aligns items to the start when there's not enough space available.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/justify-items.mdx#2025-04-22_snippet_1

LANGUAGE: HTML
CODE:
```
<div class="grid grid-flow-col justify-items-end ...">
  <div>01</div>
  <div>02</div>
  <div>03</div>
</div>
```

LANGUAGE: HTML
CODE:
```
<div class="grid grid-flow-col justify-items-end-safe ...">
  <div>01</div>
  <div>02</div>
  <div>03</div>
</div>
```

----------------------------------------

TITLE: CSS Build-time Import Example
DESCRIPTION: Demonstrates how Tailwind automatically bundles CSS imports without requiring additional preprocessing tools.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/compatibility.mdx#2025-04-22_snippet_0

LANGUAGE: css
CODE:
```
@import "tailwindcss";
@import "./typography.css";
```

----------------------------------------

TITLE: Flex Basis Percentage Example
DESCRIPTION: Demonstrates using fractional basis utilities like basis-1/3 and basis-2/3 for percentage-based sizing
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/flex-basis.mdx#2025-04-22_snippet_3

LANGUAGE: html
CODE:
```
<div class="flex flex-row">
  <div class="basis-1/3">01</div>
  <div class="basis-2/3">02</div>
</div>
```

----------------------------------------

TITLE: Styling Input Wrapper with :has() in React
DESCRIPTION: React component example demonstrating how to style a wrapper element based on the disabled state of its child input element using the :has() selector in Tailwind CSS.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-4/index.mdx#2025-04-22_snippet_5

LANGUAGE: jsx
CODE:
```
export function Input({ ... }) {
  return (
    <span className="has-[:disabled]:opacity-50 ...">
      <input ... />
    </span>
  )
}
```

----------------------------------------

TITLE: Centering Grid Items with Tailwind CSS
DESCRIPTION: Illustrates the use of 'justify-items-center' and 'justify-items-center-safe' utilities to center grid items along their inline axis. The safe variant aligns items to the start when centering is not possible due to space constraints.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/justify-items.mdx#2025-04-22_snippet_2

LANGUAGE: HTML
CODE:
```
<div class="grid grid-flow-col justify-items-center ...">
  <div>01</div>
  <div>02</div>
  <div>03</div>
</div>
```

LANGUAGE: HTML
CODE:
```
<div class="grid grid-flow-col justify-items-center-safe ...">
  <div>01</div>
  <div>02</div>
  <div>03</div>
</div>
```

----------------------------------------

TITLE: Removing Border Radius with Tailwind CSS
DESCRIPTION: This example demonstrates how to use the 'rounded-none' utility to remove an existing border radius from an element, resulting in sharp corners regardless of parent styling or browser defaults.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/border-radius.mdx#2025-04-22_snippet_5

LANGUAGE: html
CODE:
```
<button class="rounded-none ...">Save Changes</button>
```

----------------------------------------

TITLE: Using theme() in Arbitrary Values for HTML Elements
DESCRIPTION: Example demonstrating how to use the theme function with arbitrary values directly in HTML class attributes.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-1/index.mdx#2025-04-22_snippet_7

LANGUAGE: html
CODE:
```
<div class="bg-[image:linear-gradient(to_right,theme(colors.red.500)_75%,theme(colors.red.500/25%))]">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Implementing Vertical Scrolling with TailwindCSS
DESCRIPTION: Uses the overflow-y-scroll utility class to enable vertical scrolling with always-visible scrollbars. This implementation is suitable for creating vertically scrollable content containers.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/overflow.mdx#2025-04-22_snippet_7

LANGUAGE: html
CODE:
```
<div class="overflow-y-scroll ...">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Linear Gradient Angle Example
DESCRIPTION: Demonstrates the new angle-based linear gradient utilities.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4/index.mdx#2025-04-22_snippet_13

LANGUAGE: html
CODE:
```
<div class="bg-linear-45 from-indigo-500 via-purple-500 to-pink-500"></div>
```

----------------------------------------

TITLE: Implementing Start-Snapping Image Gallery with Tailwind CSS
DESCRIPTION: An example of creating a horizontally scrollable image gallery where images snap to the start using Tailwind CSS utilities.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/scroll-snap-align.mdx#2025-04-22_snippet_2

LANGUAGE: HTML
CODE:
```
<div class="snap-x ...">
  <div class="snap-start ...">
    <img src="/img/vacation-01.jpg" />
  </div>
  <div class="snap-start ...">
    <img src="/img/vacation-02.jpg" />
  </div>
  <div class="snap-start ...">
    <img src="/img/vacation-03.jpg" />
  </div>
  <div class="snap-start ...">
    <img src="/img/vacation-04.jpg" />
  </div>
  <div class="snap-start ...">
    <img src="/img/vacation-05.jpg" />
  </div>
  <div class="snap-start ...">
    <img src="/img/vacation-06.jpg" />
  </div>
</div>
```

----------------------------------------

TITLE: Basic Transition Button Example
DESCRIPTION: Demonstrates basic transition effects on a button with hover states for translation, scaling, and background color changes
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/transition-property.mdx#2025-04-22_snippet_1

LANGUAGE: html
CODE:
```
<button class="bg-blue-500 transition delay-150 duration-300 ease-in-out hover:-translate-y-1 hover:scale-110 hover:bg-indigo-500 ...">
  Save Changes
</button>
```

----------------------------------------

TITLE: Basic Positioning Example
DESCRIPTION: Demonstrates basic positioning utilities with a grid layout showing various positioning combinations.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/top-right-bottom-left.mdx#2025-04-22_snippet_1

LANGUAGE: html
CODE:
```
<div class="relative size-32 ...">
  <div class="absolute top-0 left-0 size-16 ...">01</div>
</div>

<div class="relative size-32 ...">
  <div class="absolute inset-x-0 top-0 h-16 ...">02</div>
</div>

<div class="relative size-32 ...">
  <div class="absolute top-0 right-0 size-16 ...">03</div>
</div>

<div class="relative size-32 ...">
  <div class="absolute inset-y-0 left-0 w-16 ...">04</div>
</div>

<div class="relative size-32 ...">
  <div class="absolute inset-0 ...">05</div>
</div>

<div class="relative size-32 ...">
  <div class="absolute inset-y-0 right-0 w-16 ...">06</div>
</div>

<div class="relative size-32 ...">
  <div class="absolute bottom-0 left-0 size-16 ...">07</div>
</div>

<div class="relative size-32 ...">
  <div class="absolute inset-x-0 bottom-0 h-16 ...">08</div>
</div>

<div class="relative size-32 ...">
  <div class="absolute right-0 bottom-0 size-16 ...">09</div>
</div>
```

----------------------------------------

TITLE: Parent-based State Styling with group Class in Tailwind CSS
DESCRIPTION: Demonstrates how to style child elements based on parent state using the group class and group-hover variant. Shows a complex example with SVG and text elements that respond to parent hover state.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#2025-04-22_snippet_15

LANGUAGE: html
CODE:
```
<a href="#" class="group ...">
  <div>
    <svg class="stroke-sky-500 group-hover:stroke-white ..." fill="none" viewBox="0 0 24 24">
      <!-- ... -->
    </svg>
    <h3 class="text-gray-900 group-hover:text-white ...">New project</h3>
  </div>
  <p class="text-gray-500 group-hover:text-white ...">Create a new project from a variety of starting templates.</p>
</a>
```

----------------------------------------

TITLE: Gitignore Configuration for Automatic Content Detection
DESCRIPTION: Example .gitignore file that Tailwind CSS v4.0 automatically reads to exclude files from content detection. This helps avoid scanning dependencies or generated files not under version control.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4/index.mdx#2025-04-22_snippet_3

LANGUAGE: shell
CODE:
```
/node_modules
/coverage
/.next/
/build
```

----------------------------------------

TITLE: Using forced-colors Variant in Tailwind CSS
DESCRIPTION: Example demonstrating how to handle forced color modes with radio inputs and custom theme selectors. Shows usage of forced-colors variant for appearance, visibility, and hiding elements.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#2025-04-22_snippet_40

LANGUAGE: html
CODE:
```
<label>
  <input type="radio" class="appearance-none forced-colors:appearance-auto" />
  <p class="hidden forced-colors:block">Cyan</p>
  <div class="bg-cyan-200 forced-colors:hidden ..."></div>
  <div class="bg-cyan-500 forced-colors:hidden ..."></div>
</label>
```

----------------------------------------

TITLE: Explicitly Excluding Classes
DESCRIPTION: CSS configuration to prevent specific utilities from being generated even if they are detected in source files, useful for optimizing the CSS output.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/detecting-classes-in-source-files.mdx#2025-04-22_snippet_16

LANGUAGE: css
CODE:
```
@import "tailwindcss";
@source not inline("{hover:,focus:,}bg-red-{50,{100..900..100},950}");
```

----------------------------------------

TITLE: Styling with Group-Has Variant
DESCRIPTION: Demonstrates using the group class and group-has-[a] variant to show/hide an SVG icon based on whether the group contains an anchor tag.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#2025-04-22_snippet_11

LANGUAGE: html
CODE:
```
<div class="group ...">
  <img src="..." />
  <h4>Spencer Sharp</h4>
  <svg class="hidden group-has-[a]:block ..."><!-- ... --></svg>
  <p>Product Designer at <a href="...">planeteria.tech</a></p>
</div>
```

----------------------------------------

TITLE: Customizing Dark Mode Selector in Tailwind CSS
DESCRIPTION: This CSS snippet shows how to override the default dark mode selector in Tailwind CSS to use a custom class instead of the prefers-color-scheme media query.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/dark-mode.mdx#2025-04-22_snippet_1

LANGUAGE: CSS
CODE:
```
@import "tailwindcss";

@custom-variant dark (&:where(.dark, .dark *));
```

----------------------------------------

TITLE: Applying Fixed Minimum Height Utilities - HTML
DESCRIPTION: Demonstrates the basic usage of Tailwind CSS fixed minimum height utilities such as min-h-80, min-h-64, etc., directly in HTML markup. It shows nested div elements where the inner divs have different min-h-* classes applied to control their minimum vertical dimension. Requires Tailwind CSS included in the project.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/min-height.mdx#_snippet_1

LANGUAGE: HTML
CODE:
```
<!-- [!code classes:min-h-80,min-h-64,min-h-48,min-h-40,min-h-32,min-h-24,min-h-full] -->
<div class="h-20 ...">
  <div class="min-h-80 ...">min-h-80</div>
  <div class="min-h-64 ...">min-h-64</div>
  <div class="min-h-48 ...">min-h-48</div>
  <div class="min-h-40 ...">min-h-40</div>
  <div class="min-h-32 ...">min-h-32</div>
  <div class="min-h-24 ...">min-h-24</div>
</div>
```

----------------------------------------

TITLE: Baseline Alignment in Tailwind CSS
DESCRIPTION: Example showing how to align an inline element's baseline with its parent's baseline using the align-baseline utility class.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/vertical-align.mdx#2025-04-22_snippet_0

LANGUAGE: html
CODE:
```
<span class="inline-block align-baseline">The quick brown fox...</span>
```

----------------------------------------

TITLE: Applying Percentage Minimum Height Utilities - HTML
DESCRIPTION: Demonstrates the usage of Tailwind CSS percentage-based minimum height utilities like min-h-full, min-h-9/10, min-h-3/4, min-h-1/2, and min-h-1/3 directly in HTML markup. It shows div elements where the min-h-* classes set the minimum height relative to their parent element. Requires Tailwind CSS included in the project.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/min-height.mdx#_snippet_3

LANGUAGE: HTML
CODE:
```
<!-- [!code classes:min-h-9/10,min-h-3/4,min-h-1/2,min-h-1/3,min-h-full] -->
<div class="min-h-full ...">min-h-full</div>
<div class="min-h-9/10 ...">min-h-9/10</div>
<div class="min-h-3/4 ...">min-h-3/4</div>
<div class="min-h-1/2 ...">min-h-1/2</div>
<div class="min-h-1/3 ...">min-h-1/3</div>
```

----------------------------------------

TITLE: Text-Top Alignment in Tailwind CSS
DESCRIPTION: Demonstrates aligning an element to the top of the parent element's font using the align-text-top utility class.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/vertical-align.mdx#2025-04-22_snippet_4

LANGUAGE: html
CODE:
```
<span class="inline-block align-text-top">The quick brown fox...</span>
```

----------------------------------------

TITLE: Applying Fixed Width Utilities in HTML
DESCRIPTION: HTML code snippet demonstrating the use of Tailwind's fixed-width utility classes (`w-96`, `w-80`, `w-64`, `w-48`, `w-40`, `w-32`, `w-24`). These classes set the width of the `div` elements based on the configured spacing scale. The comment indicates which classes are being demonstrated.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/width.mdx#2025-04-22_snippet_4

LANGUAGE: html
CODE:
```
<!-- [!code classes:w-96,w-80,w-64,w-48,w-40,w-32,w-24] -->
<div class="w-96 ...">w-96</div>
<div class="w-80 ...">w-80</div>
<div class="w-64 ...">w-64</div>
<div class="w-48 ...">w-48</div>
<div class="w-40 ...">w-40</div>
<div class="w-32 ...">w-32</div>
<div class="w-24 ...">w-24</div>
```

----------------------------------------

TITLE: Styling ::before and ::after Pseudo-Elements in Tailwind CSS
DESCRIPTION: Demonstrates how to style the ::before and ::after pseudo-elements using the 'after' variant to add required field indicators and decorative elements.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#2025-04-22_snippet_24

LANGUAGE: html
CODE:
```
<!-- [!code classes:after:content-['*']] -->
<!-- [!code classes:after:ml-0.5] -->
<!-- [!code classes:after:text-red-500] -->
<label>
  <span class="text-gray-700 after:ml-0.5 after:text-red-500 after:content-['*'] ...">Email</span>
  <input type="email" name="email" class="..." placeholder="<EMAIL>" />
</label>
```

----------------------------------------

TITLE: Container Scale Max-Width Example
DESCRIPTION: Example showing how to use Tailwind's container scale utilities for maximum width.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/max-width.mdx#2025-04-22_snippet_3

LANGUAGE: html
CODE:
```
<div class="max-w-md ...">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Configuring Container Sizes in TailwindCSS
DESCRIPTION: Shows how to configure custom container sizes in the tailwind.config.js file using the containers theme key.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-2/index.mdx#2025-04-22_snippet_13

LANGUAGE: javascript
CODE:
```
// tailwind.config.js
module.exports = {
  theme: {
    extend: {
      containers: {
        "2xs": "16rem",
        // etc...
      },
    },
  },
};
```

----------------------------------------

TITLE: React Example with LTR and RTL Direction
DESCRIPTION: This React component demonstrates the use of `dir="ltr"` and `dir="rtl"` to display content in both left-to-right and right-to-left directions. It uses Tailwind CSS classes for styling, including `ms-3` for margin-inline-start, which adapts automatically in RTL layouts.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-3/index.mdx#_snippet_5

LANGUAGE: JavaScript
CODE:
```
<div className="mx-auto grid max-w-lg grid-cols-1 gap-x-6 gap-y-10 sm:grid-cols-2">
  <div dir="ltr">
    <p className="mb-4 text-sm font-medium">Left-to-right</p>
    <div className="group flex items-center">
      <img
        className="h-12 w-12 shrink-0 rounded-full"
        src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
        alt=""
      />
      <div className="ms-3">
        <p className="text-sm font-medium text-slate-700 group-hover:text-slate-900 dark:text-slate-300 dark:group-hover:text-white">
          <>Tom Cook</>
        </p>
        <p className="text-sm font-medium text-slate-500 group-hover:text-slate-700 dark:group-hover:text-slate-300">
          <>Director of Operations</>
        </p>
      </div>
    </div>
  </div>
  <div dir="rtl">
    <p className="mb-4 text-sm font-medium">Right-to-left</p>
    <div className="group flex items-center">
      <img
        className="h-12 w-12 shrink-0 rounded-full"
        src="https://images.unsplash.com/photo-1563833717765-00462801314e?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
        alt=""
      />
      <div className="ms-3">
        <p className="text-sm font-medium text-slate-700 group-hover:text-slate-900 dark:text-slate-300 dark:group-hover:text-white">
          <>تامر كرم</>
        </p>
        <p className="text-sm font-medium text-slate-500 group-hover:text-slate-700 dark:group-hover:text-slate-300">
          <>الرئيس التنفيذي</>
        </p>
      </div>
    </div>
  </div>
</div>
```

----------------------------------------

TITLE: Using Headless UI Data Attributes with Tailwind CSS
DESCRIPTION: Demonstrates the usage of the new @headlessui/tailwindcss plugin with data-headlessui-state attributes for styling component states using Tailwind CSS classes. Shows implementation with Listbox.Option component including active and selected states.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/2022-09-09-new-personal-website-heroicons-2-headless-ui-v17/index.mdx#2025-04-22_snippet_2

LANGUAGE: jsx
CODE:
```
<Listbox.Option
  key={person.id}
  value={person}
  className="ui-active:bg-blue-500 ui-active:text-white ui-not-active:bg-white ui-not-active:text-black"
>
  <CheckIcon className="ui-selected:block hidden" />
  {person.name}
</Listbox.Option>
```

----------------------------------------

TITLE: Factor-based Flex Grow Example
DESCRIPTION: HTML example demonstrating how to use grow factors (grow-3, grow-7) to control proportional growth of flex items.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/flex-grow.mdx#2025-04-22_snippet_2

LANGUAGE: html
CODE:
```
<div class="flex ...">
  <div class="size-14 grow-3 ...">01</div>
  <div class="size-14 grow-7 ...">02</div>
  <div class="size-14 grow-3 ...">03</div>
</div>
```

----------------------------------------

TITLE: Translating Elements on X-Axis using Tailwind CSS
DESCRIPTION: Demonstrates how to use translate-x utilities to move elements horizontally. Shows examples of negative translation (-translate-x-4), positive translation (translate-x-2), and fractional translation (translate-x-1/2).
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/translate.mdx#2025-04-22_snippet_3

LANGUAGE: html
CODE:
```
<img class="-translate-x-4 ..." src="/img/mountains.jpg" />
<img class="translate-x-2 ..." src="/img/mountains.jpg" />
<img class="translate-x-1/2 ..." src="/img/mountains.jpg" />
```

----------------------------------------

TITLE: Specifying Grid Columns with Tailwind CSS
DESCRIPTION: Demonstrates how to use grid-cols-<number> utilities to create grids with equally sized columns in Tailwind CSS.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/grid-template-columns.mdx#2025-04-22_snippet_1

LANGUAGE: html
CODE:
```
<div class="grid grid-cols-4 gap-4">
  <div>01</div>
  <!-- ... -->
  <div>09</div>
</div>
```

----------------------------------------

TITLE: Setting CSS Variables Dynamically with Inline Styles in JSX
DESCRIPTION: Demonstrates setting CSS variables using inline styles based on dynamic props in a JSX component, and then referencing these variables within Tailwind utility classes.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/styling-with-utility-classes.mdx#_snippet_26

LANGUAGE: JSX
CODE:
```
// [!code filename:branded-button.jsx]
export function BrandedButton({ buttonColor, buttonColorHover, textColor, children }) {
  return (
    <button
      style={{
        // [!code highlight:4]
        "--bg-color": buttonColor,
        "--bg-color-hover": buttonColorHover,
        "--text-color": textColor,
      }}
      // [!code classes:bg-(--bg-color),text-(--text-color),hover:bg-(--bg-color-hover)]
      className="bg-(--bg-color) text-(--text-color) hover:bg-(--bg-color-hover) ..."
    >
      {children}
    </button>
  );
}
```

----------------------------------------

TITLE: Implementing Normal Distribution with Tailwind CSS
DESCRIPTION: Uses justify-normal utility to position flex items in their default position, as if no justify-content value was set.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/justify-content.mdx#2025-04-22_snippet_7

LANGUAGE: html
CODE:
```
<div class="flex justify-normal ...">
  <div>01</div>
  <div>02</div>
  <div>03</div>
</div>
```

----------------------------------------

TITLE: Setting Font Size and Line Height in HTML
DESCRIPTION: Shows how to set both font size and line height using Tailwind CSS utility classes in HTML.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/font-size.mdx#2025-04-22_snippet_2

LANGUAGE: html
CODE:
```
<p class="text-sm/6 ...">So I started to walk into the water...</p>
<p class="text-sm/7 ...">So I started to walk into the water...</p>
<p class="text-sm/8 ...">So I started to walk into the water...</p>
```

----------------------------------------

TITLE: Customizing Shadow Colors in HTML with Tailwind CSS
DESCRIPTION: Illustrates how to change the color of box shadows using Tailwind CSS utilities. The example shows three buttons with different colored shadows, including opacity adjustments.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/box-shadow.mdx#2025-04-22_snippet_3

LANGUAGE: html
CODE:
```
<button class="bg-cyan-500 shadow-lg shadow-cyan-500/50 ...">Subscribe</button>
<button class="bg-blue-500 shadow-lg shadow-blue-500/50 ...">Subscribe</button>
<button class="bg-indigo-500 shadow-lg shadow-indigo-500/50 ...">Subscribe</button>
```

----------------------------------------

TITLE: Installing Tailwind CSS v3.2 via NPM
DESCRIPTION: Command to upgrade existing Tailwind CSS projects to the latest version using npm.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-2/index.mdx#2025-04-22_snippet_0

LANGUAGE: sh
CODE:
```
npm install -D tailwindcss@latest
```

----------------------------------------

TITLE: Demonstrating Grid Auto Flow in HTML with Tailwind CSS Classes
DESCRIPTION: This code snippet shows a basic example of using grid-auto-flow utilities in Tailwind CSS. It creates a grid layout with specific placement of elements using classes like 'grid-flow-row-dense'.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/grid-auto-flow.mdx#2025-04-22_snippet_1

LANGUAGE: HTML
CODE:
```
<!-- [!code classes:grid-flow-row-dense] -->
<div class="grid grid-flow-row-dense grid-cols-3 grid-rows-3 ...">
  <div class="col-span-2">01</div>
  <div class="col-span-2">02</div>
  <div>03</div>
  <div>04</div>
  <div>05</div>
</div>
```

----------------------------------------

TITLE: Capitalize Text Example
DESCRIPTION: Example showing how to use the capitalize utility class to capitalize the first letter of each word.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/text-transform.mdx#2025-04-22_snippet_3

LANGUAGE: html
CODE:
```
<!-- [!code classes:capitalize] -->
<p class="capitalize">The quick brown fox ...</p>
```

----------------------------------------

TITLE: Styling Child Elements with Arbitrary Variants
DESCRIPTION: Demonstrates using arbitrary variants to target and style child elements uniformly within a parent container.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-1/index.mdx#2025-04-22_snippet_18

LANGUAGE: html
CODE:
```
<ul role="list" class="space-y-4 [&>*]:rounded-lg [&>*]:bg-white [&>*]:p-4 [&>*]:shadow">
  <li class="flex">
    <img class="h-10 w-10 rounded-full" src="..." alt="" />
    <div class="ml-3 overflow-hidden">
      <p class="text-sm font-medium text-slate-900">Kristen Ramos</p>
      <p class="truncate text-sm text-slate-500"><EMAIL></p>
    </div>
  </li>
  <!-- ... -->
</ul>
```

----------------------------------------

TITLE: Center Content Grid Layout
DESCRIPTION: Demonstrates using place-content-center to center grid items both horizontally and vertically within a container.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/place-content.mdx#2025-04-22_snippet_0

LANGUAGE: html
CODE:
```
<div class="grid h-48 grid-cols-2 place-content-center gap-4 ...">
  <div>01</div>
  <div>02</div>
  <div>03</div>
  <div>04</div>
</div>
```

----------------------------------------

TITLE: Using Arbitrary Values for Gradient Color Stops in Tailwind CSS
DESCRIPTION: This snippet demonstrates how to use arbitrary values within square brackets to define precise gradient color stop positions in Tailwind CSS. This provides maximum flexibility in gradient design.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-3/index.mdx#_snippet_27

LANGUAGE: HTML
CODE:
```
<div class="bg-gradient-to-r from-cyan-400 from-[21.56%] ...">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Vertical Scrolling with TailwindCSS overflow-y-auto
DESCRIPTION: Implementation of vertical scrolling container using TailwindCSS overflow-y-auto utility. This enables vertical scrolling when content exceeds the container height, defined by a specific height constraint (h-32).
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/overflow.mdx#2025-04-22_snippet_5

LANGUAGE: html
CODE:
```
<div class="h-32 overflow-y-auto ...">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Targeting Dark Mode with Tailwind CSS Classes
DESCRIPTION: This snippet illustrates how to use the 'dark' variant in Tailwind CSS to apply different styles for dark mode. It includes classes for background, text, and other elements that change in dark mode.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/colors.mdx#2025-04-22_snippet_4

LANGUAGE: html
CODE:
```
<div class="bg-white dark:bg-gray-800 rounded-lg px-6 py-8 ring shadow-xl ring-gray-900/5">
  <div>
    <span class="inline-flex items-center justify-center rounded-md bg-indigo-500 p-2 shadow-lg">
      <svg class="h-6 w-6 stroke-white" ...>
        <!-- ... -->
      </svg>
    </span>
  </div>
  <h3 class="text-gray-900 dark:text-white mt-5 text-base font-medium tracking-tight ">Writes upside-down</h3>
  <p class="text-gray-500 dark:text-gray-400 mt-2 text-sm ">
    The Zero Gravity Pen can be used to write in any orientation, including upside-down. It even works in outer space.
  </p>
</div>
```

----------------------------------------

TITLE: Dynamic Grid Columns Example
DESCRIPTION: Shows how arbitrary grid column values can be used directly without configuration.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4/index.mdx#2025-04-22_snippet_8

LANGUAGE: html
CODE:
```
<div class="grid grid-cols-15">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Setting CSS Variables with Arbitrary Values - HTML - Tailwind CSS
DESCRIPTION: Shows how to set custom CSS variables directly using arbitrary value syntax. The `[--gutter-width:1rem]` class sets a variable, and `lg:[--gutter-width:2rem]` shows how to apply it responsively.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/styling-with-utility-classes.mdx#_snippet_19

LANGUAGE: HTML
CODE:
```
<div class="[--gutter-width:1rem] lg:[--gutter-width:2rem]">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Implementing Responsive Box Shadow Design in Tailwind CSS (JSX)
DESCRIPTION: Shows how to create responsive box shadow designs using Tailwind CSS. The component toggles between a default class of 'shadow-none' and a featured class of 'shadow-lg' based on screen size.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/box-shadow.mdx#2025-04-22_snippet_12

LANGUAGE: jsx
CODE:
```
<ResponsiveDesign property="box-shadow" defaultClass="shadow-none" featuredClass="shadow-lg" />
```

----------------------------------------

TITLE: Adjusting Shadow Opacity in HTML with Tailwind CSS
DESCRIPTION: Shows how to use the opacity modifier in Tailwind CSS to adjust the opacity of box shadows. The example demonstrates three levels of opacity for an extra-large shadow.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/box-shadow.mdx#2025-04-22_snippet_2

LANGUAGE: html
CODE:
```
<div class="shadow-xl ..."></div>
<div class="shadow-xl/20 ..."></div>
<div class="shadow-xl/30 ..."></div>
```

----------------------------------------

TITLE: Using Arbitrary Variants in Tailwind CSS
DESCRIPTION: Shows how to use arbitrary variants to create custom selector variants directly in HTML, including stacking with built-in variants.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#2025-04-22_snippet_68

LANGUAGE: svelte
CODE:
```
<ul role="list">
  {#each items as item}
    <li class="[&.is-dragging]:cursor-grabbing">{item}</li>
  {/each}
</ul>
```

----------------------------------------

TITLE: Hiding Elements with Tailwind CSS
DESCRIPTION: This example shows how to use the 'hidden' utility class to remove an element from the document. It's applied to the first div in a flex container, making it invisible.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/display.mdx#2025-04-22_snippet_9

LANGUAGE: html
CODE:
```
<div class="flex ...">
  <div class="hidden ...">01</div>
  <div>02</div>
  <div>03</div>
</div>
```

----------------------------------------

TITLE: Text Ellipsis Example
DESCRIPTION: HTML example demonstrating the use of text-ellipsis utility class with overflow hidden.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/text-overflow.mdx#2025-04-22_snippet_2

LANGUAGE: html
CODE:
```
<p class="overflow-hidden text-ellipsis">The longest word in any of the major...</p>
```

----------------------------------------

TITLE: Implementing Image Gallery with Snap-End Scrolling in React/JSX
DESCRIPTION: A React component implementing a horizontal scrolling image gallery with snap-end behavior using Tailwind CSS utilities. The gallery features multiple images with smooth scroll snapping to their end positions.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/scroll-snap-align.mdx#2025-04-22_snippet_3

LANGUAGE: jsx
CODE:
```
<div className="relative">
  <div className="mr-6 mb-6 flex items-end justify-end pt-10">
    <div className="dark:highlight-white/10 mr-2 rounded bg-indigo-50 px-1.5 font-mono text-[0.625rem] leading-6 text-indigo-600 ring-1 ring-indigo-600 ring-inset dark:bg-indigo-500 dark:text-white dark:ring-0">
      snap point
    </div>
    <div className="absolute top-0 right-6 bottom-0 border-l border-indigo-500"></div>
  </div>
  <div className="relative flex w-full snap-x snap-mandatory gap-6 overflow-x-auto pb-14">
    <div className="shrink-0 snap-end scroll-mx-6">
      <div className="w-3 shrink-0 sm:-mr-[2px] sm:w-10"></div>
    </div>
    <!-- Additional image containers omitted for brevity -->
  </div>
</div>
```

----------------------------------------

TITLE: Updating Tailwind CSS to the latest version
DESCRIPTION: This command updates Tailwind CSS to the latest version using npm. It installs the latest version as a development dependency.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-3/index.mdx#_snippet_37

LANGUAGE: sh
CODE:
```
npm install -D tailwindcss@latest
```

----------------------------------------

TITLE: Applying Tailwind divide-style Utility HTML
DESCRIPTION: Illustrates applying the `divide-dashed` utility along with `divide-x-3` in HTML to create a dashed border between the child elements of a container, specifically styling the horizontal dividers.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/border-style.mdx#_snippet_4

LANGUAGE: html
CODE:
```
<!-- [!code classes:divide-dashed] -->
<div class="grid grid-cols-3 divide-x-3 divide-dashed divide-indigo-500">
  <div>01</div>
  <div>02</div>
  <div>03</div>
</div>
```

----------------------------------------

TITLE: Inset Ring Utilities in Tailwind CSS
DESCRIPTION: Examples of using inset ring utilities with different thicknesses. Shows the implementation of inset-ring, inset-ring-2, and inset-ring-4 classes.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/box-shadow.mdx#2025-04-22_snippet_8

LANGUAGE: html
CODE:
```
<button class="inset-ring ...">Subscribe</button>
<button class="inset-ring-2 ...">Subscribe</button>
<button class="inset-ring-4 ...">Subscribe</button>
```

----------------------------------------

TITLE: Using Custom Font in HTML
DESCRIPTION: Demonstrates usage of custom font theme variable in HTML markup
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/theme.mdx#2025-04-22_snippet_11

LANGUAGE: html
CODE:
```
<p class="font-script">This will use the Great Vibes font family.</p>
```

----------------------------------------

TITLE: Setting Base Path for Source Detection
DESCRIPTION: CSS configuration to set a custom base path for Tailwind's source scanning, useful in monorepo setups where build commands run from the root directory.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/detecting-classes-in-source-files.mdx#2025-04-22_snippet_7

LANGUAGE: css
CODE:
```
@import "tailwindcss" source("../src");
```

----------------------------------------

TITLE: Creating Arbitrary CSS Groups in Tailwind
DESCRIPTION: This CSS snippet demonstrates how to create arbitrary group-based variants using Tailwind CSS with custom selectors. It requires Tailwind CSS and respects predefined CSS syntax. By using custom selectors like 'group-[.is-published]', it achieves conditional block display. Inputs are CSS class names, and outputs are applied styles. Limitations include contextual selector usage and a defined Tailwind setup.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#2025-04-22_snippet_17

LANGUAGE: css
CODE:
```
.group-\[\.is-published\]\:block {
  &:is(:where(.group):is(.is-published) *) {
    display: block;
  }
}
```

----------------------------------------

TITLE: Place-Self Center Example
DESCRIPTION: HTML example showing how to use place-self-center utility to align an item at the center on both axes.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/place-self.mdx#2025-04-22_snippet_3

LANGUAGE: html
CODE:
```
<div class="grid grid-cols-3 gap-4 ...">
  <div>01</div>
  <div class="place-self-center ...">02</div>
  <div>03</div>
  <div>04</div>
  <div>05</div>
  <div>06</div>
</div>
```

----------------------------------------

TITLE: Percentage-based Translation Example
DESCRIPTION: Shows how to use fraction-based translation utilities to move elements by percentages of their size.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/translate.mdx#2025-04-22_snippet_2

LANGUAGE: html
CODE:
```
<img class="-translate-1/4 ..." src="/img/mountains.jpg" />
<img class="translate-1/6 ..." src="/img/mountains.jpg" />
<img class="translate-1/2 ..." src="/img/mountains.jpg" />
```

----------------------------------------

TITLE: Right Aligning Text with Tailwind CSS
DESCRIPTION: Shows the usage of the 'text-right' utility class to right-align text within an HTML paragraph element.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/text-align.mdx#2025-04-22_snippet_2

LANGUAGE: HTML
CODE:
```
<p class="text-right">So I started to walk into the water...</p>
```

----------------------------------------

TITLE: Scrolling if Needed in Tailwind CSS
DESCRIPTION: An example of using the overflow-auto utility to add scrollbars to an element when its content overflows. This is demonstrated with a list of profile cards that exceed the container height, allowing vertical scrolling.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/overflow.mdx#2025-04-22_snippet_3

LANGUAGE: HTML
CODE:
```
<div class="overflow-auto ...">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Basic Padding Example in Tailwind CSS
DESCRIPTION: Demonstrates the usage of the 'p-8' utility class to add padding to all sides of an element.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/padding.mdx#2025-04-22_snippet_2

LANGUAGE: html
CODE:
```
<!-- [!code classes:p-8] -->
<div class="p-8 ...">p-8</div>
```

----------------------------------------

TITLE: Using pointer-coarse variant for responsive touchscreen interfaces in HTML with Tailwind CSS
DESCRIPTION: This example demonstrates how to use Tailwind's pointer-coarse variant to make UI elements more touch-friendly on touchscreen devices. It applies larger padding, spacing, and fewer columns for better touchscreen interaction.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4-1/index.mdx#2025-04-22_snippet_13

LANGUAGE: html
CODE:
```
<!-- [!code classes:pointer-coarse:mt-6,pointer-coarse:grid-cols-3,pointer-coarse:gap-4,pointer-coarse:p-4] -->
<fieldset aria-label="Choose a memory option">
  <div class="flex items-center justify-between">
    <div>RAM</div>
    <a href="#"> See performance specs </a>
  </div>
  <div class="mt-4 grid grid-cols-6 gap-2 pointer-coarse:mt-6 pointer-coarse:grid-cols-3 pointer-coarse:gap-4">
    <label class="p-2 pointer-coarse:p-4 ...">
      <input type="radio" name="memory-option" value="4 GB" className="sr-only" />
      <span>4 GB</span>
    </label>
    <!-- ... -->
  </div>
</fieldset>
```

----------------------------------------

TITLE: Using Custom Font Family Utility
DESCRIPTION: Shows how to use a custom font family utility class in HTML.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/theme.mdx#2025-04-22_snippet_5

LANGUAGE: html
CODE:
```
<h1 class="font-poppins">This headline will use Poppins.</h1>
```

----------------------------------------

TITLE: Applying Percentage Width Utilities in HTML
DESCRIPTION: HTML code snippet demonstrating the use of Tailwind's percentage-based width utility classes (`w-1/2`, `w-2/5`, `w-3/5`, `w-full`, etc.) within flexbox containers (`flex`). These classes set the width of the `div` elements as a percentage of their parent container's width. The comment indicates which classes are being demonstrated.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/width.mdx#2025-04-22_snippet_6

LANGUAGE: html
CODE:
```
<!-- [!code classes:w-1/2,w-2/5,w-3/5,w-1/3,w-2/3,w-1/4,w-3/4,w-1/5,w-4/5,w-1/6,w-5/6,w-full] -->
<div class="flex ...">
  <div class="w-1/2 ...">w-1/2</div>
  <div class="w-1/2 ...">w-1/2</div>
</div>
<div class="flex ...">
  <div class="w-2/5 ...">w-2/5</div>
  <div class="w-3/5 ...">w-3/5</div>
</div>
<div class="flex ...">
  <div class="w-1/3 ...">w-1/3</div>
  <div class="w-2/3 ...">w-2/3</div>
</div>
<div class="flex ...">
  <div class="w-1/4 ...">w-1/4</div>
  <div class="w-3/4 ...">w-3/4</div>
</div>
<div class="flex ...">
  <div class="w-1/5 ...">w-1/5</div>
  <div class="w-4/5 ...">w-4/5</div>
</div>
<div class="flex ...">
  <div class="w-1/6 ...">w-1/6</div>
  <div class="w-5/6 ...">w-5/6</div>
</div>
<div class="w-full ...">w-full</div>
```

----------------------------------------

TITLE: Configuring Data Attribute Shortcuts in Tailwind
DESCRIPTION: Shows how to configure shortcuts for common data attribute selectors in the Tailwind configuration file.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-2/index.mdx#2025-04-22_snippet_9

LANGUAGE: javascript
CODE:
```
module.exports = {
  theme: {
    data: {
      checked: 'ui~="checked"',
    },
  },
  // ...
};
```

----------------------------------------

TITLE: Horizontal Scroll Snapping with Tailwind CSS
DESCRIPTION: Demonstrates how to implement horizontal scroll snapping using Tailwind CSS utilities. The example shows a container with multiple images that snap to center when scrolling.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/scroll-snap-type.mdx#2025-04-22_snippet_1

LANGUAGE: html
CODE:
```
<div class="snap-x ...">
  <div class="snap-center ...">
    <img src="/img/vacation-01.jpg" />
  </div>
  <div class="snap-center ...">
    <img src="/img/vacation-02.jpg" />
  </div>
  <div class="snap-center ...">
    <img src="/img/vacation-03.jpg" />
  </div>
  <div class="snap-center ...">
    <img src="/img/vacation-04.jpg" />
  </div>
  <div class="snap-center ...">
    <img src="/img/vacation-05.jpg" />
  </div>
  <div class="snap-center ...">
    <img src="/img/vacation-06.jpg" />
  </div>
</div>
```

----------------------------------------

TITLE: Using Custom Box Shadow Value in Tailwind CSS (JSX)
DESCRIPTION: Demonstrates how to use a custom box shadow value with Tailwind CSS utilities. The component allows customization of shadow, inset-shadow, ring, and inset-ring utilities.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/box-shadow.mdx#2025-04-22_snippet_11

LANGUAGE: jsx
CODE:
```
<UsingACustomValue
  utilities={["shadow", "inset-shadow", "ring", "inset-ring"]}
  name="box shadow"
  value="0_35px_35px_rgba(0,0,0,0.25)"
/>
```

----------------------------------------

TITLE: Container Query Ranges Implementation
DESCRIPTION: Demonstrates how to target specific container size ranges using combined min and max container queries.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/responsive-design.mdx#2025-04-22_snippet_14

LANGUAGE: html
CODE:
```
<div class="@container">
  <div class="flex flex-row @sm:@max-md:flex-col">
    <!-- ... -->
  </div>
</div>
```

----------------------------------------

TITLE: Basic Grid Rows Implementation
DESCRIPTION: Example showing how to create a grid with 4 equally sized rows using the grid-rows-4 utility class.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/grid-template-rows.mdx#2025-04-22_snippet_1

LANGUAGE: html
CODE:
```
<div class="grid grid-flow-col grid-rows-4 gap-4">
  <div>01</div>
  <!-- ... -->
  <div>09</div>
</div>
```

----------------------------------------

TITLE: Removing Background Images with Tailwind CSS
DESCRIPTION: This snippet shows how to use the 'bg-none' utility in Tailwind CSS to remove an existing background image from an element.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/background-image.mdx#2025-04-22_snippet_8

LANGUAGE: html
CODE:
```
<div class="bg-none"></div>
```

----------------------------------------

TITLE: Safelisting Specific Utilities
DESCRIPTION: CSS configuration to force Tailwind to generate specific utility classes even if they don't exist in your content files, with example of generated output.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/detecting-classes-in-source-files.mdx#2025-04-22_snippet_10

LANGUAGE: css
CODE:
```
@import "tailwindcss";
@source inline("underline");
```

----------------------------------------

TITLE: Using Stacked Tailwind Variants (HTML)
DESCRIPTION: Demonstrates how to combine multiple state variants in Tailwind CSS, such as `disabled:` and `hover:`, to apply a utility class's styles only when all specified conditions are met.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/styling-with-utility-classes.mdx#_snippet_8

LANGUAGE: html
CODE:
```
<!-- [!code classes:disabled:hover:bg-sky-500] -->
<button class="bg-sky-500 disabled:hover:bg-sky-500 ...">Save changes</button>
```

----------------------------------------

TITLE: Spin Animation Example
DESCRIPTION: Example showing how to implement a loading spinner animation using the animate-spin utility class.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/animation.mdx#2025-04-22_snippet_1

LANGUAGE: html
CODE:
```
<button type="button" class="bg-indigo-500 ..." disabled>
  <svg class="mr-3 size-5 animate-spin ..." viewBox="0 0 24 24">
    <!-- ... -->
  </svg>
  Processing…
</button>
```

----------------------------------------

TITLE: Basic ARIA Checked Variant Example
DESCRIPTION: Demonstrates using the aria-checked variant to conditionally style a checkbox background color based on its checked state.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3-2/index.mdx#2025-04-22_snippet_4

LANGUAGE: html
CODE:
```
<span class="bg-gray-600 aria-checked:bg-blue-600" aria-checked="true" role="checkbox">
  <!-- ... -->
</span>
```

----------------------------------------

TITLE: Basic Border Color Example
DESCRIPTION: Example showing how to apply basic border colors using TailwindCSS utilities.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/border-color.mdx#2025-04-22_snippet_1

LANGUAGE: html
CODE:
```
<div class="border-4 border-indigo-500 ..."></div>
<div class="border-4 border-purple-500 ..."></div>
<div class="border-4 border-sky-500 ..."></div>
```

----------------------------------------

TITLE: Importing Tailwind CSS with @import Directive (CSS)
DESCRIPTION: Use the @import directive to inline import CSS files into your stylesheet, including the main Tailwind CSS file itself. This makes Tailwind's base styles, components, and utilities available.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/functions-and-directives.mdx#_snippet_0

LANGUAGE: CSS
CODE:
```
@import "tailwindcss";
```

----------------------------------------

TITLE: Basic Aspect Ratio Implementation in HTML
DESCRIPTION: Demonstrates how to apply a 3:2 aspect ratio to an image element using Tailwind CSS classes.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/aspect-ratio.mdx#2025-04-22_snippet_0

LANGUAGE: html
CODE:
```
<img class="aspect-3/2 object-cover ..." src="/img/villas.jpg" />
```

----------------------------------------

TITLE: Combining Ring Utilities and Shadows HTML
DESCRIPTION: Shows that ring utilities (`focus:ring-2`) and standard box-shadow utilities (`shadow-sm`) can be applied simultaneously to an element. Tailwind CSS ensures both effects are rendered together using CSS custom properties without layout impact.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v2/index.mdx#_snippet_10

LANGUAGE: HTML
CODE:
```
<button class="shadow-sm focus:ring-2 ...">
```

----------------------------------------

TITLE: Compiling Tailwind CSS with CLI in JIT Watch Mode (Shell)
DESCRIPTION: Command to compile Tailwind CSS using the new CLI. It outputs the compiled CSS to `dist/tailwind.css`, watches for file changes (`--watch`), enables Just-in-Time mode (`--jit`), and purges unused styles by scanning HTML files in the `./src/` directory (`--purge`). Requires Node.js and npx.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-2-2/index.mdx#_snippet_1

LANGUAGE: sh
CODE:
```
npx tailwindcss -o dist/tailwind.css --watch --jit --purge="./src/**/*.html"
```

----------------------------------------

TITLE: Using content-normal in TailwindCSS
DESCRIPTION: Example showing how to use content-normal utility to pack content items in their default position
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/align-content.mdx#2025-04-22_snippet_7

LANGUAGE: html
CODE:
```
<div class="grid h-56 grid-cols-3 content-normal gap-4 ...">
  <div>01</div>
  <div>02</div>
  <div>03</div>
  <div>04</div>
  <div>05</div>
</div>
```

----------------------------------------

TITLE: Flex Auto Example in HTML
DESCRIPTION: Demonstrates flex-auto utility which allows flex items to both grow and shrink while considering their initial size.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/flex.mdx#2025-04-22_snippet_2

LANGUAGE: html
CODE:
```
<div class="flex ...">
  <div class="w-14 flex-none ...">01</div>
  <div class="w-64 flex-auto ...">02</div>
  <div class="w-32 flex-auto ...">03</div>
</div>
```

----------------------------------------

TITLE: Applying justify-items-stretch in HTML with Tailwind CSS
DESCRIPTION: This HTML snippet demonstrates how to use the justify-items-stretch utility class in a grid container. It creates a grid layout where items are stretched along their inline axis.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/justify-items.mdx#2025-04-22_snippet_4

LANGUAGE: html
CODE:
```
<div class="grid justify-items-stretch ...">
  <div>01</div>
  <div>02</div>
  <div>03</div>
  <div>04</div>
  <div>05</div>
  <div>06</div>
</div>
```

----------------------------------------

TITLE: End-Aligned Content Grid Layout
DESCRIPTION: Illustrates using place-content-end to align grid items at the end of the container.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/place-content.mdx#2025-04-22_snippet_2

LANGUAGE: html
CODE:
```
<div class="grid h-48 grid-cols-2 place-content-end gap-4 ...">
  <div>01</div>
  <div>02</div>
  <div>03</div>
  <div>04</div>
</div>
```

----------------------------------------

TITLE: Implementing Reversed Column Direction with Tailwind CSS Flex Utilities
DESCRIPTION: Example showing how to use flex-col-reverse to position flex items vertically in the opposite direction. This creates a column layout with items flowing from bottom to top.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/flex-direction.mdx#2025-04-22_snippet_3

LANGUAGE: html
CODE:
```
<!-- [!code classes:flex-col-reverse] -->
<div class="flex flex-col-reverse ...">
  <div>01</div>
  <div>02</div>
  <div>03</div>
</div>
```

----------------------------------------

TITLE: Justifying Grid Items to Start with Tailwind CSS
DESCRIPTION: Demonstrates the use of the 'justify-items-start' utility to align grid items to the start of their inline axis. This example shows a grid with 6 items, each aligned to the start of their cell.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/justify-items.mdx#2025-04-22_snippet_0

LANGUAGE: HTML
CODE:
```
<div class="grid justify-items-start ...">
  <div>01</div>
  <div>02</div>
  <div>03</div>
  <div>04</div>
  <div>05</div>
  <div>06</div>
</div>
```

----------------------------------------

TITLE: Basic List Style Examples - HTML/TailwindCSS
DESCRIPTION: Examples demonstrating the three basic list style utilities in TailwindCSS: list-disc, list-decimal, and list-none. Shows proper class usage and HTML structure for styled lists.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/list-style-type.mdx#2025-04-22_snippet_1

LANGUAGE: html
CODE:
```
<!-- [!code classes:list-disc] -->
<ul class="list-disc">
  <li>Now this is a story all about how, my life got flipped-turned upside down</li>
  <!-- ... -->
</ul>

<!-- [!code classes:list-decimal] -->
<ol class="list-decimal">
  <li>Now this is a story all about how, my life got flipped-turned upside down</li>
  <!-- ... -->
</ol>

<!-- [!code classes:list-none] -->
<ul class="list-none">
  <li>Now this is a story all about how, my life got flipped-turned upside down</li>
  <!-- ... -->
</ul>
```

----------------------------------------

TITLE: Vite Configuration for Tailwind
DESCRIPTION: Setup instructions for using Tailwind CSS v4 with Vite.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4-alpha/index.mdx#2025-04-22_snippet_8

LANGUAGE: typescript
CODE:
```
import tailwindcss from "@tailwindcss/vite";
import { defineConfig } from "vite";

export default defineConfig({
  plugins: [tailwindcss()],
});
```

----------------------------------------

TITLE: Using Arbitrary Variants for Custom Selectors
DESCRIPTION: Shows how to use arbitrary variants to apply styles based on any CSS selector, useful for complex scenarios or styling HTML you don't control.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/styling-with-utility-classes.mdx#_snippet_23

LANGUAGE: HTML
CODE:
```
<!-- [!code filename:HTML] -->
<!-- [!code classes:[&>[data-active]+span]:text-blue-600] -->
<div class="[&>[data-active]+span]:text-blue-600 ...">
  <span data-active><!-- ... --></span>
  <!-- [!code highlight:2] -->
  <span>This text will be blue</span>
</div>
```

LANGUAGE: CSS
CODE:
```
/* [!code filename:Simplified CSS] */
div > [data-active] + span {
  color: var(--color-blue-600);
}
```

----------------------------------------

TITLE: Items Center Example
DESCRIPTION: HTML example showing how to use items-center utility to align flex items along the center of the container's cross axis.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/align-items.mdx#2025-04-22_snippet_3

LANGUAGE: html
CODE:
```
<div class="flex items-center ...">
  <div class="py-4">01</div>
  <div class="py-12">02</div>
  <div class="py-8">03</div>
</div>
```

----------------------------------------

TITLE: Demonstrating Responsive Width Design (JSX)
DESCRIPTION: Uses the custom `ResponsiveDesign` React component to explain and potentially demonstrate how to apply width utilities conditionally based on screen size. The props `property="width"`, `defaultClass="w-1/2"`, and `featuredClass="w-full"` suggest it will illustrate changing width from `w-1/2` to `w-full` at a certain breakpoint (e.g., `md:w-full`).
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/width.mdx#2025-04-22_snippet_14

LANGUAGE: jsx
CODE:
```
<ResponsiveDesign property="width" defaultClass="w-1/2" featuredClass="w-full" />
```

----------------------------------------

TITLE: Implementing Column Direction with Tailwind CSS Flex Utilities
DESCRIPTION: Example showing how to use flex-col to position flex items vertically. This creates a column layout with items flowing from top to bottom.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/flex-direction.mdx#2025-04-22_snippet_2

LANGUAGE: html
CODE:
```
<!-- [!code classes:flex-col] -->
<div class="flex flex-col ...">
  <div>01</div>
  <div>02</div>
  <div>03</div>
</div>
```

----------------------------------------

TITLE: Spanning Rows with Tailwind CSS Grid Utilities (HTML)
DESCRIPTION: Demonstrates how to use Tailwind CSS row-span utilities to make elements span multiple rows in a grid layout. The example shows elements spanning 2 and 3 rows.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/grid-row.mdx#2025-04-22_snippet_1

LANGUAGE: html
CODE:
```
<div class="grid grid-flow-col grid-rows-3 gap-4">
  <div class="row-span-3 ...">01</div>
  <div class="col-span-2 ...">02</div>
  <div class="col-span-2 row-span-2 ...">03</div>
</div>
```

----------------------------------------

TITLE: Implementing Space Evenly Distribution with Tailwind CSS
DESCRIPTION: Uses justify-evenly utility to create equal spacing around flex items, ensuring consistent spacing between items including the edges of the container.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/justify-content.mdx#2025-04-22_snippet_5

LANGUAGE: html
CODE:
```
<div class="flex justify-evenly ...">
  <div>01</div>
  <div>02</div>
  <div>03</div>
</div>
```

----------------------------------------

TITLE: Applying Independent Row and Column Gaps in Tailwind CSS Grid
DESCRIPTION: Illustrates the use of gap-x-8 and gap-y-4 utility classes to set different gaps for columns and rows in a three-column grid layout with six items.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/gap.mdx#2025-04-22_snippet_2

LANGUAGE: HTML
CODE:
```
<div class="grid grid-cols-3 gap-x-8 gap-y-4">
  <div>01</div>
  <div>02</div>
  <div>03</div>
  <div>04</div>
  <div>05</div>
  <div>06</div>
</div>
```

----------------------------------------

TITLE: Left Aligning Text with Tailwind CSS
DESCRIPTION: Demonstrates how to use the 'text-left' utility class to left-align text within an HTML paragraph element.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/text-align.mdx#2025-04-22_snippet_1

LANGUAGE: HTML
CODE:
```
<p class="text-left">So I started to walk into the water...</p>
```

----------------------------------------

TITLE: Installing Tailwind CSS Standalone CLI
DESCRIPTION: Instructions for downloading and setting up the standalone Tailwind CSS CLI executable on macOS arm64
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/standalone-cli/index.mdx#2025-04-22_snippet_0

LANGUAGE: shell
CODE:
```
# Example for macOS arm64
curl -sLO https://github.com/tailwindlabs/tailwindcss/releases/latest/download/tailwindcss-macos-arm64
chmod +x tailwindcss-macos-arm64
mv tailwindcss-macos-arm64 tailwindcss
```

----------------------------------------

TITLE: Horizontal Padding Example in Tailwind CSS
DESCRIPTION: Demonstrates the use of the 'px-8' utility to add horizontal padding to an element.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/padding.mdx#2025-04-22_snippet_4

LANGUAGE: html
CODE:
```
<!-- [!code classes:px-8] -->
<div class="px-8 ...">px-8</div>
```

----------------------------------------

TITLE: Basic Select Element with Default Browser Styles
DESCRIPTION: Example showing a basic select element with default browser styling compared to one with appearance-none applied.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/appearance.mdx#2025-04-22_snippet_0

LANGUAGE: html
CODE:
```
<select>
  <option>Yes</option>
  <option>No</option>
  <option>Maybe</option>
</select>

<div class="grid">
  <select class="col-start-1 row-start-1 appearance-none bg-gray-50 dark:bg-gray-800 ...">
    <option>Yes</option>
    <option>No</option>
    <option>Maybe</option>
  </select>
  <svg class="pointer-events-none col-start-1 row-start-1 ...">
    <!-- ... -->
  </svg>
</div>
```

----------------------------------------

TITLE: Mobile-First Incorrect Example
DESCRIPTION: Example showing incorrect usage of sm: prefix for targeting mobile screens.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/responsive-design.mdx#2025-04-22_snippet_3

LANGUAGE: html
CODE:
```
<div class="sm:text-center"></div>
```

----------------------------------------

TITLE: Color Scheme Example Component in React/JSX
DESCRIPTION: Demonstrates an interactive example of color scheme utilities with three different options (light, dark, and light-dark) applied to date inputs, allowing users to see how they render.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/color-scheme.mdx#2025-04-22_snippet_3

LANGUAGE: jsx
CODE:
```
<Figure hint="Try switching your system color scheme to see the difference">

<Example>
  {
    <div className="flex justify-between gap-8 text-sm max-sm:flex-col">
      <div className="flex flex-grow flex-col items-center gap-3 text-center scheme-light">
        <p className="font-mono font-medium text-gray-500 dark:text-gray-400">scheme-light</p>
        <input
          type="date"
          className="w-full rounded-lg border border-gray-950/10 bg-[Field] px-3 py-2 text-[FieldText] dark:border-white/10"
        />
      </div>
      <div className="flex flex-grow flex-col items-center gap-3 text-center scheme-dark">
        <p className="font-mono font-medium text-gray-500 dark:text-gray-400">scheme-dark</p>
        <input
          type="date"
          className="w-full rounded-lg border border-gray-950/10 bg-[Field] px-3 py-2 text-[FieldText] dark:border-white/10"
        />
      </div>
      <div className="flex flex-grow flex-col items-center gap-3 text-center scheme-light-dark">
        <p className="font-medium text-gray-500 dark:text-gray-400">scheme-light-dark</p>
        <input
          type="date"
          className="w-full rounded-lg border border-gray-950/10 bg-[Field] px-3 py-2 text-[FieldText] dark:border-white/10"
        />
      </div>
    </div>
  }
</Example>
```

----------------------------------------

TITLE: Custom Font Family Theme Variable
DESCRIPTION: Demonstrates adding a custom font family theme variable to create a new font utility class.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/theme.mdx#2025-04-22_snippet_4

LANGUAGE: css
CODE:
```
@import "tailwindcss";

@theme {
  --font-poppins: Poppins, sans-serif;
}
```

----------------------------------------

TITLE: Stretching Grid Items with justify-items-stretch in JSX
DESCRIPTION: This snippet shows how to use the justify-items-stretch utility in a React component with Tailwind CSS classes. It creates a grid layout with stretched items and custom styling.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/justify-items.mdx#2025-04-22_snippet_3

LANGUAGE: jsx
CODE:
```
<div className="grid grid-cols-1">
  <Stripes border className="col-start-1 row-start-1 rounded-lg" />
  <div className="col-start-1 row-start-1 grid grid-cols-3 justify-items-stretch gap-4 font-mono text-sm leading-6 font-bold text-white">
    <div className="flex h-14 items-center justify-center rounded-lg bg-blue-500">01</div>
    <div className="flex h-14 items-center justify-center rounded-lg bg-blue-500">02</div>
    <div className="flex h-14 items-center justify-center rounded-lg bg-blue-500">03</div>
    <div className="flex h-14 items-center justify-center rounded-lg bg-blue-500">04</div>
    <div className="flex h-14 items-center justify-center rounded-lg bg-blue-500">05</div>
    <div className="flex h-14 items-center justify-center rounded-lg bg-blue-500">06</div>
  </div>
</div>
```

----------------------------------------

TITLE: Rendering Simultaneous Width/Height Example with Example Component (JSX)
DESCRIPTION: Uses the `Example` and `Figure` components to display a visual demonstration of `size-*` utilities (`size-16`, `size-20`, `size-24`, `size-32`, `size-40`). The inner JSX creates several `div` elements styled with these classes, setting both their width and height simultaneously based on the spacing scale. Responsive classes (`sm:grid`, `md:grid`) hide some elements on smaller screens.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/width.mdx#2025-04-22_snippet_11

LANGUAGE: jsx
CODE:
```
<Figure>

<Example>
  {
    <div className="grid grid-flow-col justify-center gap-4 text-center font-mono text-xs font-bold text-white">
      <div className="grid size-16 items-center justify-center rounded-lg bg-indigo-500">size-16</div>
      <div className="grid size-20 items-center justify-center rounded-lg bg-indigo-500">size-20</div>
      <div className="grid size-24 items-center justify-center rounded-lg bg-indigo-500">size-24</div>
      <div className="hidden size-32 items-center justify-center rounded-lg bg-indigo-500 sm:grid">size-32</div>
      <div className="hidden size-40 items-center justify-center rounded-lg bg-indigo-500 md:grid">size-40</div>
    </div>
  }
</Example>
```

----------------------------------------

TITLE: Aligning Item to Center using self-center (HTML)
DESCRIPTION: Use the `self-center` utility to align an item along the center of the container's cross axis, despite the container's `align-items` value.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/align-self.mdx#_snippet_2

LANGUAGE: html
CODE:
```
<!-- [!code classes:self-center] -->
<div class="flex items-stretch ...">
  <div>01</div>
  <div class="self-center ...">02</div>
  <div>03</div>
</div>
```

----------------------------------------

TITLE: Importing Tailwind CSS Base
DESCRIPTION: Basic setup for importing Tailwind CSS into a project using the CSS @import statement.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4-alpha/index.mdx#2025-04-22_snippet_2

LANGUAGE: css
CODE:
```
@import "tailwindcss";
```

----------------------------------------

TITLE: Basic Z-Index Example
DESCRIPTION: Demonstrates the basic usage of z-index utilities with stacked elements using different z-index values
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/z-index.mdx#2025-04-22_snippet_1

LANGUAGE: html
CODE:
```
<div class="z-40 ...">05</div>
<div class="z-30 ...">04</div>
<div class="z-20 ...">03</div>
<div class="z-10 ...">02</div>
<div class="z-0 ...">01</div>
```

----------------------------------------

TITLE: Using Tailwind CSS Color Variables in HTML Classes
DESCRIPTION: This HTML snippet shows how to use Tailwind CSS color variables as arbitrary values in utility classes, demonstrating the flexibility of the color system.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/colors.mdx#2025-04-22_snippet_6

LANGUAGE: html
CODE:
```
<div class="bg-[light-dark(var(--color-white),var(--color-gray-950))]">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Defining Custom Color Variables with OKLCH in CSS
DESCRIPTION: This snippet defines custom CSS variables for various color scales (gray, zinc, neutral, stone) using the OKLCH color space. It also includes definitions for black and white colors. These variables can be used to customize color schemes in TailwindCSS or other CSS frameworks.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/colors.mdx#2025-04-22_snippet_14

LANGUAGE: CSS
CODE:
```
  --color-gray-500: oklch(0.551 0.027 264.364);
  --color-gray-600: oklch(0.446 0.03 256.802);
  --color-gray-700: oklch(0.373 0.034 259.733);
  --color-gray-800: oklch(0.278 0.033 256.848);
  --color-gray-900: oklch(0.21 0.034 264.665);
  --color-gray-950: oklch(0.13 0.028 261.692);

  --color-zinc-50: oklch(0.985 0 0);
  --color-zinc-100: oklch(0.967 0.001 286.375);
  --color-zinc-200: oklch(0.92 0.004 286.32);
  --color-zinc-300: oklch(0.871 0.006 286.286);
  --color-zinc-400: oklch(0.705 0.015 286.067);
  --color-zinc-500: oklch(0.552 0.016 285.938);
  --color-zinc-600: oklch(0.442 0.017 285.786);
  --color-zinc-700: oklch(0.37 0.013 285.805);
  --color-zinc-800: oklch(0.274 0.006 286.033);
  --color-zinc-900: oklch(0.21 0.006 285.885);
  --color-zinc-950: oklch(0.141 0.005 285.823);

  --color-neutral-50: oklch(0.985 0 0);
  --color-neutral-100: oklch(0.97 0 0);
  --color-neutral-200: oklch(0.922 0 0);
  --color-neutral-300: oklch(0.87 0 0);
  --color-neutral-400: oklch(0.708 0 0);
  --color-neutral-500: oklch(0.556 0 0);
  --color-neutral-600: oklch(0.439 0 0);
  --color-neutral-700: oklch(0.371 0 0);
  --color-neutral-800: oklch(0.269 0 0);
  --color-neutral-900: oklch(0.205 0 0);
  --color-neutral-950: oklch(0.145 0 0);

  --color-stone-50: oklch(0.985 0.001 106.423);
  --color-stone-100: oklch(0.97 0.001 106.424);
  --color-stone-200: oklch(0.923 0.003 48.717);
  --color-stone-300: oklch(0.869 0.005 56.366);
  --color-stone-400: oklch(0.709 0.01 56.259);
  --color-stone-500: oklch(0.553 0.013 58.071);
  --color-stone-600: oklch(0.444 0.011 73.639);
  --color-stone-700: oklch(0.374 0.01 67.558);
  --color-stone-800: oklch(0.268 0.007 34.298);
  --color-stone-900: oklch(0.216 0.006 56.043);
  --color-stone-950: oklch(0.147 0.004 49.25);

  --color-black: #000;
  --color-white: #fff;
```

----------------------------------------

TITLE: Applying Custom Dark Mode Class in HTML
DESCRIPTION: This HTML snippet demonstrates how to apply the custom dark mode class to the HTML element for manual dark mode toggling.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/dark-mode.mdx#2025-04-22_snippet_2

LANGUAGE: HTML
CODE:
```
<html class="dark">
  <body>
    <div class="bg-white dark:bg-black">
      <!-- ... -->
    </div>
  </body>
</html>
```

----------------------------------------

TITLE: Space Around Items with justify-around
DESCRIPTION: Example demonstrating how to create equal spacing around flex items using justify-around utility.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/justify-content.mdx#2025-04-22_snippet_4

LANGUAGE: html
CODE:
```
<div class="flex justify-around ...">
  <div>01</div>
  <div>02</div>
  <div>03</div>
</div>
```

----------------------------------------

TITLE: Centering Grid Item with justify-self-center in HTML
DESCRIPTION: This snippet illustrates the use of justify-self-center to align a grid item along the center of its inline axis.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/justify-self.mdx#2025-04-22_snippet_2

LANGUAGE: HTML
CODE:
```
<div class="grid justify-items-stretch ...">
  <!-- ... -->
  <div class="justify-self-center ...">02</div>
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Using Arbitrary Background Color - HTML - Tailwind CSS
DESCRIPTION: Illustrates how to apply a background color not defined in the theme using Tailwind's arbitrary value syntax. The `bg-[#316ff6]` class sets the background color directly using a hexadecimal value.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/styling-with-utility-classes.mdx#_snippet_16

LANGUAGE: HTML
CODE:
```
<button class="bg-[#316ff6] ...">
  Sign in with Facebook
</button>
```

----------------------------------------

TITLE: Implementing Center-Snapping Image Gallery with Tailwind CSS
DESCRIPTION: An example of creating a horizontally scrollable image gallery where images snap to the center using Tailwind CSS utilities.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/scroll-snap-align.mdx#2025-04-22_snippet_1

LANGUAGE: HTML
CODE:
```
<div class="snap-x ...">
  <div class="snap-center ...">
    <img src="/img/vacation-01.jpg" />
  </div>
  <div class="snap-center ...">
    <img src="/img/vacation-02.jpg" />
  </div>
  <div class="snap-center ...">
    <img src="/img/vacation-03.jpg" />
  </div>
  <div class="snap-center ...">
    <img src="/img/vacation-04.jpg" />
  </div>
  <div class="snap-center ...">
    <img src="/img/vacation-05.jpg" />
  </div>
  <div class="snap-center ...">
    <img src="/img/vacation-06.jpg" />
  </div>
</div>
```

----------------------------------------

TITLE: Using prefers-reduced-motion Variants with Transitions
DESCRIPTION: Example of using the new motion-reduce variant to disable transitions for users who prefer reduced motion, enhancing accessibility for motion-sensitive users.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-1-6/index.mdx#2025-04-22_snippet_2

LANGUAGE: html
CODE:
```
<div class="transition duration-150 ease-in-out motion-reduce:transition-none ... ..."></div>
```

----------------------------------------

TITLE: Horizontal Scrolling with TailwindCSS overflow-x-auto
DESCRIPTION: Implementation of horizontal scrolling container using TailwindCSS overflow-x-auto utility. This allows content to scroll horizontally when it exceeds the container width while maintaining a clean UI.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/overflow.mdx#2025-04-22_snippet_4

LANGUAGE: html
CODE:
```
<div class="overflow-x-auto ...">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Responsive Flex Basis Example
DESCRIPTION: Shows how to implement responsive flex basis utilities with different breakpoint variants
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/flex-basis.mdx#2025-04-22_snippet_4

LANGUAGE: html
CODE:
```
<div class="flex flex-row">
  <div class="basis-1/4 md:basis-1/3">01</div>
  <div class="basis-1/4 md:basis-1/3">02</div>
  <div class="basis-1/2 md:basis-1/3">03</div>
</div>
```

----------------------------------------

TITLE: Aligning Item Baseline with self-baseline (HTML)
DESCRIPTION: Use the `self-baseline` utility to align an item such that its baseline aligns with the baseline of the flex container's cross axis. This is particularly useful when items have varying text sizes or padding and you want their text baselines to line up.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/align-self.mdx#_snippet_5

LANGUAGE: html
CODE:
```
<!-- [!code classes:self-baseline] -->
<div class="flex ...">
  <div class="self-baseline pt-2 pb-6">01</div>
  <div class="self-baseline pt-8 pb-12">02</div>
  <div class="self-baseline pt-12 pb-4">03</div>
</div>
```

----------------------------------------

TITLE: Image Gallery with Proximity Scroll Snapping in React/JSX
DESCRIPTION: A React component implementing a horizontal scrolling image gallery with proximity-based snap points. Uses Tailwind CSS utilities for scroll snapping, layout, and styling. Includes visual indicators for snap points and responsive spacing.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/scroll-snap-type.mdx#2025-04-22_snippet_3

LANGUAGE: jsx
CODE:
```
<div className="relative">
  <div className="mb-6 ml-[50%] flex items-end justify-start pt-10">
    <div className="dark:highlight-white/10 ml-2 rounded bg-indigo-50 px-1.5 font-mono text-[0.625rem] leading-6 text-indigo-600 ring-1 ring-indigo-600 ring-inset dark:bg-indigo-500 dark:text-white dark:ring-0">
      snap point
    </div>
    <div className="absolute top-0 bottom-0 left-1/2 border-l border-indigo-500"></div>
  </div>
  <div className="relative flex w-full snap-x snap-proximity gap-6 overflow-x-auto pb-14">
    <div className="shrink-0 snap-center">
      <div className="w-4 shrink-0 sm:w-37"></div>
    </div>
    <div className="shrink-0 snap-center first:pl-8 last:pr-8">
      <img
        className="h-40 w-80 shrink-0 rounded-lg bg-white"
        src="https://images.unsplash.com/photo-1604999565976-8913ad2ddb7c?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=320&h=160&q=80"
      />
    </div>
    <!-- Additional image elements -->
  </div>
</div>
```

----------------------------------------

TITLE: Customizing Font Size Theme in CSS
DESCRIPTION: Demonstrates how to customize the Tailwind CSS theme by adding a new font size with associated properties.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/font-size.mdx#2025-04-22_snippet_3

LANGUAGE: css
CODE:
```
@theme {
  --text-tiny: 0.625rem;
  --text-tiny--line-height: 1.5rem;
  --text-tiny--letter-spacing: 0.125rem;
  --text-tiny--font-weight: 500;
}
```

----------------------------------------

TITLE: Justifying Text with Tailwind CSS
DESCRIPTION: Demonstrates the application of the 'text-justify' utility class to justify text within an HTML paragraph element.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/text-align.mdx#2025-04-22_snippet_4

LANGUAGE: HTML
CODE:
```
<p class="text-justify">So I started to walk into the water...</p>
```

----------------------------------------

TITLE: Vue Component with CSS Variables Example
DESCRIPTION: Shows Vue component styling using CSS variables instead of @apply.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/compatibility.mdx#2025-04-22_snippet_8

LANGUAGE: html
CODE:
```
<template>
  <button><slot /></button>
</template>

<style scoped>
  button {
    background-color: var(--color-blue-500);
  }
</style>
```

----------------------------------------

TITLE: Implementing Content-Based Field Sizing in HTML with Tailwind CSS
DESCRIPTION: This snippet demonstrates using the field-sizing-content utility class to make a textarea adjust its size based on the content. The textarea will expand as content is added.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/field-sizing.mdx#2025-04-22_snippet_0

LANGUAGE: html
CODE:
```
<!-- [!code classes:field-sizing-content] -->
<textarea class="field-sizing-content ..." rows="2">
  Latex Salesman, Vanderlay Industries
</textarea>
```

----------------------------------------

TITLE: Complex Opacity UI Example
DESCRIPTION: A more complex example showing buttons with different opacity levels, including responsive design classes and additional styling.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/opacity.mdx#2025-04-22_snippet_2

LANGUAGE: jsx
CODE:
```
<div className="flex flex-col items-center justify-center gap-8 text-sm leading-6 font-bold text-white sm:flex-row sm:gap-16">
  <div className="flex shrink-0 flex-col items-center">
    <p className="mb-3 text-center font-mono text-xs font-medium text-gray-500 dark:text-gray-400">opacity-100</p>
    <button className="rounded-md bg-indigo-500 px-4 py-2 text-sm font-semibold text-white opacity-100">
      Button A
    </button>
  </div>
  <div className="flex shrink-0 flex-col items-center">
    <p className="mb-3 text-center font-mono text-xs font-medium text-gray-500 dark:text-gray-400">opacity-75</p>
    <button className="rounded-md bg-indigo-500 px-4 py-2 text-sm font-semibold text-white opacity-75">
      Button B
    </button>
  </div>
  <div className="flex shrink-0 flex-col items-center">
    <p className="mb-3 text-center font-mono text-xs font-medium text-gray-500 dark:text-gray-400">opacity-50</p>
    <button className="rounded-md bg-indigo-500 px-4 py-2 text-sm font-semibold text-white opacity-50">
      Button C
    </button>
  </div>
  <div className="flex shrink-0 flex-col items-center">
    <p className="mb-3 text-center font-mono text-xs font-medium text-gray-500 dark:text-gray-400">opacity-25</p>
    <button className="rounded-md bg-indigo-500 px-4 py-2 text-sm font-semibold text-white opacity-25">
      Button D
    </button>
  </div>
</div>
```

----------------------------------------

TITLE: Default Font Size and Line Height Config JS
DESCRIPTION: Illustrates the structure of the default `fontSize` scale in Tailwind's theme configuration, showing how each font size utility is now paired with a sensible default `lineHeight`. This simplifies typography styling by automatically applying a base line height.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v2/index.mdx#_snippet_13

LANGUAGE: js
CODE:
```
// Tailwind's default theme
module.exports = {
  theme: {
    // ...
    fontSize: {
      xs: ["0.75rem", { lineHeight: "1rem" }],
      sm: ["0.875rem", { lineHeight: "1.25rem" }],
      base: ["1rem", { lineHeight: "1.5rem" }],
      lg: ["1.125rem", { lineHeight: "1.75rem" }],
      xl: ["1.25rem", { lineHeight: "1.75rem" }],
      "2xl": ["1.5rem", { lineHeight: "2rem" }],
      "3xl": ["1.875rem", { lineHeight: "2.25rem" }],
      "4xl": ["2.25rem", { lineHeight: "2.5rem" }],
      "5xl": ["3rem", { lineHeight: "1" }],
      "6xl": ["3.75rem", { lineHeight: "1" }],
      "7xl": ["4.5rem", { lineHeight: "1" }],
      "8xl": ["6rem", { lineHeight: "1" }],
      "9xl": ["8rem", { lineHeight: "1" }],
    },
  },
};
```

----------------------------------------

TITLE: Ping Animation Example
DESCRIPTION: Implementation of a notification badge with ping animation effect.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/animation.mdx#2025-04-22_snippet_2

LANGUAGE: html
CODE:
```
<span class="relative flex size-3">
  <span class="absolute inline-flex h-full w-full animate-ping rounded-full bg-sky-400 opacity-75"></span>
  <span class="relative inline-flex size-3 rounded-full bg-sky-500"></span>
</span>
```

----------------------------------------

TITLE: Using @starting-style for Element Transitions
DESCRIPTION: Demonstrates the new starting variant for animating elements on initial display using @starting-style CSS feature.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4/index.mdx#2025-04-22_snippet_16

LANGUAGE: html
CODE:
```
<div>
  <button popovertarget="my-popover">Check for updates</button>
  <div popover id="my-popover" class="transition-discrete starting:open:opacity-0 ...">
    <!-- ... -->
  </div>
</div>
```

----------------------------------------

TITLE: Adding Custom Variants with @custom-variant Directive (CSS)
DESCRIPTION: The @custom-variant directive enables you to define your own custom variants based on specific CSS selectors. This allows you to create utility classes that apply styles conditionally based on custom states or attributes, like a data-theme attribute.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/functions-and-directives.mdx#_snippet_5

LANGUAGE: CSS
CODE:
```
@custom-variant theme-midnight (&:where([data-theme="midnight"] *));
```

----------------------------------------

TITLE: Using Arbitrary Peer Variants in HTML and CSS
DESCRIPTION: This snippet demonstrates how to create and use arbitrary peer variants for custom styling based on sibling states.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#2025-04-22_snippet_22

LANGUAGE: HTML
CODE:
```
<form>
  <label for="email">Email:</label>
  <input id="email" name="email" type="email" class="is-dirty peer" required />
  <div class="peer-[.is-dirty]:peer-required:block hidden">This field is required.</div>
  <!-- ... -->
</form>
```

LANGUAGE: CSS
CODE:
```
.peer-\[\.is-dirty\]\:peer-required\:block {
  &:is(:where(.peer):is(.is-dirty) ~ *) {
    &:is(:where(.peer):required ~ *) {
      display: block;
    }
  }
}
```

----------------------------------------

TITLE: Implementing Image Gallery with Scroll Snap in HTML
DESCRIPTION: Shows the HTML implementation of a scrollable image gallery using Tailwind CSS scroll snap utilities. Demonstrates the use of snap-x and snap-center classes for smooth scrolling behavior.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3/index.mdx#2025-04-22_snippet_4

LANGUAGE: html
CODE:
```
<div class="snap-x ...">
  <div class="snap-center ...">
    <img src="https://images.unsplash.com/photo-1604999565976-8913ad2ddb7c?auto=format&fit=crop&w=320&h=160&q=80" />
  </div>
  <div class="snap-center ...">
    <img src="https://images.unsplash.com/photo-1540206351-d6465b3ac5c1?auto=format&fit=crop&w=320&h=160&q=80" />
  </div>
  <!-- Additional image elements -->
</div>
```

----------------------------------------

TITLE: Applying Data Attribute for Dark Mode in HTML
DESCRIPTION: This HTML snippet demonstrates how to use a data attribute to activate dark mode when using the custom data attribute selector.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/dark-mode.mdx#2025-04-22_snippet_4

LANGUAGE: HTML
CODE:
```
<html data-theme="dark">
  <body>
    <div class="bg-white dark:bg-black">
      <!-- ... -->
    </div>
  </body>
</html>
```

----------------------------------------

TITLE: Translating Elements on Y-Axis using Tailwind CSS
DESCRIPTION: Demonstrates how to use translate-y utilities to move elements vertically. Shows examples of negative translation (-translate-y-4), positive translation (translate-y-2), and fractional translation (translate-y-1/2).
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/translate.mdx#2025-04-22_snippet_4

LANGUAGE: html
CODE:
```
<img class="-translate-y-4 ..." src="/img/mountains.jpg" />
<img class="translate-y-2 ..." src="/img/mountains.jpg" />
<img class="translate-y-1/2 ..." src="/img/mountains.jpg" />
```

----------------------------------------

TITLE: Visualizing Fixed Minimum Height - React/JSX
DESCRIPTION: This snippet provides a visual demonstration of Tailwind's fixed minimum height utilities like min-h-80, min-h-64, etc. It renders multiple bars within a container, each showing its applied minimum height class, illustrating the height difference based on the spacing scale. Requires React and Tailwind CSS setup.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/min-height.mdx#_snippet_0

LANGUAGE: Javascript
CODE:
```
<div className="mx-auto flex justify-center">
  <div className="relative flex items-end space-x-4 font-mono text-xs font-bold text-white">
    <Stripes border className="absolute -inset-x-[5%] h-20 w-[110%] rounded-lg" />
    <div className="relative flex h-96 w-8 items-end justify-center rounded-lg bg-blue-500">
      <div className="mb-8 -rotate-90 text-left text-nowrap">min-h-96</div>
    </div>
    <div className="relative flex h-80 w-8 items-end justify-center rounded-lg bg-blue-500">
      <div className="mb-8 -rotate-90 text-left text-nowrap">min-h-80</div>
    </div>
    <div className="relative flex h-64 w-8 items-end justify-center rounded-lg bg-blue-500">
      <div className="mb-8 -rotate-90 text-left text-nowrap">min-h-64</div>
    </div>
    <div className="relative flex h-48 w-8 items-end justify-center rounded-lg bg-blue-500">
      <div className="mb-8 -rotate-90 text-left text-nowrap">min-h-48</div>
    </div>
    <div className="relative flex h-40 w-8 items-end justify-center rounded-lg bg-blue-500">
      <div className="mb-8 -rotate-90 text-left text-nowrap">min-h-40</div>
    </div>
    <div className="relative flex h-32 w-8 items-end justify-center rounded-lg bg-blue-500">
      <div className="mb-8 -rotate-90 text-left text-nowrap">min-h-32</div>
    </div>
    <div className="relative flex h-24 w-8 items-end justify-center rounded-lg bg-blue-500">
      <div className="mb-8 -rotate-90 text-left text-nowrap">min-h-24</div>
    </div>
  </div>
</div>
```

----------------------------------------

TITLE: Pretty Text Wrapping with text-pretty in HTML
DESCRIPTION: Uses the text-pretty utility class to prevent orphans (single words on their own line) at the end of text blocks. This creates a more visually appealing text layout.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/text-wrap.mdx#2025-04-22_snippet_3

LANGUAGE: html
CODE:
```
<!-- [!code classes:text-pretty] -->
<article>
  <h3 class="text-pretty">Beloved Manhattan soup stand closes</h3>
  <p>New Yorkers are facing the winter chill...</p>
</article>
```

----------------------------------------

TITLE: Implementing Automatic Table Layout in HTML with Tailwind CSS
DESCRIPTION: This example demonstrates how to use the 'table-auto' utility class to automatically size table columns based on their content. It includes a full HTML structure for a table with three columns.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/table-layout.mdx#2025-04-22_snippet_1

LANGUAGE: HTML
CODE:
```
<table class="table-auto">
  <thead>
    <tr>
      <th>Song</th>
      <th>Artist</th>
      <th>Year</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>The Sliding Mr. Bones (Next Stop, Pottersville)</td>
      <td>Malcolm Lockyer</td>
      <td>1961</td>
    </tr>
    <tr>
      <td>Witchy Woman</td>
      <td>The Eagles</td>
      <td>1972</td>
    </tr>
    <tr>
      <td>Shining Star</td>
      <td>Earth, Wind, and Fire</td>
      <td>1975</td>
    </tr>
  </tbody>
</table>
```

----------------------------------------

TITLE: Demonstrating Conflicting Tailwind Utility Classes
DESCRIPTION: Shows an example where two conflicting utility classes (`grid` and `flex`) are applied to the same HTML element. The accompanying CSS illustrates that the class defined later in the stylesheet (`.grid`) takes precedence, regardless of the order in the HTML `class` attribute.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/styling-with-utility-classes.mdx#_snippet_32

LANGUAGE: html
CODE:
```
<!-- [!code filename:HTML] -->
<!-- prettier-ignore -->
<div class="grid flex">
  <!-- ... -->
</div>
```

LANGUAGE: css
CODE:
```
/* [!code filename: CSS] */
.flex {
  display: flex;
}
.grid {
  display: grid;
}
```

----------------------------------------

TITLE: Referencing External Color Variables in TailwindCSS
DESCRIPTION: Demonstrates how to reference external CSS color variables within a TailwindCSS theme using @theme inline. Includes dark mode support through data-theme attribute.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/colors.mdx#2025-04-22_snippet_12

LANGUAGE: css
CODE:
```
@import "tailwindcss";

:root {
  --acme-canvas-color: oklch(0.967 0.003 264.542);
}

[data-theme="dark"] {
  --acme-canvas-color: oklch(0.21 0.034 264.665);
}

@theme inline {
  --color-canvas: var(--acme-canvas-color);
}
```

----------------------------------------

TITLE: No-Repeat Background Example
DESCRIPTION: Shows how to prevent background image repetition using bg-no-repeat.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/background-repeat.mdx#2025-04-22_snippet_6

LANGUAGE: html
CODE:
```
<div class="bg-[url(/img/clouds.svg)] bg-center bg-no-repeat ..."></div>
```

----------------------------------------

TITLE: Adding Custom Utilities with @utility Directive (CSS)
DESCRIPTION: The @utility directive is used to define custom utility classes that can be used with Tailwind's built-in variants (like hover, focus, responsive prefixes). It allows you to encapsulate custom CSS rules into a single class name.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/functions-and-directives.mdx#_snippet_3

LANGUAGE: CSS
CODE:
```
@utility tab-4 {
  tab-size: 4;
}
```

----------------------------------------

TITLE: Implementing Text Wrapping with overflow-wrap in HTML
DESCRIPTION: Demonstrates the use of the new 'wrap-break-word' utility class to control text wrapping within an element, particularly useful for long words or URLs.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4-1/index.mdx#2025-04-22_snippet_10

LANGUAGE: html
CODE:
```
<p class="wrap-break-word">The longest word in any of the major...</p>
```

----------------------------------------

TITLE: Items Baseline Example
DESCRIPTION: HTML example showing how to use items-baseline utility to align flex items along their baselines.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/align-items.mdx#2025-04-22_snippet_5

LANGUAGE: html
CODE:
```
<div class="flex items-baseline ...">
  <div class="pt-2 pb-6">01</div>
  <div class="pt-8 pb-12">02</div>
  <div class="pt-12 pb-4">03</div>
</div>
```

----------------------------------------

TITLE: Using content-between in TailwindCSS
DESCRIPTION: Example showing how to use content-between utility to distribute rows with equal space between each line
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/align-content.mdx#2025-04-22_snippet_3

LANGUAGE: html
CODE:
```
<div class="grid h-56 grid-cols-3 content-between gap-4 ...">
  <div>01</div>
  <div>02</div>
  <div>03</div>
  <div>04</div>
  <div>05</div>
</div>
```

----------------------------------------

TITLE: Implementing Reversed Row Direction with Tailwind CSS Flex Utilities
DESCRIPTION: Example showing how to use flex-row-reverse to position flex items horizontally in the opposite direction of text. This creates a row layout with items flowing from right to left.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/flex-direction.mdx#2025-04-22_snippet_1

LANGUAGE: html
CODE:
```
<!-- [!code classes:flex-row-reverse] -->
<div class="flex flex-row-reverse ...">
  <div>01</div>
  <div>02</div>
  <div>03</div>
</div>
```

----------------------------------------

TITLE: Place-Self Stretch Example
DESCRIPTION: HTML example showing how to use place-self-stretch utility to stretch an item on both axes.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/place-self.mdx#2025-04-22_snippet_5

LANGUAGE: html
CODE:
```
<div class="grid grid-cols-3 gap-4 ...">
  <div>01</div>
  <div class="place-self-stretch ...">02</div>
  <div>03</div>
  <div>04</div>
  <div>05</div>
  <div>06</div>
</div>
```

----------------------------------------

TITLE: Removing Border with Tailwind border-none Utility HTML
DESCRIPTION: Shows how to use the `border-none` utility class in HTML to explicitly remove any border style from an element, useful for overriding inherited styles or styles applied under different conditions.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/border-style.mdx#_snippet_3

LANGUAGE: html
CODE:
```
<!-- [!code classes:border-none] -->
<button class="border-none ...">Save Changes</button>
```

----------------------------------------

TITLE: Using content-around in TailwindCSS
DESCRIPTION: Example showing how to use content-around utility to distribute rows with equal space around each line
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/align-content.mdx#2025-04-22_snippet_4

LANGUAGE: html
CODE:
```
<div class="grid h-56 grid-cols-3 content-around gap-4 ...">
  <div>01</div>
  <div>02</div>
  <div>03</div>
  <div>04</div>
  <div>05</div>
</div>
```

----------------------------------------

TITLE: Using content-start in TailwindCSS
DESCRIPTION: Example showing how to use content-start utility to pack rows against the start of the cross axis in a grid container
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/align-content.mdx#2025-04-22_snippet_0

LANGUAGE: html
CODE:
```
<div class="grid h-56 grid-cols-3 content-start gap-4 ...">
  <div>01</div>
  <div>02</div>
  <div>03</div>
  <div>04</div>
  <div>05</div>
</div>
```

----------------------------------------

TITLE: Removing Text Decoration in HTML
DESCRIPTION: Demonstrates using the no-underline utility class to remove text decoration from elements.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/text-decoration-line.mdx#2025-04-22_snippet_4

LANGUAGE: html
CODE:
```
<!-- [!code classes:no-underline] -->
<p class="no-underline">The quick brown fox...</p>
```

----------------------------------------

TITLE: Applying Percentage max-height Utilities (HTML)
DESCRIPTION: Illustrates the use of percentage-based max-height utilities in HTML, such as max-h-1/2 and max-h-full. The code shows elements constrained to a maximum height relative to their parent's height (h-96).
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/max-height.mdx#_snippet_3

LANGUAGE: html
CODE:
```
<!-- [!code classes:max-h-9/10,max-h-3/4,max-h-1/2,max-h-1/4,max-h-full] -->
<div class="h-96 ...">
  <div class="h-full max-h-9/10 ...">max-h-9/10</div>
  <div class="h-full max-h-3/4 ...">max-h-3/4</div>
  <div class="h-full max-h-1/2 ...">max-h-1/2</div>
  <div class="h-full max-h-1/4 ...">max-h-1/4</div>
  <div class="h-full max-h-full ...">max-h-full</div>
</div>
```

----------------------------------------

TITLE: Rendering Fixed Width Example with Example Component (JSX)
DESCRIPTION: Uses the `Example` and `Figure` components to render a visual demonstration of fixed-width utilities (`w-96`, `w-80`, `w-64`, etc.). The inner JSX creates several `div` elements, each styled with a different `w-<number>` class to show the effect on their width based on the Tailwind spacing scale. Responsive classes (`sm:block`) are used to hide some elements on smaller screens.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/width.mdx#2025-04-22_snippet_3

LANGUAGE: jsx
CODE:
```
<Figure>

<Example>
  {
    <div className="flex justify-center">
      <div className="space-y-4 text-center font-mono text-xs font-bold text-white">
        <div className="hidden w-96 rounded-lg bg-blue-500 px-4 py-2 sm:block">w-96</div>
        <div className="hidden w-80 rounded-lg bg-blue-500 px-4 py-2 sm:block">w-80</div>
        <div className="hidden w-64 rounded-lg bg-blue-500 px-4 py-2 sm:block">w-64</div>
        <div className="w-48 rounded-lg bg-blue-500 px-4 py-2">w-48</div>
        <div className="w-40 rounded-lg bg-blue-500 px-4 py-2">w-40</div>
        <div className="w-32 rounded-lg bg-blue-500 px-4 py-2">w-32</div>
        <div className="w-24 rounded-lg bg-blue-500 px-4 py-2">w-24</div>
      </div>
    </div>
  }
</Example>
```

----------------------------------------

TITLE: Basic Grid Auto Rows Example in HTML
DESCRIPTION: Demonstrates the basic usage of auto-rows-max utility class in a grid layout to control implicitly-created grid rows.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/grid-auto-rows.mdx#2025-04-22_snippet_1

LANGUAGE: html
CODE:
```
<div class="grid grid-flow-row auto-rows-max">
  <div>01</div>
  <div>02</div>
  <div>03</div>
</div>
```

----------------------------------------

TITLE: Styling Direct Children with Tailwind CSS
DESCRIPTION: This snippet shows how to use the '*' variant in Tailwind CSS to style direct children elements. It's particularly useful when you need to style children that you don't have direct control over. The example styles a list of category tags.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#2025-04-22_snippet_66

LANGUAGE: HTML
CODE:
```
<div>
  <h2>Categories<h2>
  <ul class="*:rounded-full *:border *:border-sky-100 *:bg-sky-50 *:px-2 *:py-0.5 dark:text-sky-300 dark:*:border-sky-500/15 dark:*:bg-sky-500/10 ...">
    <li>Sales</li>
    <li>Marketing</li>
    <li>SEO</li>
    <!-- ... -->
  </ul>
</div>
```

----------------------------------------

TITLE: Applying Overline Text Decoration in HTML
DESCRIPTION: Demonstrates using the overline utility class to add an overline to text elements.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/text-decoration-line.mdx#2025-04-22_snippet_2

LANGUAGE: html
CODE:
```
<!-- [!code classes:overline] -->
<p class="overline">The quick brown fox...</p>
```

----------------------------------------

TITLE: Implementing Hover Styles for Desktop-only in Tailwind CSS v4
DESCRIPTION: Shows the updated implementation of the hover variant in Tailwind CSS v4, which now only applies when the primary input device supports hover.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/upgrade-guide.mdx#2025-04-22_snippet_14

LANGUAGE: CSS
CODE:
```
@media (hover: hover) {
  .hover\:underline:hover {
    text-decoration: underline;
  }
}
```

----------------------------------------

TITLE: Custom Outline Configuration
DESCRIPTION: Shows how to configure custom outline styles in the Tailwind configuration file.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-1-9/index.mdx#2025-04-22_snippet_3

LANGUAGE: javascript
CODE:
```
module.exports = {
  theme: {
    extend: {
      outline: {
        blue: "2px solid #0000ff",
      },
    },
  },
};
```

LANGUAGE: javascript
CODE:
```
module.exports = {
  theme: {
    extend: {
      outline: {
        blue: ["2px solid #0000ff", "1px"],
      },
    },
  },
};
```

----------------------------------------

TITLE: Basic Scroll Margin Example - HTML
DESCRIPTION: Example showing how to use scroll-margin utilities with snap container items. Demonstrates the use of scroll-ml-6 class for left margin scrolling behavior.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/scroll-margin.mdx#2025-04-22_snippet_1

LANGUAGE: html
CODE:
```
<div class="snap-x ...">
  <div class="snap-start scroll-ml-6 ...">
    <img src="/img/vacation-01.jpg"/>
  </div>
  <div class="snap-start scroll-ml-6 ...">
    <img src="/img/vacation-02.jpg"/>
  </div>
  <div class="snap-start scroll-ml-6 ...">
    <img src="/img/vacation-03.jpg"/>
  </div>
  <div class="snap-start scroll-ml-6 ...">
    <img src="/img/vacation-04.jpg"/>
  </div>
  <div class="snap-start scroll-ml-6 ...">
    <img src="/img/vacation-05.jpg"/>
  </div>
</div>
```

----------------------------------------

TITLE: Using Extended Inset Utility HTML
DESCRIPTION: Demonstrates the use of an extended `inset` utility (like `top-8`). The `inset` and `translate` plugins now include the full extended spacing scale, offering more positioning and translation options.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v2/index.mdx#_snippet_18

LANGUAGE: html
CODE:
```
<div class="top-8">
  <!-- .... -->
</div>
```

----------------------------------------

TITLE: Customizing Animations in Tailwind Configuration
DESCRIPTION: Configuration example showing how to extend Tailwind's animation system by adding a custom 'wiggle' animation with its corresponding keyframes in the tailwind.config.js file.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-1-6/index.mdx#2025-04-22_snippet_1

LANGUAGE: js
CODE:
```
module.exports = {
  theme: {
    extend: {
      animation: {
        wiggle: "wiggle 1s ease-in-out infinite",
      },
      keyframes: {
        wiggle: {
          "0%, 100%": { transform: "rotate(-3deg)" },
          "50%": { transform: "rotate(3deg)" },
        },
      },
    },
  },
};
```

----------------------------------------

TITLE: Applying self-baseline-last in React/JSX
DESCRIPTION: Demonstrates the use of the `self-baseline-last` utility class within a React/JSX component structure to align items in a grid layout. Shows how to apply the class to specific elements (`<p>` and `<a>`) to align their baselines.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/align-self.mdx#_snippet_6

LANGUAGE: jsx
CODE:
```
<div className="mx-auto grid max-w-md divide-y divide-gray-100 border-x border-x-gray-200 text-gray-700 dark:divide-gray-800 dark:border-x-gray-800 dark:bg-gray-950/10 dark:text-gray-300">
  <div className="grid grid-cols-[auto_1fr_auto] gap-x-4 px-4 py-6">
    <img
      className="size-[2rem] rounded-full"
      src="https://spotlight.tailwindui.com/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.51a13c67.jpg&w=128&q=80"
      alt=""
    />
    <div className="font-semibold text-gray-900 sm:col-start-2 dark:text-white">Spencer Sharp</div>
    <p className="self-baseline-last text-sm sm:col-start-2">
      Working on the future of astronaut recruitment at Space Recruit.
    </p>
    <a
      href="#"
      className="self-baseline-last font-mono text-xs font-medium text-gray-400 underline hover:text-blue-500 dark:text-gray-500"
    >
      spacerecruit.com
    </a>
  </div>
  <div className="grid grid-cols-[auto_1fr_auto] gap-x-4 px-4 py-6">
    <img
      className="size-[2rem] rounded-full"
      src="https://images.unsplash.com/photo-1590895340509-793cb98788c9?q=80&w=256&h=256&&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
      alt=""
    />
    <div className="font-semibold text-gray-900 sm:col-start-2 dark:text-white">Alex Reed</div>
    <p className="self-baseline-last text-sm sm:col-start-2">A multidisciplinary designer.</p>
    <a
      href="#"
      className="self-baseline-last font-mono text-xs font-medium text-gray-400 underline hover:text-blue-500 dark:text-gray-500"
    >
      alex-reed.com
    </a>
  </div>
</div>
```

----------------------------------------

TITLE: Basic Transition Timing Function Example
DESCRIPTION: Demonstrates the usage of transition timing function utilities in Tailwind CSS with three different buttons showing ease-in, ease-out, and ease-in-out effects.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/transition-timing-function.mdx#2025-04-22_snippet_1

LANGUAGE: html
CODE:
```
<button class="duration-300 ease-in ...">Button A</button>
<button class="duration-300 ease-out ...">Button B</button>
<button class="duration-300 ease-in-out ...">Button C</button>
```

----------------------------------------

TITLE: Custom Breakpoint Configuration from Scratch
DESCRIPTION: Demonstrates resetting all default breakpoints and defining new custom breakpoints with semantic names.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/responsive-design.mdx#2025-04-22_snippet_10

LANGUAGE: css
CODE:
```
@import "tailwindcss";

@theme {
  --breakpoint-*: initial;
  --breakpoint-tablet: 40rem;
  --breakpoint-laptop: 64rem;
  --breakpoint-desktop: 80rem;
}
```

----------------------------------------

TITLE: Displaying Image at Original Size using Tailwind CSS
DESCRIPTION: This snippet shows how to use the 'object-none' utility class to display an image's content at its original size, ignoring the container size. This may result in the image being cropped.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/object-fit.mdx#2025-04-22_snippet_4

LANGUAGE: HTML
CODE:
```
<img class="h-48 w-96 object-none ..." src="/img/mountains.jpg" />
```

----------------------------------------

TITLE: Custom Breakpoint Theme Variable
DESCRIPTION: Example of defining a custom breakpoint theme variable for responsive variants.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/theme.mdx#2025-04-22_snippet_6

LANGUAGE: css
CODE:
```
@import "tailwindcss";

@theme {
  --breakpoint-3xl: 120rem;
}
```

----------------------------------------

TITLE: CSS Variables Usage Example
DESCRIPTION: Shows how to use native CSS variables with Tailwind for typography styling.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/compatibility.mdx#2025-04-22_snippet_1

LANGUAGE: css
CODE:
```
.typography {
  font-size: var(--text-base);
  color: var(--color-gray-700);
}
```

----------------------------------------

TITLE: Showing Content that Overflows in Tailwind CSS
DESCRIPTION: An example of using the overflow-visible utility to prevent content within an element from being clipped. This is demonstrated with a profile card where the image extends beyond the card boundaries.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/overflow.mdx#2025-04-22_snippet_1

LANGUAGE: HTML
CODE:
```
<div class="overflow-visible ...">
  <!-- ... -->
</div>
```

----------------------------------------

TITLE: Break All HTML Example
DESCRIPTION: Example demonstrating break-all class usage to add line breaks wherever necessary without preserving whole words.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/word-break.mdx#2025-04-22_snippet_2

LANGUAGE: html
CODE:
```
<!-- [!code classes:break-all] -->
<p class="break-all">The longest word in any of the major...</p>
```

----------------------------------------

TITLE: Configuring Gradient and Background Image Settings in Tailwind
DESCRIPTION: Configuration settings for gradient and background image utilities in tailwind.config.js, defining gradient directions and color stops.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-1-7/index.mdx#2025-04-22_snippet_0

LANGUAGE: javascript
CODE:
```
module.exports = {
  theme: {
    backgroundImage: {
      "gradient-to-t": "linear-gradient(to top, var(--gradient-color-stops))",
      "gradient-to-tr": "linear-gradient(to top right, var(--gradient-color-stops))",
      "gradient-to-r": "linear-gradient(to right, var(--gradient-color-stops))",
      "gradient-to-br": "linear-gradient(to bottom right, var(--gradient-color-stops))",
      "gradient-to-b": "linear-gradient(to bottom, var(--gradient-color-stops))",
      "gradient-to-bl": "linear-gradient(to bottom left, var(--gradient-color-stops))",
      "gradient-to-l": "linear-gradient(to left, var(--gradient-color-stops))",
      "gradient-to-tl": "linear-gradient(to top left, var(--gradient-color-stops))",
    },
    gradientColorStops: (theme) => theme("colors"),
  },
  variants: {
    backgroundImage: ["responsive"],
    gradientColorStops: ["responsive", "hover", "focus"],
  },
};
```

----------------------------------------

TITLE: Filling Container with Background Image
DESCRIPTION: Example of using bg-cover utility to scale and fill the container with a background image, cropping if necessary.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/background-size.mdx#2025-04-22_snippet_0

LANGUAGE: html
CODE:
```
<div class="bg-[url(/img/mountains.jpg)] bg-cover bg-center"></div>
```

----------------------------------------

TITLE: Using prefers-contrast Variant in Tailwind CSS
DESCRIPTION: Example showing how to use the contrast-more variant to enhance form input contrast based on user preferences. Demonstrates applying different border colors, placeholder text colors, and opacity levels.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#2025-04-22_snippet_39

LANGUAGE: html
CODE:
```
<label class="block">
  <span class="block text-sm font-medium text-gray-700">Social Security Number</span>
  <input
    class="border-gray-200 placeholder-gray-400 contrast-more:border-gray-400 contrast-more:placeholder-gray-500 ..."
  />
  <p class="text-gray-600 opacity-10 contrast-more:opacity-100 ...">We need this to steal your identity.</p>
</label>
```

----------------------------------------

TITLE: Using 2XL Breakpoint HTML
DESCRIPTION: Shows how to use the new `2xl:` breakpoint prefix (`2xl:text-9xl`) to apply styles that only take effect at viewport widths of 1536px and above, targeting extra-wide screens.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v2/index.mdx#_snippet_7

LANGUAGE: HTML
CODE:
```
<h1 class="2xl:text-9xl ...">Godzilla</h1>
```

----------------------------------------

TITLE: Implementing Responsive Breakpoints in Tailwind CSS
DESCRIPTION: This snippet demonstrates how Tailwind CSS translates responsive breakpoint utilities into container queries. It shows the CSS implementation for various max-width breakpoints.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#2025-04-22_snippet_77

LANGUAGE: CSS
CODE:
```
@container (width < 28rem)
```

LANGUAGE: CSS
CODE:
```
@container (width < 32rem)
```

LANGUAGE: CSS
CODE:
```
@container (width < 36rem)
```

LANGUAGE: CSS
CODE:
```
@container (width < 42rem)
```

LANGUAGE: CSS
CODE:
```
@container (width < 48rem)
```

LANGUAGE: CSS
CODE:
```
@container (width < 56rem)
```

LANGUAGE: CSS
CODE:
```
@container (width < 64rem)
```

LANGUAGE: CSS
CODE:
```
@container (width < 72rem)
```

LANGUAGE: CSS
CODE:
```
@container (width < 80rem)
```

----------------------------------------

TITLE: Importing Google Fonts in Tailwind CSS
DESCRIPTION: This CSS snippet shows how to import a font from Google Fonts and use it in Tailwind CSS theme. It emphasizes the importance of placing @import statements at the top of the CSS file.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/font-family.mdx#2025-04-22_snippet_4

LANGUAGE: css
CODE:
```
@import url("https://fonts.googleapis.com/css2?family=Roboto&display=swap");
@import "tailwindcss";

@theme {
  --font-roboto: "Roboto", sans-serif;
}
```

----------------------------------------

TITLE: Alternative to ::before Using Regular HTML Elements in Tailwind CSS
DESCRIPTION: Demonstrates how to achieve the same effect as ::before pseudo-elements using regular HTML elements, which can be simpler and more readable.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/hover-focus-and-other-states.mdx#2025-04-22_snippet_26

LANGUAGE: html
CODE:
```
<blockquote class="text-center text-2xl font-semibold text-gray-900 italic">
  When you look
  <span class="relative">
    <!-- [!code highlight:2] -->
    <span class="absolute -inset-1 block -skew-y-3 bg-pink-500" aria-hidden="true"></span>
    <span class="relative text-white">annoyed</span>
  </span>
  all the time, people think that you're busy.
</blockquote>
```

----------------------------------------

TITLE: Implementing Sidebar Layout Component in React with Catalyst
DESCRIPTION: Example of using the SidebarLayout component from Catalyst to create a responsive application layout with a sidebar that collapses into a mobile menu on smaller screens. Demonstrates how to integrate Navbar and Sidebar components.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/2024-05-24-catalyst-application-layouts/index.mdx#2025-04-22_snippet_0

LANGUAGE: jsx
CODE:
```
import { SidebarLayout } from "@/components/sidebar-layout";
import { Navbar } from "@/components/navbar";
import { Sidebar } from "@/components/sidebar";

function Example({ children }) {
  return (
    <SidebarLayout
      sidebar={<Sidebar>{/* Sidebar menu */}</Sidebar>}
      navbar={<Navbar>{/* Navbar for mobile screens */}</Navbar>}
    >
      {/* Your page content */}
    </SidebarLayout>
  );
}
```

----------------------------------------

TITLE: Video Aspect Ratio Implementation in HTML
DESCRIPTION: Shows how to apply a 16:9 video aspect ratio to an iframe element using the aspect-video utility class.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/aspect-ratio.mdx#2025-04-22_snippet_1

LANGUAGE: html
CODE:
```
<iframe class="aspect-video ..." src="https://www.youtube.com/embed/dQw4w9WgXcQ"></iframe>
```

----------------------------------------

TITLE: Implementing Tailwind CSS Play CDN
DESCRIPTION: Shows how to include the Tailwind CSS Play CDN in an HTML document. This script allows using all Tailwind features directly in the browser without compilation, intended for development purposes only.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3/index.mdx#2025-04-22_snippet_13

LANGUAGE: html
CODE:
```
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Example</title>
    <script src="https://cdn.tailwindcss.com/"></script> <!-- [!code ++] -->
  </head>
  <body>
    <!-- -->
  </body>
</html>
```

----------------------------------------

TITLE: Colored Inset Ring Utilities in Tailwind CSS
DESCRIPTION: Implementation of colored inset rings with opacity modifiers. Shows how to use inset-ring-blue-500 with optional opacity values.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/box-shadow.mdx#2025-04-22_snippet_9

LANGUAGE: html
CODE:
```
<button class="inset-ring-2 inset-ring-blue-500 ...">Subscribe</button>
<button class="inset-ring-2 inset-ring-blue-500/50 ...">Subscribe</button>
```

----------------------------------------

TITLE: Styling Native Form Controls with Tailwind CSS
DESCRIPTION: Demonstrates how to style file inputs and checkboxes using Tailwind CSS's new file: modifier and accent-color property. Includes custom styling for file upload buttons and checkbox accent colors.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v3/index.mdx#2025-04-22_snippet_6

LANGUAGE: html
CODE:
```
<form>
  <div class="flex items-center space-x-6">
    <div class="shrink-0">
      <img
        class="h-16 w-16 rounded-full object-cover"
        src="https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1361&q=80"
        alt="Current profile photo"
      />
    </div>
    <label class="block">
      <span class="sr-only">Choose profile photo</span>
      <input
        type="file"
        class="block w-full text-sm text-slate-500 file:mr-4 file:rounded-full file:border-0 file:bg-violet-50 file:py-2 file:text-sm file:font-semibold file:text-violet-700 hover:file:bg-violet-100"
      />
    </label>
  </div>
  <label class="mt-6 flex items-center justify-center space-x-2 text-sm font-medium text-slate-600">
    <input type="checkbox" class="accent-violet-500" checked />
    <span>Yes, send me all your stupid updates</span>
  </label>
</form>
```

----------------------------------------

TITLE: Configuring JIT Mode in Tailwind CSS
DESCRIPTION: Configuration snippet showing how to enable the Just-in-Time engine in Tailwind CSS v2.1 using the mode option in tailwind.config.js
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-2-1/index.mdx#2025-04-22_snippet_0

LANGUAGE: javascript
CODE:
```
module.exports = {
  mode: "jit",
  purge: [
    // ...
  ],
  // ...
};
```

----------------------------------------

TITLE: Transition Behavior Example Implementation
DESCRIPTION: HTML implementation showing how to use transition-normal and transition-discrete utilities with checkboxes to control element visibility and transition behavior.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/transition-behavior.mdx#2025-04-22_snippet_1

LANGUAGE: html
CODE:
```
<!-- [!code classes:transition-discrete] -->
<label class="peer ...">
  <input type="checkbox" checked />
</label>
<button class="hidden transition-all not-peer-has-checked:opacity-0 peer-has-checked:block ...">
  <!-- prettier-ignore -->
  I hide
</button>

<label class="peer ...">
  <input type="checkbox" checked />
</label>
<button class="hidden transition-all transition-discrete not-peer-has-checked:opacity-0 peer-has-checked:block ...">
  I fade out
</button>
```

----------------------------------------

TITLE: React JSX example of floated images with paragraph text
DESCRIPTION: Example in JSX showing the implementation of floated images with a paragraph of text using Tailwind utility classes for float directions, margin control, and image styling.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/clear.mdx#2025-04-22_snippet_6

LANGUAGE: jsx
CODE:
```
<>
  <img
    className="float-left mr-6 aspect-6/5 w-2/5 rounded-lg object-cover outline -outline-offset-1 outline-black/10"
    src="https://images.unsplash.com/photo-1434394354979-a235cd36269d?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1000&h=1000&q=90"
  />
  <img
    className="float-right ml-6 aspect-16/9 w-1/4 rounded-lg object-cover outline -outline-offset-1 outline-black/10"
    src="https://images.unsplash.com/photo-1554629947-334ff61d85dc?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=formathttps://images.unsplash.com/photo-1454496522488-7a8e488e8606?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1000&h=1000&q=90"
  />
  <p className="text-justify">
    Maybe we can live without libraries, people like you and me. Maybe. Sure, we're too old to change the world, but
    what about that kid, sitting down, opening a book, right now, in a branch at the local library and finding
    drawings of pee-pees and wee-wees on the Cat in the Hat and the Five Chinese Brothers? Doesn't HE deserve
    better? Look. If you think this is about overdue fines and missing books, you'd better think again. This is
    about that kid's right to read a book without getting his mind warped! Or: maybe that turns you on, Seinfeld;
    maybe that's how y'get your kicks. You and your good-time buddies.
  </p>
</>
```

----------------------------------------

TITLE: Using @variant Directive in TailwindCSS
DESCRIPTION: Demonstrates how to apply dark mode variant to custom CSS using the @variant directive. Shows both basic usage and nested variants for multiple variant combinations.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/adding-custom-styles.mdx#2025-04-22_snippet_3

LANGUAGE: css
CODE:
```
.my-element {
  background: white;

  @variant dark {
    background: black;
  }
}
```

LANGUAGE: css
CODE:
```
.my-element {
  background: white;

  @variant dark {
    @variant hover {
      background: black;
    }
  }
}
```

----------------------------------------

TITLE: Using Transition Component in React with Tailwind CSS
DESCRIPTION: Example of using the Transition component to add enter/leave animations to elements using Tailwind CSS utility classes. This component allows for fade-in and fade-out animations triggered by the 'isOpen' state.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/building-react-and-vue-support-for-tailwind-ui/index.mdx#2025-04-22_snippet_1

LANGUAGE: jsx
CODE:
```
<Transition
  show={isOpen}
  enter="transition-opacity duration-75"
  enterFrom="opacity-0"
  enterTo="opacity-100"
  leave="transition-opacity duration-150"
  leaveFrom="opacity-100"
  leaveTo="opacity-0"
>
  I will fade in and out
</Transition>
```

----------------------------------------

TITLE: Applying Dark Mode Hover Styles HTML
DESCRIPTION: Demonstrates how to combine the `dark:` variant with state variants like `hover:` (`dark:hover:bg-gray-50`) to apply specific styles when both dark mode is active and the element is being hovered over. Requires `darkMode` enabled.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v2/index.mdx#_snippet_4

LANGUAGE: HTML
CODE:
```
<button class="bg-gray-900 hover:bg-gray-800 dark:bg-white dark:hover:bg-gray-50">
```

----------------------------------------

TITLE: Stretching Item with self-stretch (HTML)
DESCRIPTION: Use the `self-stretch` utility to stretch an item to fill the container's cross axis, overriding the container's `align-items` value. This is commonly used in flex or grid layouts to make an item occupy the full height or width available.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/align-self.mdx#_snippet_4

LANGUAGE: html
CODE:
```
<!-- [!code classes:self-stretch] -->
<div class="flex items-stretch ...">
  <div>01</div>
  <div class="self-stretch ...">02</div>
  <div>03</div>
</div>
```

----------------------------------------

TITLE: Visualizing Percentage Minimum Height - React/JSX
DESCRIPTION: This snippet visually demonstrates Tailwind's percentage-based minimum height utilities like min-h-full, min-h-9/10, min-h-3/4, min-h-1/2, and min-h-1/3. It renders bars inside a fixed-height container, showing how these classes set the minimum height as a fraction of the parent's height. Requires React and Tailwind CSS setup.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/min-height.mdx#_snippet_2

LANGUAGE: Javascript
CODE:
```
<div className="flex h-96 items-end justify-center space-x-4 font-mono text-xs font-bold text-white">
  <div className="relative flex h-full items-end">
    <Stripes border className="absolute inset-0 h-full rounded-lg" />
    <div className="relative flex min-h-full w-8 items-end justify-center rounded-lg bg-sky-500">
      <div className="mb-10 -rotate-90 text-left text-nowrap">min-h-full</div>
    </div>
  </div>
  <div className="relative flex h-full items-end">
    <Stripes border className="absolute inset-0 h-full rounded-lg" />
    <div className="relative flex min-h-9/10 w-8 items-end justify-center rounded-lg bg-sky-500">
      <div className="mb-10 -rotate-90 text-left text-nowrap">min-h-9/10</div>
    </div>
  </div>
  <div className="relative flex h-full items-end">
    <Stripes border className="absolute inset-0 h-full rounded-lg" />
    <div className="relative flex min-h-3/4 w-8 items-end justify-center rounded-lg bg-sky-500">
      <div className="mb-10 -rotate-90 text-left text-nowrap">min-h-3/4</div>
    </div>
  </div>
  <div className="relative flex h-full items-end">
    <Stripes border className="absolute inset-0 h-full rounded-lg" />
    <div className="relative flex min-h-1/2 w-8 items-end justify-center rounded-lg bg-sky-500">
      <div className="mb-10 -rotate-90 text-left text-nowrap">min-h-1/2</div>
    </div>
  </div>
  <div className="relative flex h-full items-end">
    <Stripes border className="absolute inset-0 h-full rounded-lg" />
    <div className="relative flex min-h-1/3 w-8 items-end justify-center rounded-lg bg-sky-500">
      <div className="mb-10 -rotate-90 text-left text-nowrap">min-h-1/3</div>
    </div>
  </div>
</div>
```

----------------------------------------

TITLE: Setting Both Width and Height (Size Utilities) - Tailwind CSS HTML
DESCRIPTION: Explains how `size-<number>` utilities (like `size-16`, `size-20`, etc.) can be used to set both the width and height of an element simultaneously based on the spacing scale.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/height.mdx#_snippet_7

LANGUAGE: html
CODE:
```
<!-- [!code classes:size-16,size-20,size-24,size-32,size-40] -->
<div class="size-16 ...">size-16</div>
<div class="size-20 ...">size-20</div>
<div class="size-24 ...">size-24</div>
<div class="size-32 ...">size-32</div>
<div class="size-40 ...">size-40</div>
```

----------------------------------------

TITLE: Using Current Text Color for SVG Fill in HTML
DESCRIPTION: Demonstrates how to use the fill-current utility to set the SVG fill color to match the current text color, useful for creating interactive elements.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/fill.mdx#2025-04-22_snippet_3

LANGUAGE: html
CODE:
```
<button class="bg-white text-indigo-600 hover:bg-indigo-600 hover:text-white ...">
  <svg class="size-5 fill-current ...">
    <!-- ... -->
  </svg>
  Check for updates
</button>
```

----------------------------------------

TITLE: Items End Example
DESCRIPTION: HTML example demonstrating the items-end utility to align flex items to the end of the container's cross axis.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/align-items.mdx#2025-04-22_snippet_4

LANGUAGE: html
CODE:
```
<div class="flex items-end ...">
  <div class="py-4">01</div>
  <div class="py-12">02</div>
  <div class="py-8">03</div>
</div>
```

----------------------------------------

TITLE: Implementing Text Shadow Utilities in HTML
DESCRIPTION: HTML example demonstrating the usage of different text shadow utilities from text-shadow-2xs to text-shadow-lg. Shows how to apply these new utilities to text elements.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4-1/index.mdx#2025-04-22_snippet_3

LANGUAGE: html
CODE:
```
<!-- [!code classes:text-shadow-2xs,text-shadow-xs,text-shadow-sm,text-shadow-md,text-shadow-lg,text-shadow-xl] -->
<p class="text-shadow-2xs ...">The quick brown fox...</p>
<p class="text-shadow-xs ...">The quick brown fox...</p>
<p class="text-shadow-sm ...">The quick brown fox...</p>
<p class="text-shadow-md ...">The quick brown fox...</p>
<p class="text-shadow-lg ...">The quick brown fox...</p>
```

----------------------------------------

TITLE: Applying Fixed max-height Utilities (HTML)
DESCRIPTION: Demonstrates how to apply fixed maximum heights to elements using Tailwind's spacing scale classes (e.g., max-h-80, max-h-64). The example shows elements constrained within a parent with a fixed height (h-96) to illustrate the effect.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/max-height.mdx#_snippet_2

LANGUAGE: html
CODE:
```
<!-- [!code classes:max-h-80,max-h-64,max-h-48,max-h-40,max-h-32,max-h-24,max-h-full] -->
<div class="h-96 ...">
  <div class="h-full max-h-80 ...">max-h-80</div>
  <div class="h-full max-h-64 ...">max-h-64</div>
  <div class="h-full max-h-48 ...">max-h-48</div>
  <div class="h-full max-h-40 ...">max-h-40</div>
  <div class="h-full max-h-32 ...">max-h-32</div>
  <div class="h-full max-h-24 ...">max-h-24</div>
</div>
```

----------------------------------------

TITLE: Using Arbitrary Values and CSS Variables for Opacity in Tailwind CSS
DESCRIPTION: This example shows how to use arbitrary values and CSS variable shorthand for opacity adjustment in Tailwind CSS background classes.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/colors.mdx#2025-04-22_snippet_3

LANGUAGE: html
CODE:
```
<div class="bg-pink-500/[71.37%]"><!-- ... --></div>
<div class="bg-cyan-400/(--my-alpha-value)"><!-- ... --></div>
```

----------------------------------------

TITLE: Cropping Background to Text - JSX
DESCRIPTION: Demonstrates using `bg-clip-text` in JSX with a gradient background (`bg-gradient-to-r`). The text color is set to transparent (`text-transparent`) to allow the background to show through the text shape. This creates a text fill effect using the background.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/background-clip.mdx#_snippet_3

LANGUAGE: JSX
CODE:
```
    {
    <p className="bg-gradient-to-r from-pink-500 to-violet-500 bg-clip-text text-center text-4xl leading-none font-extrabold tracking-tight text-transparent sm:text-5xl">
      Hello world
    </p>
  }
```

----------------------------------------

TITLE: 3D Transform Example
DESCRIPTION: Shows usage of new 3D transform utilities with perspective and rotation.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/blog/tailwindcss-v4/index.mdx#2025-04-22_snippet_12

LANGUAGE: html
CODE:
```
<div class="perspective-distant">
  <article class="rotate-x-51 rotate-z-43 transform-3d ...">
    <!-- ... -->
  </article>
</div>
```

----------------------------------------

TITLE: Applying X-Axis Skew Transformations with Tailwind CSS
DESCRIPTION: Demonstrates how to skew HTML elements on the x-axis using Tailwind CSS utility classes like -skew-x-12, skew-x-6, and skew-x-12. These classes apply CSS transform: skewX() with different angle values.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/skew.mdx#2025-04-22_snippet_3

LANGUAGE: html
CODE:
```
<!-- [!code classes:-skew-x-12,skew-x-12,skew-x-6] -->
<img class="-skew-x-12 ..." src="/img/mountains.jpg" />
<img class="skew-x-6 ..." src="/img/mountains.jpg" />
<img class="skew-x-12 ..." src="/img/mountains.jpg" />
```

----------------------------------------

TITLE: Horizontal and Vertical Borders Example
DESCRIPTION: Example demonstrating the application of border colors to horizontal and vertical sides simultaneously.
SOURCE: https://github.com/tailwindlabs/tailwindcss.com/blob/main/src/docs/border-color.mdx#2025-04-22_snippet_4

LANGUAGE: html
CODE:
```
<div class="border-4 border-indigo-200 border-x-indigo-500 ..."></div>
<div class="border-4 border-indigo-200 border-y-indigo-500 ..."></div>
```