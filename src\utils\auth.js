const TOKEN_KEY = 'admin_token'
const USER_INFO_KEY = 'admin_user_info'

/**
 * Get token from localStorage
 */
export function getToken() {
  return localStorage.getItem(TOKEN_KEY)
}

/**
 * Set token in localStorage
 */
export function setToken(token) {
  return localStorage.setItem(TOKEN_KEY, token)
}

/**
 * Remove token from localStorage
 */
export function removeToken() {
  return localStorage.removeItem(TOKEN_KEY)
}

/**
 * Get user info from localStorage
 */
export function getUserInfo() {
  const userInfo = localStorage.getItem(USER_INFO_KEY)
  return userInfo ? JSON.parse(userInfo) : null
}

/**
 * Set user info in localStorage
 */
export function setUserInfo(info) {
  return localStorage.setItem(USER_INFO_KEY, JSON.stringify(info))
}

/**
 * Remove user info from localStorage
 */
export function removeUserInfo() {
  return localStorage.removeItem(USER_INFO_KEY)
}

/**
 * Clear all auth data (logout)
 */
export function clearAuth() {
  removeToken()
  removeUserInfo()
} 