<template>
  <vxe-modal
    v-model="groupOptions.visible"
    :title="groupOptions.title"
    :width="formattedWidth"
    :height="formattedHeight"
    :show-close="groupOptions.showClose !== false"
    :show-footer="groupOptions.showFooter !== false"
    :show-cancel-button="groupOptions.showCancelButton !== false"
    :show-confirm-button="groupOptions.showConfirmButton !== false"
    :cancel-button-text="groupOptions.cancelButtonText || '取消'"
    :confirm-button-text="groupOptions.confirmButtonText || '确定'"
    @confirm="handleConfirm"
    @cancel="handleCancel"
    destroy-on-close
    resize
    show-zoom
  >
    <div class="group-form-dialog-container">
      <vxe-collapse class="form-collapse" v-model ="activeNames" @change="handleCollapseChange">
        <vxe-collapse-pane
          v-for="(item, index) in groupOptions.items"
          :key="item.id || index"
          :name="`${index}`"
          :class="{ 'custom-pane-content': item.maxHeight || item.minHeight }"
        >
          <template #title>
            <div class="pane-title">{{ item.title }}</div>
          </template>

          <template #default>
            <div
              class="pane-content-wrapper"
              :style="{
                maxHeight: item.maxHeight,
                minHeight: item.minHeight,
                overflow: item.maxHeight ? 'auto' : 'visible'
              }"
            >
              <bm-form v-if="item.type === 'form'" :form-options="item.formOptions" :ref="el => setFormRef(el, index)" />
              <bm-grid v-else-if="item.type === 'grid'" :grid-options="item.gridOptions" :ref="el => setGridRef(el, index)" />
            </div>
          </template>
        </vxe-collapse-pane>
      </vxe-collapse>

      <div v-if="groupOptions.tools && groupOptions.tools.length > 0" class="tools-container">
        <template v-for="(tool, toolIndex) in groupOptions.tools" :key="toolIndex">
          <div
            class="tool-item"
            :class="[`tool-${tool.position || 'left'}`]"
            :style="{
              width: tool.width || 'auto',
              marginLeft: tool.marign?.left || '0',
              marginRight: tool.marign?.right || '0',
              marginTop: tool.marign?.top || '0',
              marginBottom: tool.marign?.bottom || '0'
            }"
          >
            <component
              :is="componentMap.get(tool.name)"
              v-bind="tool.props"
              v-on="getToolEvents(tool)"
            />
          </div>
        </template>
      </div>
    </div>
  </vxe-modal>
</template>

<script lang="tsx" setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { VxeModal, VxeIcon, VxeTag, VxeButton, VxeSelect, VxeInput, VxeSwitch, VxeButtonGroup, VxeCheckbox, VxeImage, VxeImageGroup, VxeRadio, VxeTextarea, VxeRadioGroup, VxeText, VxeDatePicker, VxeTreeSelect, VxeUpload, VxeCollapse, VxeCollapsePane } from 'vxe-pc-ui'
import BmForm from './BmForm.vue'
import BmGrid from './BmGrid.vue'
import { GroupFormDialogOptions, ToolItem } from '@/types/group-form-dialog'


interface Props {
  groupOptions: GroupFormDialogOptions
}

const props = withDefaults(defineProps<Props>(), {
  groupOptions: () => ({
    visible: false,
    title: '',
    items: []
  })
})

// 定义确认事件的数据类型
interface ConfirmEventData {
  forms: Record<string, any>;
  grids: Record<string, any>;
  utils: GroupFormDialogUtils;
}

const emit = defineEmits<{
  (e: 'confirm', data: GroupFormDialogUtils | ConfirmEventData): void
  (e: 'cancel'): void
  (e: 'update:groupOptions', options: GroupFormDialogOptions): void
}>()

// 对话框可见性
const dialogVisible = computed({
  get: () => props.groupOptions.visible,
  set: (value) => {
    const newOptions = { ...props.groupOptions, visible: value }
    emit('update:groupOptions', newOptions)
  }
})

// 格式化宽度，支持像素值和百分比
const formattedWidth = computed(() => {
  const width = props.groupOptions.width
  if (!width) return '800px' // 默认宽度

  // 如果已经是字符串且包含单位（px 或 %），直接返回
  if (typeof width === 'string' && (width.includes('px') || width.includes('%'))) {
    return width
  }

  // 如果是数字或不带单位的字符串，添加 px 单位
  return `${width}px`
})

// 格式化高度，支持像素值和百分比
const formattedHeight = computed(() => {
  const height = props.groupOptions.height
  if (!height) return undefined // 默认高度（由组件自动计算）

  // 如果已经是字符串且包含单位（px 或 %），直接返回
  if (typeof height === 'string' && (height.includes('px') || height.includes('%'))) {
    return height
  }

  // 如果是数字或不带单位的字符串，添加 px 单位
  return `${height}px`
})

// 计算当前展开的面板名称数组
const activeNames = computed(() => {
  return props.groupOptions.items
    .map((item, index) => item.expanded ? `${index}` : null)
    .filter(name => name !== null)
})

// const activeNames = ref(['0'])

// 表单和表格引用
const formRefs = reactive(new Map())
const gridRefs = reactive(new Map())

// 设置表单引用
const setFormRef = (el: any, index: number) => {
  if (el) {
    const item = props.groupOptions.items[index]
    // 使用id作为键，如果没有id则使用索引
    const key = item.id || index
    formRefs.set(key, el)
  }
}

// 设置表格引用
const setGridRef = (el: any, index: number) => {
  if (el) {
    const item = props.groupOptions.items[index]
    // 使用id作为键，如果没有id则使用索引
    const key = item.id || index
    gridRefs.set(key, el)
  }
}

// 处理折叠面板变化事件
const handleCollapseChange = (names: any) => {
  // 确保 names 是数组
  const namesArray = Array.isArray(names) ? names : [names].filter(Boolean);

  // 更新每个项的展开状态
  const newItems = props.groupOptions.items.map((item, index) => {
    return {
      ...item,
      expanded: namesArray.includes(`${index}`)
    }
  })

  const newOptions = {
    ...props.groupOptions,
    items: newItems
  }

  emit('update:groupOptions', newOptions)
}

// 切换分组展开/折叠状态（保留此方法以兼容现有代码）
// 注意：此方法已被 utils.toggleGroup 替代，但保留以兼容现有代码
// 这个方法可能会在模板中被使用，所以不要删除
function toggleGroup(index: number) {
  const newItems = [...props.groupOptions.items]
  newItems[index] = {
    ...newItems[index],
    expanded: !newItems[index].expanded
  }

  const newOptions = {
    ...props.groupOptions,
    items: newItems
  }

  emit('update:groupOptions', newOptions)
}

// 确认按钮处理
const handleConfirm = async () => {
  try {
    // 直接传递工具类实例，其中包含获取表单和表格数据的方法
    emit('confirm', utils);

    dialogVisible.value = false;
  } catch (error) {
    console.error('确认时发生错误:', error);
  }
}

// 取消按钮处理
const handleCancel = () => {
  emit('cancel')
  dialogVisible.value = false
}

// 这些函数已移至 GroupFormDialogUtils 工具类中

// 组件映射
const componentMap = new Map<string, any>([
  ['VxeTag', VxeTag],
  ['VxeButton', VxeButton],
  ['VxeSelect', VxeSelect],
  ['VxeInput', VxeInput],
  ['VxeSwitch', VxeSwitch],
  ['VxeButtonGroup', VxeButtonGroup],
  ['VxeIcon', VxeIcon],
  ['VxeCheckbox', VxeCheckbox],
  ['VxeImage', VxeImage],
  ['VxeImageGroup', VxeImageGroup],
  ['VxeRadio', VxeRadio],
  ['VxeTextarea', VxeTextarea],
  ['VxeRadioGroup', VxeRadioGroup],
  ['VxeText', VxeText],
  ['VxeDatePicker', VxeDatePicker],
  ['VxeTreeSelect', VxeTreeSelect],
  ['VxeUpload', VxeUpload]
])

// 获取工具事件
const getToolEvents = (tool: ToolItem) => {
  const events = {}

  if (tool.events) {
    Object.entries(tool.events).forEach(([eventName, handler]) => {
      if (handler) {
        events[eventName] = (...args: any[]) => {
          handler(utils.getAllFormData(), {
            formRefs,
            gridRefs,
            setVisiable: utils.setVisiable.bind(utils)
          }, ...args)
        }
      }
    })
  }

  return events
}

// 初始化组件
onMounted(() => {
  // 初始化每个组的展开状态
  if (props.groupOptions.items) {
    props.groupOptions.items.forEach((item, _index) => {
      if (item.expanded === undefined) {
        item.expanded = true // 默认展开
      }
    })
  }
})

// 创建工具类实例
class GroupFormDialogUtils {
  // 将私有属性改为公共属性，使其可以在外部访问
  public formRefs: Map<string | number, any>;
  public gridRefs: Map<string | number, any>;
  public dialogOptions: GroupFormDialogOptions;
  public updateOptions: (options: GroupFormDialogOptions) => void;
  private watchers: Map<string, Array<(newValue: any, oldValue: any) => void>> = new Map();

  constructor(
    formRefs: Map<string | number, any>,
    gridRefs: Map<string | number, any>,
    dialogOptions: GroupFormDialogOptions,
    updateOptions: (options: GroupFormDialogOptions) => void
  ) {
    this.formRefs = formRefs;
    this.gridRefs = gridRefs;
    this.dialogOptions = dialogOptions;
    this.updateOptions = (newOptions: GroupFormDialogOptions) => {
      const oldOptions = { ...this.dialogOptions };
      this.dialogOptions = newOptions;
      this.notifyWatchers('dialogOptions', newOptions, oldOptions);
      updateOptions(newOptions);
    };
  }

  // 通知监听器数据变化
  public notifyWatchers(type: string, newValue: any, oldValue: any) {
    const callbacks = this.watchers.get(type);
    if (callbacks && callbacks.length > 0) {
      callbacks.forEach(callback => {
        try {
          callback(newValue, oldValue);
        } catch (error) {
          console.error(`Error in ${type} watcher:`, error);
        }
      });
    }
  }

  // 获取所有表单数据
  public getAllFormData() {
    const allData = {};

    for (const [_key, formRef] of this.formRefs.entries()) {
      if (formRef && formRef.getFormData) {
        const data = formRef.getFormData();
        Object.assign(allData, data);
      }
    }

    // 通知表单数据变化的监听器
    this.notifyWatchers('formData', allData, null);

    return allData;
  }

  // 获取所有表格数据
  public getAllGridData() {
    const allData = {};

    for (const [key, gridRef] of this.gridRefs.entries()) {
      if (gridRef && gridRef.getTableInstance) {
        const tableInstance = gridRef.getTableInstance();
        if (tableInstance) {
          const data = tableInstance.getTableData().fullData;
          allData[`grid_${key}`] = data;
        }
      }
    }

    // 通知表格数据变化的监听器
    this.notifyWatchers('gridData', allData, null);

    return allData;
  }

  // 根据ID获取指定表单组件实例
  public getFormById(id: string | number) {
    if (this.formRefs.has(id)) {
      return this.formRefs.get(id);
    }
    return null;
  }

  // 根据ID获取指定表格组件实例
  public getGridById(id: string | number) {
    if (this.gridRefs.has(id)) {
      return this.gridRefs.get(id);
    }
    return null;
  }

  // 设置对话框可见性
  public setVisiable(visible: boolean) {
    const newOptions = {
      ...this.dialogOptions,
      visible: visible
    };
    this.updateOptions(newOptions);
  }

  // 设置对话框宽度
  public setWidth(width: string | number) {
    const newOptions = {
      ...this.dialogOptions,
      width: width
    };
    this.updateOptions(newOptions);
  }

  // 设置对话框高度
  public setHeight(height: string | number) {
    const newOptions = {
      ...this.dialogOptions,
      height: height
    };
    this.updateOptions(newOptions);
  }

  // 同时设置对话框宽度和高度
  public setSize(width: string | number, height: string | number) {
    const newOptions = {
      ...this.dialogOptions,
      width: width,
      height: height
    };
    this.updateOptions(newOptions);
  }

  // 设置指定面板的最大高度和最小高度
  public setPaneHeight(index: number, maxHeight: string, minHeight: string) {
    if (index >= 0 && index < this.dialogOptions.items.length) {
      // 创建新的 items 数组
      const newItems = [...this.dialogOptions.items];

      // 更新指定索引的面板高度
      newItems[index] = {
        ...newItems[index],
        maxHeight,
        minHeight
      };

      // 更新 dialogOptions
      const newOptions = {
        ...this.dialogOptions,
        items: newItems
      };

      this.updateOptions(newOptions);
    }
  }

  // 设置指定面板的展开状态
  public setPaneExpanded(index: number, expanded: boolean) {
    if (index >= 0 && index < this.dialogOptions.items.length) {
      // 创建新的 items 数组
      const newItems = [...this.dialogOptions.items];

      // 更新指定索引的面板展开状态
      newItems[index] = {
        ...newItems[index],
        expanded
      };

      // 更新 dialogOptions
      const newOptions = {
        ...this.dialogOptions,
        items: newItems
      };

      this.updateOptions(newOptions);
    }
  }

  // 切换分组展开/折叠状态
  public toggleGroup(index: number) {
    if (index >= 0 && index < this.dialogOptions.items.length) {
      const newItems = [...this.dialogOptions.items];
      newItems[index] = {
        ...newItems[index],
        expanded: !newItems[index].expanded
      };

      const newOptions = {
        ...this.dialogOptions,
        items: newItems
      };

      this.updateOptions(newOptions);
    }
  }

  // 获取当前对话框宽度
  public getWidth() {
    const width = this.dialogOptions.width;
    if (!width) return '800px'; // 默认宽度

    // 如果已经是字符串且包含单位（px 或 %），直接返回
    if (typeof width === 'string' && (width.includes('px') || width.includes('%'))) {
      return width;
    }

    // 如果是数字或不带单位的字符串，添加 px 单位
    return `${width}px`;
  }

  // 获取当前对话框高度
  public getHeight() {
    const height = this.dialogOptions.height;
    if (!height) return undefined; // 默认高度（由组件自动计算）

    // 如果已经是字符串且包含单位（px 或 %），直接返回
    if (typeof height === 'string' && (height.includes('px') || height.includes('%'))) {
      return height;
    }

    // 如果是数字或不带单位的字符串，添加 px 单位
    return `${height}px`;
  }

  // 执行自定义函数
  public executeCustomFunction(functionName: string, ...args: any[]) {
    if (typeof (this as any)[functionName] === 'function') {
      return (this as any)[functionName](...args);
    } else {
      console.error(`Function ${functionName} does not exist in GroupFormDialogUtils`);
      return null;
    }
  }

  // 批量执行自定义函数
  public executeBatchFunctions(functionCalls: Array<{name: string, args: any[]}>) {
    const results: Array<{name: string, result?: any, error?: string}> = [];

    for (const call of functionCalls) {
      if (typeof (this as any)[call.name] === 'function') {
        results.push({
          name: call.name,
          result: (this as any)[call.name](...call.args)
        });
      } else {
        console.error(`Function ${call.name} does not exist in GroupFormDialogUtils`);
        results.push({
          name: call.name,
          error: `Function ${call.name} does not exist`
        });
      }
    }

    return results;
  }

  // 添加自定义函数
  public addCustomFunction(name: string, fn: Function) {
    if (!(this as any)[name]) {
      (this as any)[name] = fn;
      return true;
    } else {
      console.error(`Function ${name} already exists in GroupFormDialogUtils`);
      return false;
    }
  }

  // 监听表单数据变化
  public watchFormData(callback: (newValue: any, oldValue: any) => void) {
    if (!this.watchers.has('formData')) {
      this.watchers.set('formData', []);
    }
    this.watchers.get('formData')?.push(callback);
    return this.getAllFormData(); // 返回当前表单数据
  }

  // 监听表格数据变化
  public watchGridData(callback: (newValue: any, oldValue: any) => void) {
    if (!this.watchers.has('gridData')) {
      this.watchers.set('gridData', []);
    }
    this.watchers.get('gridData')?.push(callback);
    return this.getAllGridData(); // 返回当前表格数据
  }

  // 监听对话框选项变化
  public watchDialogOptions(callback: (newValue: GroupFormDialogOptions, oldValue: GroupFormDialogOptions) => void) {
    if (!this.watchers.has('dialogOptions')) {
      this.watchers.set('dialogOptions', []);
    }
    this.watchers.get('dialogOptions')?.push(callback);
    return this.dialogOptions; // 返回当前对话框选项
  }

  // 移除监听器
  public unwatchAll() {
    this.watchers.clear();
  }

  // 移除特定类型的监听器
  public unwatch(type: string) {
    this.watchers.delete(type);
  }

  // 移除特定的监听器回调
  public unwatchCallback(type: string, callback: Function) {
    const callbacks = this.watchers.get(type);
    if (callbacks) {
      const index = callbacks.findIndex(cb => cb === callback);
      if (index !== -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  // 获取组件实例
  public getComponentInstance(id: string) {
    // 先尝试从表单引用中获取
    const formInstance = this.getFormById(id);
    if (formInstance) {
      return formInstance;
    }

    // 再尝试从表格引用中获取
    const gridInstance = this.getGridById(id);
    if (gridInstance) {
      return gridInstance;
    }

    // 如果都没找到，返回null
    return null;
  }

  // 获取组件值
  public getComponentValue(id: string, field?: string) {
    const instance = this.getComponentInstance(id);

    if (!instance) {
      console.error(`Component with id ${id} not found`);
      return null;
    }

    if (instance.getFormData) {
      // 如果是表单组件
      const formData = instance.getFormData();
      return field ? formData[field] : formData;
    } else if (instance.getTableInstance) {
      // 如果是表格组件
      const tableInstance = instance.getTableInstance();
      if (tableInstance) {
        return tableInstance.getTableData().fullData;
      }
    }

    return null;
  }

  // 设置组件值
  public setComponentValue(id: string, value: any, field?: string) {
    const instance = this.getComponentInstance(id);

    if (!instance) {
      console.error(`Component with id ${id} not found`);
      return false;
    }

    if (instance.setFormData) {
      // 如果是表单组件
      if (field) {
        // 如果指定了字段，只设置该字段的值
        const formData = instance.getFormData();
        formData[field] = value;
        instance.setFormData(formData);
      } else {
        // 否则设置整个表单的值
        instance.setFormData(value);
      }
      return true;
    } else if (instance.getTableInstance) {
      // 如果是表格组件
      const tableInstance = instance.getTableInstance();
      if (tableInstance && tableInstance.loadData) {
        tableInstance.loadData(value);
        return true;
      }
    }

    return false;
  }

  // 验证表单数据
  public async validateForm(id?: string) {
    if (id) {
      // 验证指定ID的表单
      const formInstance = this.getFormById(id);
      if (formInstance && formInstance.validate) {
        try {
          await formInstance.validate();
          return true;
        } catch (error) {
          console.error(`Form validation failed for form ${id}:`, error);
          return false;
        }
      } else {
        console.error(`Form with id ${id} not found or does not support validation`);
        return false;
      }
    } else {
      // 验证所有表单
      let allValid = true;

      for (const [formId, formRef] of this.formRefs.entries()) {
        if (formRef && formRef.validate) {
          try {
            await formRef.validate();
          } catch (error) {
            console.error(`Form validation failed for form ${formId}:`, error);
            allValid = false;
          }
        }
      }

      return allValid;
    }
  }

  // 重置表单数据
  public resetForm(id?: string) {
    if (id) {
      // 重置指定ID的表单
      const formInstance = this.getFormById(id);
      if (formInstance && formInstance.reset) {
        formInstance.reset();
        return true;
      } else {
        console.error(`Form with id ${id} not found or does not support reset`);
        return false;
      }
    } else {
      // 重置所有表单
      let allReset = true;

      for (const [formId, formRef] of this.formRefs.entries()) {
        if (formRef && formRef.reset) {
          try {
            formRef.reset();
          } catch (error) {
            console.error(`Form reset failed for form ${formId}:`, error);
            allReset = false;
          }
        }
      }

      return allReset;
    }
  }

}

// 创建工具类实例
const utils = new GroupFormDialogUtils(
  formRefs,
  gridRefs,
  props.groupOptions,
  (newOptions) => emit('update:groupOptions', newOptions)
);

// 暴露方法
defineExpose({
  // 使用工具类中的方法
  setVisiable: utils.setVisiable.bind(utils),
  setWidth: utils.setWidth.bind(utils),
  setHeight: utils.setHeight.bind(utils),
  setSize: utils.setSize.bind(utils),
  setPaneHeight: utils.setPaneHeight.bind(utils),
  setPaneExpanded: utils.setPaneExpanded.bind(utils),
  getAllFormData: utils.getAllFormData.bind(utils),
  getAllGridData: utils.getAllGridData.bind(utils),
  getFormById: utils.getFormById.bind(utils),
  getGridById: utils.getGridById.bind(utils),
  // 保留对原始引用的访问
  formRefs,
  gridRefs,
  // 添加获取当前宽度和高度的方法
  getWidth: utils.getWidth.bind(utils),
  getHeight: utils.getHeight.bind(utils),
  // 添加工具类实例
  utils
})
</script>

<style scoped>
.group-form-dialog-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  height: 100%;
  overflow-y: auto;
}

.form-collapse {
  width: 100%;
  height: 100%;
  overflow: auto;
}

.pane-title {
  font-weight: 500;
  font-size: 16px;
  color: #333;
}

:deep(.vxe-collapse-pane) {
  margin-bottom: 10px;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #ebeef5;
}

:deep(.vxe-collapse-pane-header) {
  background-color: #f5f7fa;
  padding: 12px;
  border-bottom: 1px solid #ebeef5;
}

:deep(.vxe-collapse-pane-body) {
  padding: 16px;
}

:deep(.custom-pane-content .vxe-collapse-pane-body) {
  padding: 0;
}

.pane-content-wrapper {
  padding: 16px;
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
}

.tools-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 16px;
}

.tool-item {
  display: flex;
}

.tool-left {
  margin-right: auto;
}

.tool-center {
  margin-left: auto;
  margin-right: auto;
}

.tool-right {
  margin-left: auto;
}
</style>