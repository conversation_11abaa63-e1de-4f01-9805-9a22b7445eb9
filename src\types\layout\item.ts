export const gridItemProps = {
  isDraggable: {
    type: Boolean,
    default: null
  },
  isResizable: {
    type: Boolean,
    default: null
  },
  isBounded: {
    type: Boolean,
    default: null
  },
  static: {
    type: Boolean,
    default: false
  },
  minH: {
    type: Number,
    default: 1
  },
  minW: {
    type: Number,
    default: 1
  },
  maxH: {
    type: Number,
    default: Infinity
  },
  maxW: {
    type: Number,
    default: Infinity
  },
  x: {
    type: Number,
    required: true
  },
  y: {
    type: Number,
    required: true
  },
  w: {
    type: Number,
    required: true
  },
  h: {
    type: Number,
    required: true
  },
  dragIgnoreFrom: {
    type: String,
    default: 'a, button'
  },
  dragAllowFrom: {
    type: String,
    default: null
  },
  resizeIgnoreFrom: {
    type: String,
    default: 'a, button'
  }
}

export const gridItemEmits = [
  'resize',
  'resized',
  'move',
  'moved',
  'container-resized'
]
