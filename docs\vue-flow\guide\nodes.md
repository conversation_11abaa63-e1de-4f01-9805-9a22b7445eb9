Introduction to Nodes ​
=======================

Nodes are the underlying components of your graph. They can be any kind of data you want to visualize in your graph, existing independently and being interconnected through edges to create a data map.

Remember, every node is unique and thus **requires a unique id** and **an XY\-position**.

For the full list of options available for a node, check out the Node Interface.

Adding Nodes to the Graph ​
---------------------------

Nodes are rendered by passing them to the `nodes` prop (or the deprecated `v-model` prop) of the Vue Flow component.

WARNING

This method will *not* create a change. Check out the Controlled Flow section for more information.

vue
```

<script setup>
import { ref, onMounted } from 'vue'
import { VueFlow, Panel } from '@vue-flow/core'

const nodes = ref([
  {
    id: '1',
    position: { x: 50, y: 50 },
    data: { label: 'Node 1', },
  }
]);

function addNode {
  const id = Date.now.toString
  
  nodes.value.push({
    id,
    position: { x: 150, y: 50 },
    data: { label: `Node ${id}`, },
  })
}
</script>

<template>
  <VueFlow :nodes="nodes">
    <Panel>
      <button type="button" @click="addNode">Add a node</button>
    </Panel>
  </VueFlow>
</template>
```
vue
```

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import type { Node } from '@vue-flow/core'
import { VueFlow, Panel } from '@vue-flow/core'

const nodes = ref<Node[]>([
  {
    id: '1',
    position: { x: 50, y: 50 },
    data: { label: 'Node 1', },
  }
]);

function addNode {
  const id = Date.now.toString

  nodes.value.push({
    id,
    position: { x: 150, y: 50 },
    data: { label: `Node ${id}`, },
  })
}
</script>

<template>
  <VueFlow :nodes="nodes">
    <Panel>
      <button type="button" @click="addNode">Add a node</button>
    </Panel>
  </VueFlow>
</template>
```
If you are working with more complex graphs or simply require access to the internal state, the useVueFlow composable will come in handy.

The `addNodes` action is available through useVueFlow, allowing you to add nodes straight to the state.

What's more, this action isn't limited to the component rendering the graph; it can be utilized elsewhere, like in a Sidebar or Toolbar.

vue
```
<script setup>
import { ref } from 'vue'
import { Panel, VueFlow, useVueFlow } from '@vue-flow/core'

const initialNodes = ref([
  {
    id: '1',
    position: { x: 50, y: 50 },
    data: { label: 'Node 1' },
  }
])
const { addNodes } = useVueFlow

function generateRandomNode {
  return {
    id: Math.random.toString,
    position: { x: Math.random * 500, y: Math.random * 500 },
    label: 'Random Node',
  }
}

function onAddNode {
  // add a single node to the graph
  addNodes(generateRandomNode)
}

function onAddNodes {
  // add multiple nodes to the graph
  addNodes(Array.from({ length: 10 }, generateRandomNode))
}
</script>

<template>
  <VueFlow :nodes="initialNodes">
    <Panel>
      <button type="button" @click="onAddNode">Add a node</button>
      <button type="button" @click="onAddNodes">Add multiple nodes</button>
    </Panel>
  </VueFlow>
</template>
```
vue
```
<script setup lang="ts">
import { ref } from 'vue'  
import type { Node } from '@vue-flow/core'  
import { Panel, VueFlow, useVueFlow } from '@vue-flow/core'

const initialNodes = ref<Node[]>([
  {
    id: '1',
    position: { x: 50, y: 50 },
    data: { label: 'Node 1' },
  }
])
const { addNodes } = useVueFlow

function generateRandomNode {
  return {
    id: Math.random.toString,
    position: { x: Math.random * 500, y: Math.random * 500 },
    label: 'Random Node',
    data: { 
      hello: 'world',
    }
  }
}

function onAddNode {
  // add a single node to the graph
  addNodes(generateRandomNode)
}

function onAddNodes {
  // add multiple nodes to the graph
  addNodes(Array.from({ length: 10 }, generateRandomNode))
}
</script>

<template>
  <VueFlow :nodes="initialNodes">
    <Panel>
      <button type="button" @click="onAddNode">Add a node</button>
      <button type="button" @click="onAddNodes">Add multiple nodes</button>
    </Panel>
  </VueFlow>
</template>
```
INFO

If you want to have two\-way binding of nodes, use `v-model:nodes="nodes"`. This will allow you to mutate *your* nodes and have the changes reflected in the graph.

Removing Nodes from the Graph ​
-------------------------------

Similar to adding nodes, nodes can be removed from the graph by removing them from the `mode-value` (using `v-model`) or from the `nodes` prop of the Vue Flow component.

vue
```
<script setup>
import { ref } from 'vue'
import { VueFlow, Panel } from '@vue-flow/core'

const nodes = ref([
  {
    id: '1',
    position: { x: 50, y: 50 },
    data: { label: 'Node 1' },
  },
  {
    id: '2',
    position: { x: 150, y: 50 },
    data: { label: 'Node 2' },
  }
])

function removeNode(id) {
  nodes.value = nodes.value.filter((node) => node.id !== id)
}
</script>

<template>
  <VueFlow :nodes="nodes">
    <Panel>
      <button type="button" @click="removeNode('1')">Remove Node 1</button>
      <button type="button" @click="removeNode('2')">Remove Node 2</button>
    </Panel>
  </VueFlow>
</template>
```
The `removeNodes` action is available through useVueFlow, allowing you to remove nodes straight from the state.

You can also use this action outside the component rendering the graph, like in a Sidebar or Toolbar.

vue
```
<script setup>
import { ref } from 'vue'
import { VueFlow, Panel, useVueFlow } from '@vue-flow/core'

const initialNodes = ref([
  {
    id: '1',
    position: { x: 50, y: 50 },
    data: { label: 'Node 1' },
  },
  {
    id: '2',
    position: { x: 150, y: 50 },
    data: { label: 'Node 2' },
  }
])

const { removeNodes } = useVueFlow

function removeOneNode {
  removeNodes('1')
}

function removeMultipleNodes {
  removeNodes(['1', '2'])
}
</script>

<template>
  <VueFlow :nodes="initialNodes">
    <Panel>
      <button type="button" @click="removeOneNode">Remove Node 1</button>
      <button type="button" @click="removeMultipleNodes">Remove Node 1 and 2</button>
    </Panel>
  </VueFlow>
</template>
```
Updating Node Data ​
--------------------

Since nodes are reactive object, you can update their data at any point by simply mutating it. This allows you to disable or enable handles, change the label, or even add new properties to the data object at any point in time.

There are multiple ways of achieving this, here are some examples:

useVueFlowuseNodev\-modelts
```
import  { useVueFlow } from '@vue-flow/core'

const instance = useVueFlow

// use the `updateNodeData` method to update the data of an edge
instance.updateNodeData(edgeId, { hello: 'mona' })

// find the node in the state by its id
const node = instance.findNode(nodeId)

node.data = {
  ...node.data,
  hello: 'world',
}

// you can also mutate properties like `selectable` or `draggable`
node.selectable = false
node.draggable = false

// or use `updateNode` to update the node directly
instance.updateNode(nodeId, { selectable: false, draggable: false })
```
vue
```
<!-- CustomNode.vue -->
<script setup>
import { useNode } from '@vue-flow/core'

// `useNode` returns us the node object straight from the state
// since the node obj is reactive, we can mutate it to update our nodes' data
const { node } = useNode

function onSomeEvent {
  node.data = {
    ...node.data,  
    hello: 'world',
  }
  
  // you can also mutate properties like `selectable` or `draggable`
  node.selectable = false
  node.draggable = false
}
</script>
```
vue
```
<script setup>
import { ref } from 'vue'

const nodes = ref([
  {
    id: '1',
    position: { x: 50, y: 50 },
    data: {
      label: 'Node 1',
      hello: 'world',
    }
  },
])

function onSomeEvent(nodeId) {
  const node = nodes.value.find((node) => node.id === nodeId)

  node.data = {
    ...nodes.value[0].data,
    hello: 'world',
  }
    
  // you can also mutate properties like `selectable` or `draggable`
  node.selectable = false
  node.draggable = false
}
</script>

<template>
  <VueFlow :nodes="nodes">
    <Panel>
      <button type="button" @click="onSomeEvent('1')">Update Node 1</button>
    </Panel>
  </VueFlow>
</template>
```
Predefined Node\-Types ​
------------------------

Vue Flow provides several built\-in node types that you can leverage immediately. The included node types are `default`, `input`, and `output`.

### Default Node ​

A default node includes two handles and serves as a branching junction in your map.

You have the freedom to determine the location of handles in the node's definition.

ts
```
import { ref } from 'vue'
import { Position } from '@vue-flow/core'

const nodes = ref([
  {
    id: '1',
    type: 'default', // You can omit this as it's the fallback type
    targetPosition: Position.Top, // or Bottom, Left, Right,
    sourcePosition: Position.Bottom, // or Top, Left, Right,
    data: { label: 'Default Node' },
  }
])
```
Default Node Press enter or space to select a node. You can then use the arrow keys to move the node around. You can then use the arrow keys to move the node around, press delete to remove it and press escape to cancel.  Press enter or space to select an edge. You can then press delete to remove it or press escape to cancel. ### Input Node ​

An input node features a single handle, which is by default positioned at the bottom. It represents a starting point of your map.

ts
```
import { ref } from 'vue'
import { Position } from '@vue-flow/core'

const nodes = ref([
  {
    id: '1',
    type: 'input',
    sourcePosition: Position.Bottom, // or Top, Left, Right,
    data: { label: 'Input Node' },
  }
])
```
Input Node Press enter or space to select a node. You can then use the arrow keys to move the node around. You can then use the arrow keys to move the node around, press delete to remove it and press escape to cancel.  Press enter or space to select an edge. You can then press delete to remove it or press escape to cancel. ### Output Node ​

An output node also possesses a single handle, although it is typically found at the top. This node represents a conclusion point of your map.

ts
```
import { ref } from 'vue'
import { Position } from '@vue-flow/core'

const nodes = ref([
  {
    id: '1',
    type: 'output',
    targetPosition: Position.Top, // or Bottom, Left, Right,
    data: { label: 'Output Node' },
  }
])
```
Output Node Press enter or space to select a node. You can then use the arrow keys to move the node around. You can then use the arrow keys to move the node around, press delete to remove it and press escape to cancel.  Press enter or space to select an edge. You can then press delete to remove it or press escape to cancel. User\-Defined Nodes ​
---------------------

On top of the default node types mentioned earlier, you can create as many custom node\-types as you need. Node\-types are determined from your nodes' definitions.

App.vue CustomNode.vue App.vue CustomNode.vue vue
```
<script setup>
import { ref } from 'vue'
import { VueFlow } from '@vue-flow/core'

import CustomNode from './CustomNode.vue'
import SpecialNode from './SpecialNode.vue'

export const nodes = ref([
  {
    id: '1',
    data: { label: 'Node 1' },
    // this will create the node-type `custom`
    type: 'custom',
    position: { x: 50, y: 50 },
  },
  {
    id: '1',
    data: { label: 'Node 1' },
    // this will create the node-type `special`
    type: 'special',
    position: { x: 150, y: 50 },
  }
])
</script>

<template>
  <VueFlow :nodes="nodes">
    <template #node-custom="customNodeProps">
      <CustomNode v-bind="customNodeProps" />
    </template>
    
    <template #node-special="specialNodeProps">
      <SpecialNode v-bind="specialNodeProps" />
    </template>
  </VueFlow>
</template>
```
vue
```
<script setup>
import { Position, Handle } from '@vue-flow/core'

// props were passed from the slot using `v-bind="customNodeProps"`
const props = defineProps(['label'])
</script>

<template>
  <div>
    <Handle type="target" :position="Position.Top" />
    <div>{{ label }}</div>
    <Handle type="source" :position="Position.Bottom" />
  </div>
</template>
```
vue
```
<script setup lang="ts">
import { ref } from 'vue'
import type { Node } from '@vue-flow/core'
import { VueFlow } from '@vue-flow/core'

import CustomNode from './CustomNode.vue'
import SpecialNode from './SpecialNode.vue'

// You can pass 3 optional generic arguments to the Node interface, allowing you to define:
// 1. The data object type
// 2. The events object type
// 3. The possible node types

export interface CustomData {
  hello: string
}

export interface CustomEvents {
  onCustomEvent: (event: MouseEvent) => void
}

type CustomNodeTypes = 'custom' | 'special'

type CustomNode = Node<CustomData, CustomEvents, CustomNodeTypes>

export const nodes = ref<CustomNode[]>([
  {
    id: '1',
    data: { label: 'Node 1' },
    // this will create the node-type `custom`
    type: 'custom',
    position: { x: 50, y: 50 },
  },
  {
    id: '2',
    data: { label: 'Node 2' },
    // this will create the node-type `special`
    type: 'special',
    position: { x: 150, y: 50 },
  },
    
  {
    id: '3', 
    data: { label: 'Node 3' },
    // this will throw a type error, as the type is not defined in the CustomEdgeTypes
    // regardless it would be rendered as a default edge type
    type: 'invalid',
    position: { x: 150, y: 50 },
  }
])
</script>

<template>
  <VueFlow :nodes="nodes">
    <template #node-custom="customNodeProps">
      <CustomNode v-bind="customNodeProps" />
    </template>
    
    <template #node-special="specialNodeProps">
      <SpecialNode v-bind="specialNodeProps" />
    </template>
  </VueFlow>
</template>
```
vue
```
<script setup lang="ts">
import type { NodeProps } from '@vue-flow/core'  
import { Position } from '@vue-flow/core'

import { CustomData, CustomEvents } from './nodes'

// props were passed from the slot using `v-bind="customNodeProps"`
const props = defineProps<NodeProps<CustomData, CustomEvents>>
  
console.log(props.data.hello) // 'world'
</script>

<template>
  <div>
    <Handle type="target" :position="Position.Top" />
    <div>{{ label }}</div>
    <Handle type="source" :position="Position.Bottom" />
  </div>
</template>
```
Vue Flow will then attempt to resolve this node\-type to a component. Priority is given to a definition in the nodeTypes object of the state. Next, it tries to match the component to a globally registered one with the same name. Finally, it searches for a provided template slot to fill in the node\-type.

If no methods produce a result in resolving the component, the default node\-type is used as a fallback.

### Template slots ​

One of the easiest ways to define custom nodes is, by passing them as template slots. Dynamic resolution to slot\-names is done for your user\-defined node\-types, meaning a node with the type `custom` is expected to have a slot named `#node-custom`.

vue
```
<script setup>
import { VueFlow } from '@vue-flow/core'
import CustomNode from './CustomNode.vue'

const nodes = ref([
  {
    id: '1',
    data: { label: 'Node 1' },
    type: 'custom',
    position: { x: 50, y: 50 },
  }
])
</script>

<template>
  <VueFlow :nodes="nodes">
    <!-- the expected slot name is `node-custom` -->
    <template #node-custom="props">
      <CustomNode v-bind="props" />
    </template>
  </VueFlow>
</template>
```
### Node\-types object ​

Alternatively, node\-types can also be defined by passing an object as a prop to the VueFlow component (or as an option to the composable).

WARNING

Take precaution to mark your components as raw (utilizing the marked function from the Vue library) to prevent their conversion into reactive objects. Otherwise, Vue will display a warning on the console.

vue
```
<script setup>
import { markRaw } from 'vue'
import CustomNode from './CustomNode.vue'
import SpecialNode from './SpecialNode.vue'

const nodeTypes = {
  custom: markRaw(CustomNode),
  special: markRaw(SpecialNode),
}

const nodes = ref([
  {
    id: '1',
    data: { label: 'Node 1' },
    type: 'custom',
  },
  {
    id: '1',
    data: { label: 'Node 1' },
    type: 'special',
  }
])
</script>

<template>
  <VueFlow :nodes="nodes" :nodeTypes="nodeTypes" />
</template>
```
TIP

You can find a working example here.

Node Props ​
------------

Your custom nodes are enclosed so that fundamental functions like dragging or selecting operate. But you may wish to expand on these features or implement your business logic inside nodes, thus your nodes receive the following properties:



| Prop Name | Description | Type | Optional |
| --- | --- | --- | --- |
| id | Unique node id | string |  |
| type | Node Type | string |  |
| selected | Is node selected | boolean |  |
| connectable | Can node handles be connected | HandleConnectable |  |
| position | Node's x, y (relative) position on the graph | XYPosition |  |
| dimensions | Dom element dimensions (width, height) | Dimensions |  |
| label | Node label, either a string or a VNode. `h('div', props, children)` | string \| VNode \| Component \| Object |  |
| isValidTargetPos deprecated | Called when used as target for new connection | ValidConnectionFunc |  |
| isValidSourcePos deprecated | Called when used as the source for a new connection | ValidConnectionFunc |  |
| parent | Parent node id | string |  |
| dragging | Is node currently dragging | boolean |  |
| resizing | Is node currently resizing | boolean |  |
| zIndex | Node z\-index | number |  |
| targetPosition | Handle position | Position |  |
| sourcePosition | Handle position | Position |  |
| dragHandle | Drag handle query selector | string |  |
| data | Additional data of node | any object |  |
| events | Contextual and custom events of node | NodeEventsOn |  |

Node Events ​
-------------

Vue Flow provides two main ways of listening to node events, either by using `useVueFlow` to bind listeners to the event handlers or by binding them to the `<VueFlow>` component.

useVueFlowcomponentvue
```
<script setup>
import { ref } from 'vue'  
import { VueFlow, useVueFlow } from '@vue-flow/core'

// useVueFlow provides access to the event handlers
const { 
  onNodeDragStart, 
  onNodeDrag,
  onNodeDragStop, 
  onNodeClick, 
  onNodeDoubleClick, 
  onNodeContextMenu, 
  onNodeMouseEnter, 
  onNodeMouseLeave, 
  onNodeMouseMove 
} = useVueFlow
  
const nodes = ref([
  {
    id: '1',
    data: { label: 'Node 1' },
    position: { x: 50, y: 50 },
  },
])
  
// bind listeners to the event handlers
onNodeDragStart((event) => {
  console.log('Node drag started', event)
})

onNodeDrag((event) => {
  console.log('Node dragged', event)
})

onNodeDragStop((event) => {
  console.log('Node drag stopped', event)
})
  
// ... and so on  
</script>

<template>
  <VueFlow :nodes="nodes" />
</template>
```
vue
```
<script setup>
import { ref } from 'vue'
import { VueFlow } from '@vue-flow/core'

const nodes = ref([
  {
    id: '1',
    data: { label: 'Node 1' },
    position: { x: 50, y: 50 },
  },
])
  
function logEvent(name, data) {
  console.log(name, data)
}
</script>

<template>
  <!-- bind listeners to the event handlers -->
  <VueFlow
    :nodes="nodes"
    @node-drag-start="logEvent('drag start', $event)"
    @node-drag="logEvent('drag', $event)"
    @node-drag-stop="logEvent('drag stop', $event)"
    @node-click="logEvent('click', $event)"
    @node-double-click="logEvent('dblclick', $event)"
    @node-contextmenu="logEvent('contextmenu', $event)"
    @node-mouse-enter="logEvent('mouseenter', $event)"
    @node-mouse-leave="logEvent('mouseleave', $event)"
    @node-mouse-move="logEvent('mousemove', $event)"
  />
</template>
```
Default NodeInteract to see events in browser console

 Press enter or space to select a node. You can then use the arrow keys to move the node around. You can then use the arrow keys to move the node around, press delete to remove it and press escape to cancel.  Press enter or space to select an edge. You can then press delete to remove it or press escape to cancel. Customizing Appearance ​
------------------------

TIP

To override the styles of the default theme, visit the Theming section.

### User\-Defined Nodes ​

When constructing a new node type, it's necessary for you to add some styling specific to it. User\-created nodes don't have any default styles associated and thus need custom styling.

css
```
.vue-flow__node-custom {
    background: #9CA8B3;
    color: #fff;
    padding: 10px;
}
```
Implementing Scrolling within Nodes ​
-------------------------------------

Sometimes, a node might contain a large amount of content, making it difficult for users to view everything without the aid of a scroll function. To facilitate this scrolling ability without invoking zoom or pan behaviors on the node, Vue Flow provides the `noWheelClassName` property.

The `noWheelClassName` property allows you to specify a class name that, when applied to a node, will disable the default zoom\-on\-scroll or pan\-on\-scroll events on that particular node.

By default, the `noWheelClassName` is `nowheel`.

vue
```
<script setup>
import { ref } from 'vue'

const listItems = ref(Array.from({ length: 100 }, (_, i) => i))  
</script>

<template>
  <div class="custom-node-container">
    <ul class="nowheel">
      <li v-for="item in listItems" :key="item">Item {{ item }}</li>
    </ul>
  </div>
</template>
```
* Item 0
* Item 1
* Item 2
* Item 3
* Item 4
* Item 5
* Item 6
* Item 7
* Item 8
* Item 9
* Item 10
* Item 11
* Item 12
* Item 13
* Item 14
* Item 15
* Item 16
* Item 17
* Item 18
* Item 19
* Item 20
* Item 21
* Item 22
* Item 23
* Item 24
* Item 25
* Item 26
* Item 27
* Item 28
* Item 29
* Item 30
* Item 31
* Item 32
* Item 33
* Item 34
* Item 35
* Item 36
* Item 37
* Item 38
* Item 39
* Item 40
* Item 41
* Item 42
* Item 43
* Item 44
* Item 45
* Item 46
* Item 47
* Item 48
* Item 49
* Item 50
* Item 51
* Item 52
* Item 53
* Item 54
* Item 55
* Item 56
* Item 57
* Item 58
* Item 59
* Item 60
* Item 61
* Item 62
* Item 63
* Item 64
* Item 65
* Item 66
* Item 67
* Item 68
* Item 69
* Item 70
* Item 71
* Item 72
* Item 73
* Item 74
* Item 75
* Item 76
* Item 77
* Item 78
* Item 79
* Item 80
* Item 81
* Item 82
* Item 83
* Item 84
* Item 85
* Item 86
* Item 87
* Item 88
* Item 89
* Item 90
* Item 91
* Item 92
* Item 93
* Item 94
* Item 95
* Item 96
* Item 97
* Item 98
* Item 99
 Press enter or space to select a node. You can then use the arrow keys to move the node around. You can then use the arrow keys to move the node around, press delete to remove it and press escape to cancel.  Press enter or space to select an edge. You can then press delete to remove it or press escape to cancel. Preventing Drag Behavior withing Nodes ​
----------------------------------------

There are certain scenarios where you might need to interact with the contents of a node without triggering a drag action on the node itself. This can be particularly useful when nodes contain interactive elements like input boxes, buttons, or sliders that you want your users to engage with.

To accomplish this, Vue Flow provides a `noDragClassName` property. This property allows specification of a class name, which when applied to an element within a node, prevents triggering a drag action on the node when the user interacts with that element.

By default, the `noDragClassName` is set as `nodrag`.

vue
```
<script setup>
import { ref } from 'vue'

const inputValue = ref('')
</script>

<template>
  <div class="custom-node-container">
    <input class="nodrag" v-model="inputValue" />
  </div>
</template>
```
 Press enter or space to select a node. You can then use the arrow keys to move the node around. You can then use the arrow keys to move the node around, press delete to remove it and press escape to cancel.  Press enter or space to select an edge. You can then press delete to remove it or press escape to cancel. PagerPrevious pageThemingNext pageEdges
