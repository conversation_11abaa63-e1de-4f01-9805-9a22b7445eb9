<template>
  <div class="bm-flow-container" :style="containerStyle">
    <!-- 工具栏 -->
    <div v-if="config.toolbar?.show" class="bm-flow-toolbar" :class="`toolbar-${config.toolbar.position || 'top'}`">
      <template v-for="(tool, index) in config.toolbar.tools" :key="index">
        <div v-if="tool.type === 'separator'" class="toolbar-separator"></div>
        <button
          v-else-if="tool.type === 'button' || !tool.type"
          class="toolbar-button"
          :disabled="tool.disabled"
          @click="handleToolClick(tool)"
        >
          <i v-if="tool.icon" :class="tool.icon"></i>
          <span v-if="tool.label">{{ tool.label }}</span>
        </button>
      </template>
    </div>

    <!-- Vue Flow 主体 -->
    <div class="bm-flow-main" ref="flowContainer">
      <VueFlow
        ref="vueFlowRef"
        v-model:nodes="nodes"
        v-model:edges="edges"
        :node-types="nodeTypes"
        :edge-types="edgeTypes"
        :zoom-on-scroll="options.zoomOnScroll"
        :pan-on-scroll="options.panOnScroll"
        :nodes-draggable="options.nodesDraggable"
        :nodes-connectable="options.nodesConnectable"
        :elements-selectable="options.elementsSelectable"
        :min-zoom="options.minZoom"
        :max-zoom="options.maxZoom"
        :default-zoom="options.defaultZoom"
        :fit-view-on-init="options.fitView"
        :connection-line-type="options.connectionLineType"
        :connection-line-style="options.connectionLineStyle"
        :snap-to-grid="options.snapToGrid"
        :snap-grid="options.snapGrid"
        @node-click="handleNodeClick"
        @node-double-click="handleNodeDoubleClick"
        @node-drag-start="handleNodeDragStart"
        @node-drag="handleNodeDrag"
        @node-drag-stop="handleNodeDragStop"
        @edge-click="handleEdgeClick"
        @connect="handleConnect"
        @connect-start="handleConnectStart"
        @connect-end="handleConnectEnd"
        @elements-remove="handleElementsRemove"
        @pane-click="handlePaneClick"
        @pane-context-menu="handlePaneContextMenu"
      >
        <!-- 背景 -->
        <Background
          v-if="options.showBackground"
          :pattern="options.backgroundType"
          :pattern-color="options.backgroundColor"
        />

        <!-- 控制面板 -->
        <Controls v-if="options.showControls" />

        <!-- 小地图 -->
        <MiniMap v-if="options.showMinimap" />
      </VueFlow>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, h } from 'vue'
import { VueFlow, useVueFlow, Position, Handle, ConnectionLineType } from '@vue-flow/core'
import { Background } from '@vue-flow/background'
import { Controls } from '@vue-flow/controls'
import { MiniMap } from '@vue-flow/minimap'
import type {
  BmFlowConfig,
  BmFlowNode,
  BmFlowEdge,
  BmFlowOptions,
  BmFlowEvents,
  BmFlowInstance,
  BmFlowToolItem
} from '../types/bm-flow'
import BmForm from './BmForm.vue'

// 引入样式
import '@vue-flow/core/dist/style.css'
import '@vue-flow/core/dist/theme-default.css'

interface Props {
  config: BmFlowConfig
  events?: BmFlowEvents
  style?: Record<string, any>
  class?: string
  readonly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  config: () => ({
    nodes: [],
    edges: [],
    options: {},
    toolbar: { show: false }
  }),
  readonly: false
})

const emit = defineEmits<{
  (e: 'update:config', config: BmFlowConfig): void
  (e: 'nodeClick', event: any, node: BmFlowNode): void
  (e: 'nodeDoubleClick', event: any, node: BmFlowNode): void
  (e: 'edgeClick', event: any, edge: BmFlowEdge): void
  (e: 'connect', connection: any): void
}>()

// Vue Flow 实例引用
const vueFlowRef = ref()
const flowContainer = ref()

// 获取 Vue Flow 实例
const { fitView, zoomTo } = useVueFlow()

// 响应式数据
const nodes = ref<BmFlowNode[]>(props.config.nodes || [])
const edges = ref<BmFlowEdge[]>(props.config.edges || [])

// 默认配置选项
const defaultOptions: BmFlowOptions = {
  showBackground: true,
  backgroundType: 'dots',
  backgroundColor: '#f0f0f0',
  showControls: true,
  showMinimap: false,
  zoomOnScroll: true,
  panOnScroll: false,
  nodesDraggable: true,
  nodesConnectable: true,
  elementsSelectable: true,
  minZoom: 0.1,
  maxZoom: 2,
  defaultZoom: 1,
  fitView: true,
  connectionLineType: ConnectionLineType.Bezier,
  snapToGrid: false,
  snapGrid: [15, 15]
}

// 合并配置选项
const options = computed<BmFlowOptions>(() => ({
  ...defaultOptions,
  ...props.config.options
}))

// 容器样式
const containerStyle = computed(() => ({
  width: '100%',
  height: '100%',
  position: 'relative' as const,
  ...props.style
}))

// BmForm 节点组件
const BmFormNode = {
  props: ['id', 'data', 'position'],
  setup(props: any) {
    return () => h('div', {
      class: 'bm-form-node',
      style: {
        background: 'white',
        border: '1px solid #ddd',
        borderRadius: '8px',
        padding: '16px',
        minWidth: '300px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      }
    }, [
      // 节点标题
      h('div', {
        class: 'node-header',
        style: {
          marginBottom: '12px',
          fontWeight: 'bold',
          fontSize: '14px',
          color: '#333'
        }
      }, props.data.label || '表单节点'),

      // BmForm 组件
      props.data.formConfig ? h(BmForm, {
        formOptions: props.data.formConfig
      }) : h('div', { style: { color: '#999' } }, '请配置表单'),

      // 连接点
      h(Handle, {
        type: 'target',
        position: Position.Top,
        style: { background: '#555' }
      }),
      h(Handle, {
        type: 'source',
        position: Position.Bottom,
        style: { background: '#555' }
      })
    ])
  }
}

// 自定义节点类型
const nodeTypes = computed(() => ({
  bmForm: BmFormNode
}))

// 自定义边类型
const edgeTypes = computed(() => ({}))

// 事件处理函数
const handleNodeClick = (event: any) => {
  const node = event.node as BmFlowNode
  emit('nodeClick', event, node)
  props.events?.onNodeClick?.(event, node)
}

const handleNodeDoubleClick = (event: any) => {
  const node = event.node as BmFlowNode
  emit('nodeDoubleClick', event, node)
  props.events?.onNodeDoubleClick?.(event, node)
}

const handleNodeDragStart = (event: any) => {
  const node = event.node as BmFlowNode
  props.events?.onNodeDragStart?.(event, node)
}

const handleNodeDrag = (event: any) => {
  const node = event.node as BmFlowNode
  props.events?.onNodeDrag?.(event, node)
}

const handleNodeDragStop = (event: any) => {
  const node = event.node as BmFlowNode
  props.events?.onNodeDragStop?.(event, node)
}

const handleEdgeClick = (event: any) => {
  const edge = event.edge as BmFlowEdge
  emit('edgeClick', event, edge)
  props.events?.onEdgeClick?.(event, edge)
}

const handleConnect = (connection: any) => {
  emit('connect', connection)
  props.events?.onConnect?.(connection)
}

const handleConnectStart = (event: any) => {
  props.events?.onConnectStart?.(event, event)
}

const handleConnectEnd = (event: any) => {
  props.events?.onConnectEnd?.(event)
}

const handleElementsRemove = (elements: any[]) => {
  props.events?.onElementsRemove?.(elements)
}

const handlePaneClick = (event: any) => {
  props.events?.onPaneClick?.(event)
}

const handlePaneContextMenu = (event: any) => {
  props.events?.onPaneContextMenu?.(event)
}

// 监听配置变化
watch(() => props.config.nodes, (newNodes) => {
  if (newNodes) {
    nodes.value = [...newNodes]
  }
}, { deep: true })

watch(() => props.config.edges, (newEdges) => {
  if (newEdges) {
    edges.value = [...newEdges]
  }
}, { deep: true })

// 组件实例方法
const bmFlowInstance: BmFlowInstance = {
  getNodes: () => nodes.value,
  getEdges: () => edges.value,
  addNode: (node: BmFlowNode) => {
    nodes.value.push(node)
  },
  removeNode: (nodeId: string) => {
    const index = nodes.value.findIndex(n => n.id === nodeId)
    if (index > -1) {
      nodes.value.splice(index, 1)
    }
  },
  updateNode: (nodeId: string, updates: Partial<BmFlowNode>) => {
    const node = nodes.value.find(n => n.id === nodeId)
    if (node) {
      Object.assign(node, updates)
    }
  },
  addEdge: (edge: BmFlowEdge) => {
    edges.value.push(edge)
  },
  removeEdge: (edgeId: string) => {
    const index = edges.value.findIndex(e => e.id === edgeId)
    if (index > -1) {
      edges.value.splice(index, 1)
    }
  },
  updateEdge: (edgeId: string, updates: Partial<BmFlowEdge>) => {
    const edge = edges.value.find(e => e.id === edgeId)
    if (edge) {
      Object.assign(edge, updates)
    }
  },
  fitView,
  zoomTo,
  getZoom: () => 1, // 临时实现
  getViewport: () => ({ x: 0, y: 0, zoom: 1 }), // 临时实现
  setViewport: () => {}, // 临时实现
  toObject: () => ({ nodes: nodes.value, edges: edges.value })
}

// 工具栏点击处理
const handleToolClick = (tool: BmFlowToolItem) => {
  tool.onClick?.(bmFlowInstance)
}

// 暴露组件实例
defineExpose(bmFlowInstance)

onMounted(() => {
  // 组件挂载后的初始化逻辑
  if (options.value.fitView) {
    setTimeout(() => {
      fitView()
    }, 100)
  }
})
</script>

<style scoped>
.bm-flow-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  background: #f5f5f5;
}

.bm-flow-toolbar {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: white;
  border-bottom: 1px solid #e0e0e0;
  z-index: 10;
}

.toolbar-top {
  order: 1;
}

.toolbar-bottom {
  order: 3;
}

.toolbar-left {
  flex-direction: column;
  width: 60px;
  height: 100%;
  border-right: 1px solid #e0e0e0;
  border-bottom: none;
}

.toolbar-right {
  flex-direction: column;
  width: 60px;
  height: 100%;
  border-left: 1px solid #e0e0e0;
  border-bottom: none;
}

.bm-flow-main {
  flex: 1;
  order: 2;
  position: relative;
}

.toolbar-button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border: 1px solid #d0d0d0;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
}

.toolbar-button:hover:not(:disabled) {
  background: #f0f0f0;
  border-color: #b0b0b0;
}

.toolbar-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.toolbar-separator {
  width: 1px;
  height: 20px;
  background: #e0e0e0;
}

.bm-form-node {
  font-family: inherit;
}
</style>